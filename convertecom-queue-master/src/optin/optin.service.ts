import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Optin } from './optin.entity';
import { Tenant } from '../tenant/tenant.entity';

export interface EmailResult {
  email: string;
}

@Injectable()
export class OptinService {
  constructor(
    @InjectRepository(Optin)
    private readonly optinRepository: Repository<Optin>,
  ) {}

  // Note that this will grab 50 at a time
  async getUniqueUnsyncedEmailsForTenant(
    tenant: Tenant,
  ): Promise<EmailResult[]> {
    return await this.optinRepository.query(
      `SELECT DISTINCT ON (lower("email")) lower("email") as "email" FROM "optin" WHERE "tenantId" = $1 and "email" != '' and "createdAt" >= $2 and "syncedAt" is null LIMIT 50`,
      [tenant.id, tenant.emailPlatformCredentials.startDate],
    );
  }

  async flagEmailsSynced(emails: string[], tenant: Tenant) {
    return await this.optinRepository
      .createQueryBuilder()
      .update(Optin)
      .set({
        syncedAt: () => 'CURRENT_TIMESTAMP',
      })
      .where('lower(email) IN (:...emails)', { emails })
      .andWhere('"tenantId" = :tenantId', { tenantId: tenant.id })
      .execute();
  }

  async find(optin: Optin): Promise<Optin> {
    return await this.optinRepository.findOne(optin.id);
  }
}
