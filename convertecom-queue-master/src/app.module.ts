import { Module } from '@nestjs/common';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { parse } from 'pg-connection-string';
import { AppController } from './app.controller';
import { CouponModule } from './coupon/coupon.module';
import { TenantModule } from './tenant/tenant.module';
import { ShopifyModule } from './shopify/shopify.module';
import { ApiSyncQueueModule } from './api-sync-queue/api-sync-queue.module';
import { OrderModule } from './order/order.module';
import { OptinModule } from './optin/optin.module';
import { KlaviyoModule } from './klaviyo/klaviyo.module';
import { MailchimpModule } from './mailchimp/mailchimp.module';
import { BillingModule } from './billing/billing.module';
import { SharedModule } from './shared/shared.module';
import { ScheduleModule } from '@nestjs/schedule';

let connectionConfig;

if (process.env.DATABASE_URL) {
  const connection = parse(process.env.DATABASE_URL);

  connectionConfig = {
    type: process.env.TYPEORM_CONNECTION,
    host: connection.host,
    port: connection.port,
    username: connection.user,
    password: connection.password,
    database: connection.database,
    entities: [process.env.TYPEORM_ENTITIES],
    synchronize: false,
    extra: {
      /*
       rejectUnauthorized explicitly needed here because of updates in node 12.22.5, see node changelog for deets
       https://github.com/nodejs/node/blob/master/doc/changelogs/CHANGELOG_V12.md#2021-08-11-version-12225-erbium-lts-bethgriggs
       */
      ssl:
        typeof process.env.TYPEORM_SSL === 'undefined' || process.env.TYPEORM_SSL === 'true'
          ? {  rejectUnauthorized: false }
          : false,
    },
  };
}

@Module({
  imports: [
    TypeOrmModule.forRoot(connectionConfig),
    ApiSyncQueueModule,
    CouponModule,
    TenantModule,
    ShopifyModule,
    OrderModule,
    OptinModule,
    KlaviyoModule,
    MailchimpModule,
    BillingModule,
    SharedModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [AppController],
  providers: [],
})
export class AppModule {}
