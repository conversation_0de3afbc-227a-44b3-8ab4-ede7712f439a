import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiSyncQueue } from './api-sync-queue.entity';
import { ApiSyncQueueService } from './api-sync-queue.service';
import { TenantModule } from '../tenant/tenant.module';
import { CouponModule } from '../coupon/coupon.module';
import { OptinModule } from '../optin/optin.module';
import { KlaviyoModule } from '../klaviyo/klaviyo.module';
import { MailchimpModule } from '../mailchimp/mailchimp.module';
import { BillingModule } from '../billing/billing.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ApiSyncQueue]),
    CouponModule,
    TenantModule,
    OptinModule,
    KlaviyoModule,
    MailchimpModule,
    BillingModule,
  ],
  providers: [ApiSyncQueueService],
  exports: [ApiSyncQueueService],
})
export class ApiSyncQueueModule {}
