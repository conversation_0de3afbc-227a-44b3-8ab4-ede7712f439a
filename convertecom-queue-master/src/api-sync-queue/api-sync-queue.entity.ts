import {
  Entity,
  PrimaryGeneratedColumn,
  CreateDate<PERSON><PERSON>umn,
  Join<PERSON><PERSON>umn,
  Column,
  ManyToOne,
} from 'typeorm';
import { Coupon } from '../coupon/coupon.entity';
import { Tenant } from '../tenant/tenant.entity';
import { Optin } from '../optin/optin.entity';

export enum ApiSyncQueueType {
  CREATE_COUPON = 'create-coupon',
  UPDATE_COUPON = 'update-coupon',
  IMPORT_ALL_ORDERS = 'import-all-orders',
  UPDATE_MIN_GROSS = 'update-min-gross',
  OPTIN = 'optin',
  CREATE_FALLBACK_COUPON_TO_ROTATE = 'create-fallback-coupon-to-rotate',
  ROTATE_FALLBACK_COUPON = 'rotate-fallback-coupon',
}

@Entity()
export class ApiSyncQueue {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ default: ApiSyncQueueType.CREATE_COUPON })
  type: ApiSyncQueueType;

  @ManyToOne(type => Tenant, { eager: true })
  @JoinColumn()
  tenant: Tenant;

  @ManyToOne(type => Coupon, { eager: true })
  @JoinColumn()
  coupon: Coupon;

  @ManyToOne(type => Optin, { eager: true })
  @JoinColumn()
  optin: Optin;

  @CreateDateColumn()
  createdAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  startedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  finishedAt: Date;

  @Column({ default: false })
  permanentFailure: boolean;
}
