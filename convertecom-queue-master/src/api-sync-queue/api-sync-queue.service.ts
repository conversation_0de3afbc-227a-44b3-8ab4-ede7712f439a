import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Repository } from 'typeorm';
import { ApiSyncQueue, ApiSyncQueueType } from './api-sync-queue.entity';
import { CouponService } from '../coupon/coupon.service';
import { TenantService } from '../tenant/tenant.service';
import { Tenant } from '../tenant/tenant.entity';
import { ShopifyService } from '../shopify/shopify.service';
import { QueuePermanentFailureException } from '../lib/exceptions/QueuePermanentFailureException';
import { OptinService } from '../optin/optin.service';
import { KlaviyoService } from '../klaviyo/klaviyo.service';
import { MailchimpService } from '../mailchimp/mailchimp.service';
import { BillingService } from '../billing/billing.service';
import { FallbackCouponService } from '../coupon/fallback-coupon.service';

@Injectable()
export class ApiSyncQueueService {
  constructor(
    @InjectRepository(ApiSyncQueue)
    private readonly apiSyncQueueRepository: Repository<ApiSyncQueue>,
    private readonly couponService: CouponService,
    private readonly tenantService: TenantService,
    private readonly optinService: OptinService,
    private readonly klaviyoService: KlaviyoService,
    private readonly mailchimpService: MailchimpService,
    private readonly billingService: BillingService,
    private readonly fallbackCouponService: FallbackCouponService,
  ) { }

  async find(): Promise<ApiSyncQueue[]> {
    return await this.apiSyncQueueRepository.find();
  }

  async getTenantQueueBatch(tenant: Tenant): Promise<ApiSyncQueue[]> {
    return await this.apiSyncQueueRepository.find({
      where: {
        tenant,
        finishedAt: IsNull(),
        permanentFailure: false,
      },
      order: {
        createdAt: 'ASC',
      },
      take: tenant.queueBatchSize,
    });
  }

  private async setStarted(queuedJob: ApiSyncQueue) {
    return await this.apiSyncQueueRepository.update(queuedJob.id, {
      startedAt: () => 'NOW()',
    });
  }

  private async setFinished(queuedJob: ApiSyncQueue, permanentFailure = false) {
    return await this.apiSyncQueueRepository.update(queuedJob.id, {
      finishedAt: () => 'NOW()',
      permanentFailure: !!permanentFailure,
    });
  }

  private async timedWrap(queuedJob: ApiSyncQueue, promise: Promise<any>) {
    await this.setStarted(queuedJob);

    try {
      await promise;
      await this.setFinished(queuedJob);
    } catch (e) {
      if (e instanceof QueuePermanentFailureException) {
        Logger.error(e);
        await this.setFinished(queuedJob, true);
      } else {
        // Don't flag finished; let it try again
        Logger.error(e);
      }
    }
  }

  async createJobsForDisabledCoupons(tenant: Tenant) {
    const coupons = await this.couponService.getDisabledCouponsByTenant(tenant);
    await this.apiSyncQueueRepository.insert(
      coupons.map(coupon => {
        return { coupon, tenant, type: ApiSyncQueueType.UPDATE_COUPON };
      }),
    );
  }

  async syncOptinsWithEmailProviders(tenant: Tenant): Promise<void> {
    if (!tenant.emailPlatformCredentials || !tenant.emailPlatformCredentials.platform) {
      return;
    }

    // Confirm required values are set
    if (
      !tenant.emailPlatformCredentials.apiKey ||
      !tenant.emailPlatformCredentials.listId
    ) {
      Logger.warn(`Tenant ${tenant.id} is trying to sync emails but does not have` +
        `an api key or list id set`);
      return;
    }

    // Get emails that haven't been synced
    const emails = await this.optinService.getUniqueUnsyncedEmailsForTenant(
      tenant,
    );

    if (emails.length === 0) {
      return;
    }

    let syncedEmails: string[] = [];

    switch (tenant.emailPlatformCredentials.platform) {
      case 'klaviyo':
        syncedEmails = await this.klaviyoService.addEmailsToList(
          emails,
          tenant,
        );
        break;
      case 'mailchimp':
        syncedEmails = await this.mailchimpService.addEmailsToList(
          emails,
          tenant,
        );
        break;
    }

    if (syncedEmails.length > 0) {
      await this.optinService.flagEmailsSynced(syncedEmails, tenant);
      Logger.log(
        `Added ${syncedEmails.length} emails to email list for tenant ${tenant.id
        }`,
      );
    }
  }

  async processPendingQueueBatch() {
    const tenants = await this.tenantService.findAllShopifyTenants();

    for (const tenant of tenants) {
      const couponPromises = [];

      try {
        const queuedJobBatch = await this.getTenantQueueBatch(tenant);

        const shopifyService = new ShopifyService();
        await shopifyService.init(tenant);

        for (const queuedJob of queuedJobBatch) {
          switch (queuedJob.type) {
            case ApiSyncQueueType.CREATE_COUPON:
              await shopifyService.init(tenant);
              // Pending coupon needs to be added to Shopify
              couponPromises.push(
                this.timedWrap(
                  queuedJob,
                  this.couponService.processPendingShopifyCoupon(
                    queuedJob.coupon,
                    shopifyService,
                    tenant,
                  ),
                ),
              );
              break;
            case ApiSyncQueueType.UPDATE_COUPON:
              // Existing coupon needs to be updated
              await shopifyService.init(tenant);
              couponPromises.push(
                this.timedWrap(
                  queuedJob,
                  this.couponService.processExistingShopifyCoupon(
                    queuedJob.coupon,
                    shopifyService,
                    tenant,
                  ),
                ),
              );
              break;
            case ApiSyncQueueType.UPDATE_MIN_GROSS:
              // Tenant minimum gross percentage has been updated, so all disabled coupons need to be re-synced
              // So create a job for each coupon that needs to be updated
              await this.timedWrap(
                queuedJob,
                this.createJobsForDisabledCoupons(queuedJob.tenant),
              );
              break;
            case ApiSyncQueueType.IMPORT_ALL_ORDERS:
              // This will only happen when a tenant is first being set up, so just resolve the promise now
              await this.timedWrap(
                queuedJob,
                this.tenantService.processPendingShopifyTenant(
                  queuedJob.tenant,
                  shopifyService,
                ),
              );
              break;
            case ApiSyncQueueType.OPTIN:
              // Create job to send tenant email to shopify admin to create new customer
              const customer = await this.optinService.find(queuedJob.optin);
              const checkUser = new Promise(async (resolve, reject) => {
                try {
                  const res = await shopifyService.createCustomer(customer);
                  resolve(res);
                } catch (e) {
                  console.log(e)
                  reject(new QueuePermanentFailureException(e));
                }
              });
              await this.timedWrap(queuedJob, checkUser);
              break;
            case ApiSyncQueueType.CREATE_FALLBACK_COUPON_TO_ROTATE: {
              await this.timedWrap(
                queuedJob,
                this.createFallbackCouponToRotate(queuedJob, tenant),
              );
              break;
            }
            case ApiSyncQueueType.ROTATE_FALLBACK_COUPON:
              await this.timedWrap(
                queuedJob,
                this.rotateFallbackCoupon(queuedJob, shopifyService, tenant),
              );
              break;
          }
        }

        // Await all store API promises
        await Promise.all(couponPromises);

        if (tenant.billingCredentials && tenant.billingCredentials.externalId) {
          await this.billingService.processPendingBillingForTenant(
            tenant,
            shopifyService,
          );
        }
        // The process of sending new optin emails to email platforms should be run every time rather than explicitly queued
        await this.syncOptinsWithEmailProviders(tenant);
      } catch (e) {
        Logger.error(e, e.stack);
      }
    }
  }

  async processPendingForever() {
    console.log('Starting infinite queue processing loop');

    // Use a true infinite loop
    while (true) {
      try {
        console.log('Processing pending queue batch...');
        await this.processPendingQueueBatch();
        console.log('Batch processed successfully');
      } catch (e) {
        console.error('Error processing queue batch:', e);
        // Don't let errors break our infinite loop
      }

      // Use a proper promise-based delay
      console.log('Waiting before next batch...');
      await this.delay(1);
    }
  }

  async delay(seconds: number) {
    console.log(`Delaying for ${seconds} seconds...`);
    return new Promise(resolve => {
      setTimeout(() => {
        console.log('Delay completed');
        resolve(true);
      }, seconds * 1000);
    });
  }

  async createFallbackCouponToRotate(job: ApiSyncQueue, tenant: Tenant) {
    await this.fallbackCouponService.queueFallbackCouponRotation(tenant);
  }

  async rotateFallbackCoupon(
    job: ApiSyncQueue,
    shopifyService: ShopifyService,
    tenant: Tenant,
  ) {
    Logger.log(`Rotating coupon for ${tenant.id}`);
    await this.fallbackCouponService.handleFallbackCouponRotation(
      job.coupon,
      tenant.id,
      shopifyService,
    );
  }
}
