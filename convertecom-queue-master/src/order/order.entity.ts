import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
} from 'typeorm';
import { Coupon } from '../coupon/coupon.entity';

@Entity()
export class Order {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  externalOrderId: string;

  @ManyToMany(type => Coupon, coupon => coupon.redemptionOrders)
  redeemedCoupons: Coupon[];

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
  })
  totalPrice: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
  })
  subtotalPrice: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
  })
  totalTax: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
  })
  totalDiscounts: number;

  // Differentiate convertEcom coupon discounts from others
  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
  })
  convertEcomDiscounts: number;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  externalCreatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  externalPaymentAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
