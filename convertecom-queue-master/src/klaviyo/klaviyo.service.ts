import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { Tenant } from '../tenant/tenant.entity';
import { EmailResult } from '../optin/optin.service';

@Injectable()
export class KlaviyoService {
  client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      timeout: 5000,
      baseURL: 'https://a.klaviyo.com/api/',
      headers: { 'Content-Type': 'application/json' },
    })
  }

  async addEmailsToList(emails: EmailResult[], tenant: Tenant): Promise<string[]> {
    try {
      const endpoint = `/v2/list/${tenant.emailPlatformCredentials.listId}/members`;
      const params = {
        api_key: tenant.emailPlatformCredentials.apiKey,
        profiles: emails.map(o => {
          return { email: o.email, source: 'convertecom' };
        }),
      };
      const { data } = await this.client.post(endpoint, params);
      return data.map(o => o.email);
    } catch (e) {
      const response = [];

      Logger.error('Klaviyo call failed');
      Logger.error(e);
      if (e.response && e.response.data) {
        Logger.error(e.response.data);
        if (e.response.data.detail && e.response.data.detail.toString().includes(' is not a valid email.')) {
          // Batch included an invalid email, so just flag it as synced
          response.push(e.response.data.detail.toString().replace(' is not a valid email.', ''));
        }
      }
      return response;
    }
  }
}
