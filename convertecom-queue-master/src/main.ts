import { config } from 'dotenv';
config();

import { NestFactory } from '@nestjs/core';
import * as Sentry from '@sentry/node';
import { RavenInterceptor } from 'nest-raven';
import { HttpException } from '@nestjs/common';
import { AppModule } from './app.module';
import { ApiSyncQueueService } from './api-sync-queue/api-sync-queue.service';
import { NestExpressApplication } from '@nestjs/platform-express';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);


  if (process.env.SENTRY_DSN) {
    Sentry.init({ dsn: process.env.SENTRY_DSN });
    app.use(Sentry.Handlers.requestHandler());
    app.useGlobalInterceptors(new RavenInterceptor({
      filters: [
        { type: HttpException, filter: (exception: HttpException) => 500 > exception.getStatus() }
      ],
    }));
  }

  await app.listen(8000);
  console.log("server started")
  const queueService = app.get(ApiSyncQueueService);
  await queueService.processPendingForever();
}
bootstrap();
