import { Entity, PrimaryGeneratedColumn, ManyToOne, Column, CreateDateColumn, UpdateDateColumn, OneToOne, JoinColumn, Index } from 'typeorm';
import { Tenant } from '../tenant/tenant.entity';
import { BillingLog } from './billing-log.entity';

@Entity()
@Index(["tenantId", "date"], { unique: true })
export class Billing {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // So we can make it part of the compound unique index at the top of this class
  @Column()
  tenantId: string;

  @ManyToOne(type => Tenant, tenant => tenant.billing)
  tenant: Tenant;

  @Column()
  date: string;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
  })
  amount: number;

  // In case the percentage changes at some point, we have a record of what it was
  @Column({
    type: 'decimal',
    precision: 3,
    scale: 2,
  })
  percentage: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
  })
  subtotalSum: number;

  @OneToOne(type => BillingLog, { cascade: true })
  @JoinColumn()
  billingLog: BillingLog;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}