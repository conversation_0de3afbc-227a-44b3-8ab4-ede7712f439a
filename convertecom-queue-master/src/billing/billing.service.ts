import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Repository, MoreThan } from 'typeorm';
import { Tenant } from '../tenant/tenant.entity';
import { BillingLog } from './billing-log.entity';
import { Billing } from './billing.entity';
import { ShopifyService } from '../shopify/shopify.service';

@Injectable()
export class BillingService {
  constructor(
    @InjectRepository(Billing)
    private readonly billingRepository: Repository<Billing>,
  ) {}

  // Realistically each tenant will only add one usage record per day,
  // but we're adding 'limit 1' just to prevent us from killing the rate limit if a tenant needs to run billing in bulk for some reason
  async processPendingBillingForTenant(tenant: Tenant, shopifyService: ShopifyService) {
    const billing = await this.billingRepository.findOne({ where: { tenant, amount: MoreThan(0), billingLog: IsNull() } });

    if (!billing) {
      return;
    }
    
    const recurringPaymentId = parseInt(tenant.billingCredentials.externalId);
    const description = `Usage charge for ${billing.date}`;
    const price = billing.amount;

    billing.billingLog = new BillingLog();

    // Save it as 'pending' so there's no possibility of double-billing if the API call takes forever
    billing.billingLog.status = 'pending';
    billing.billingLog.rawResponse = '';
    await this.billingRepository.save(billing);

    try {
      const response = await shopifyService.createUsageCharge(recurringPaymentId, description, price);
      billing.billingLog.rawResponse = JSON.stringify(response);
      billing.billingLog.status = 'success';
    } catch (e) {
      console.log(e);
      Logger.error(`Failed to bill tenant ${tenant.externalId}`);
      Logger.error(e);
      billing.billingLog.rawResponse = JSON.stringify(e);
      billing.billingLog.status = 'error';
    }

    await this.billingRepository.save(billing);
  }
}
