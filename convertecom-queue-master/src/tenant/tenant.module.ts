import { Module } from '@nestjs/common';
import { TenantService } from './tenant.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Tenant } from './tenant.entity';
import { OrderModule } from '../order/order.module';
import { SharedModule } from '../shared/shared.module';

@Module({
  imports: [TypeOrmModule.forFeature([Tenant]), OrderModule, SharedModule],
  providers: [TenantService],
  exports: [TenantService],
})
export class TenantModule {}
