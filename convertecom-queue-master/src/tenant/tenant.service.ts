import { Injectable, Inject, Logger } from '@nestjs/common';
import { Tenant } from './tenant.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThan, Not, IsNull } from 'typeorm';
import { find } from 'lodash';
import { ShopifyService } from '../shopify/shopify.service';
import { OrderService } from '../order/order.service';
import { MAIL_SERVICE, SendGrid } from '../shared/sendgrid.provider';
import { IWebhook } from 'shopify-api-node';

@Injectable()
export class TenantService {
  private readonly logger = new Logger(TenantService.name, false);
  constructor(
    @Inject(MAIL_SERVICE) private readonly mail: SendGrid,
    @InjectRepository(Tenant)
    private readonly tenantRepository: Repository<Tenant>,
    private readonly orderService: OrderService,
  ) { }

  async findAllShopifyTenants() {
    return await this.tenantRepository.find({
      where: {
        platform: 'shopify',
        queueBatchSize: MoreThan(0),
        apiCredentials: Not(IsNull()),
      },
    });
  }

  async processPendingShopifyTenant(
    tenant: Tenant,
    shopifyService: ShopifyService,
  ) {
    // Pull the current webhooks to see if we need to create any
    let webhooks: IWebhook[] = [];
    try {
      webhooks = await shopifyService.getWebhooks();
    } catch (e) {
      this.logger.error(`Error getting webhooks for tenant ${tenant.id} - ${tenant.externalId}`);
    }

    Logger.debug(`Existing webhooks:`);
    Logger.debug(webhooks);

    // If the webhook for "orders/create" isn't set, create it
    const orderCreatedAddress = `${process.env.WEBHOOK_BASE_URL
      }/webhook/shopify/order-created`;
    if (
      !find(webhooks, { topic: 'orders/create', address: orderCreatedAddress })
    ) {
      await shopifyService.createWebhook({
        topic: 'orders/create',
        address: orderCreatedAddress,
      });
      Logger.log(`Created orders/create webhook for tenant ${tenant.id}`);
    }

    // If the webhook for "orders/paid" isn't set, create it
    const orderPaidAddress = `${process.env.WEBHOOK_BASE_URL
      }/webhook/shopify/order-paid`;
    if (!find(webhooks, { topic: 'orders/paid', address: orderPaidAddress })) {
      await shopifyService.createWebhook({
        topic: 'orders/paid',
        address: orderPaidAddress,
      });
      Logger.log(`Created orders/paid webhook for tenant ${tenant.id}`);
    }

    // If the webhook for "app/uninstalled" isn't set, create it
    const uninstallAddress = `${process.env.WEBHOOK_BASE_URL
      }/webhook/shopify/uninstall`;
    if (
      !find(webhooks, { topic: 'app/uninstalled', address: uninstallAddress })
    ) {
      await shopifyService.createWebhook({
        topic: 'app/uninstalled',
        address: uninstallAddress,
      });
      Logger.log(`Created app/uninstalled webhook for tenant ${tenant.id}`);
    }

    // Set up script tags
    const convertEcomScriptTagsCount = await shopifyService.countConvertEcomScriptTags();
    if (convertEcomScriptTagsCount === 0) {
      await shopifyService.createScriptTags();
    }

    try {
      const {
        name,
        email,
        domain,
        customer_email,
      } = await shopifyService.getShopDetails();
      let formattedMessage =
        `A new tenant installed the ConvertEcom app:\n` +
        `Store Name: ${name}\n` +
        `Email: ${email}\n` +
        `Domain: ${domain}\n` +
        `Customer Email: ${customer_email}`;

      await this.mail.send({
        to: process.env.CONVERTECOM_EMAIL,
        from: '<EMAIL>',
        subject: 'New Tenant Installation',
        text: formattedMessage,
        html: formattedMessage.split('\n').join('<br/>'),
      });
    } catch (err) {
      console.error('email notification did not go through ', err);
    }

    // TODO get as many orders as possible
    const shopifyOrders = [];
    for (const shopifyOrder of shopifyOrders) {
      // TODO bulk insert orders
      await this.orderService.create({
        // TODO map fields
      });
    }
  }
}
