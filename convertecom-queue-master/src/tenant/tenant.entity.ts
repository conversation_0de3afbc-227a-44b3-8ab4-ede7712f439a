import {
  <PERSON><PERSON><PERSON>,
  <PERSON>um<PERSON>,
  PrimaryGeneratedColumn,
  OneToMany,
  UpdateDateColumn,
  CreateDateColumn, <PERSON>in<PERSON><PERSON>umn, OneToOne,
} from 'typeorm';
import { ApiCredentials } from './interfaces/api-credentials.interface';
import { BillingCredentials } from './interfaces/billing-credentials.interface';
import { EmailPlatformCredentials } from './interfaces/email-platform-credentials.interface';
import { Coupon } from '../coupon/coupon.entity';
import { Billing } from '../billing/billing.entity';
import { TenantFeatureFlags } from './interfaces/tenant-feature-flags.interface';

@Entity()
export class Tenant {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('varchar')
  platform: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalId: string | number;

  @Column('varchar')
  name: string;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 5,
    default: 0.05,
  })
  couponPercentage: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 5,
    default: 0.8,
  })
  minimumGrossPercentage: number;

  @Column({
    default: 10,
  })
  reservationMinutes: number;

  @Column({
    type: 'json',
    nullable: true,
  })
  apiCredentials: ApiCredentials;

  @Column({
    type: 'json',
    nullable: true,
  })
  emailPlatformCredentials: EmailPlatformCredentials;

  @Column({
    type: 'json',
    nullable: true,
  })
  billingCredentials: BillingCredentials;

  @Column({
    type: 'integer',
    default: 1,
  })
  queueBatchSize: number;

  @Column({
    type: 'json',
    default: {},
  })
  featureFlags: TenantFeatureFlags;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(type => Coupon, coupons => coupons.tenant)
  coupons: Coupon[];

  @OneToMany(type => Billing, billing => billing.tenant)
  billing: Billing[];

  @OneToOne(type => Coupon, { eager: true })
  @JoinColumn()
  fallbackCoupon: Coupon;
}
