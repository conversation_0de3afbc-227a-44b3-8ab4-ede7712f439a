import { Test, TestingModule } from '@nestjs/testing';
import { RotateFallbackCouponJob } from './rotate-fallback-coupon.job';

describe('RotateFallbackCouponService', () => {
  let service: RotateFallbackCouponJob;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [RotateFallbackCouponJob],
    }).compile();

    service = module.get<RotateFallbackCouponJob>(RotateFallbackCouponJob);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
