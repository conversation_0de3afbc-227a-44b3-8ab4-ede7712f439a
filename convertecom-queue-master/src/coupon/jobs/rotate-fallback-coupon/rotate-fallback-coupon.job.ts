import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression, Timeout } from '@nestjs/schedule';
import { Tenant } from '../../../tenant/tenant.entity';
import { Repository } from 'typeorm';
import { FallbackCouponService } from '../../fallback-coupon.service';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class RotateFallbackCouponJob {
  private readonly logger = new Logger(RotateFallbackCouponJob.name);

  constructor(
    private readonly fallbackCouponService: FallbackCouponService,
    @InjectRepository(Tenant) readonly tenantRepository: Repository<Tenant>,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async schedule() {
    this.logger.log('Rotating fallback coupons via cron');
    await this.handle();
  }

  /**
   * Run the job. We are only processing tenants who have the feature flag enabled
   * at the moment.
   */
  async handle() {
    const tenantCount = await this.baseQuery().getCount();
    this.logger.log(`Processing ${tenantCount} tenants`);
    const pageSize = 10;
    let offset = 0;
    do {
      const tenants = await this.baseQuery()
        .orderBy('tenant.name')
        .skip(offset)
        .take(pageSize)
        .getMany();

      for (const tenant of tenants) {
        await this.fallbackCouponService.queueFallbackCouponRotation(tenant);
      }

      offset += pageSize;
    } while (offset + pageSize < tenantCount);
  }

  private baseQuery() {
    return this.tenantRepository
      .createQueryBuilder('tenant')
      .innerJoinAndSelect('tenant.fallbackCoupon', 'fallbackCoupon');
    // Uncomment to handle only for flagged tenants
    /* .where('tenant.featureFlags ::jsonb @> :featureFlags', {
       featureFlags: {
         couponsRotateFallback: true,
       },
     });*/
  }
}
