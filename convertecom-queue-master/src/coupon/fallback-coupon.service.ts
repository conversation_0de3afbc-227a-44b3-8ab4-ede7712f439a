import { Injectable, Logger } from '@nestjs/common';
import { Connection } from 'typeorm';
import { Coupon } from './coupon.entity';
import { addDays } from 'date-fns';
import { Tenant } from '../tenant/tenant.entity';
import { QueuePermanentFailureException } from '../lib/exceptions/QueuePermanentFailureException';
import { CouponService } from './coupon.service';
import {
  ApiSyncQueue,
  ApiSyncQueueType,
} from '../api-sync-queue/api-sync-queue.entity';
import { RotateFallbackCouponFailedException } from '../lib/exceptions/RotateFallbackCouponFailedException';
import { ShopifyService } from '../shopify/shopify.service';
import { find } from 'lodash';
import { PlatformData, PlatformDataKey } from '../platform-data/platform-data.entity';
import * as moment from 'moment';

@Injectable()
export class FallbackCouponService {
  constructor(
    private readonly connection: Connection,
    private readonly couponService: CouponService,
  ) {}

  /**
   * Create a new fallback coupon and queue it to be created in Shopify.
   * @param tenant
   * @throws RotateFallbackCouponFailedException
   */
  async queueFallbackCouponRotation(tenant: Tenant) {
    try {
      await this.connection.transaction('READ COMMITTED', async em => {
        const newFallbackCoupon = {
          tenant,
          isFallback: true,
          couponAmount: 0,
          percentage: tenant.fallbackCoupon
            ? tenant.fallbackCoupon.percentage
            : 0.05,
          code: await this.couponService.generateCode(),
        };

        const newCoupon = await em.save(Coupon, newFallbackCoupon as Coupon);
        await em.save(
          ApiSyncQueue,
          this.createRotateCouponJob(newCoupon, tenant),
        );
        Logger.log(
          `Created new pending fallback coupon for ${tenant.name} (${
            tenant.id
          })`,
        );
      });
      // TODO: Remove expired coupons from Shopify
    } catch (e) {
      const message = `Failed to rotate fallback coupon for ${tenant.name} (${
        tenant.id
      })`;
      Logger.error(message);
      Logger.debug(e);
      throw new RotateFallbackCouponFailedException(message);
    }
  }

  /**
   * Attempt to add the new fallback coupon to Shopify, set the expiry of the current
   * fallback coupon to some point in the future, and make the tenant use the new
   * fallback copuon.
   * @param pendingFallbackCoupon
   * @param tenantId
   * @param shopifyService
   * @throws QueuePermanentFailureException
   */
  async handleFallbackCouponRotation(pendingFallbackCoupon: Coupon, tenantId: string, shopifyService: ShopifyService) {
    try {
      await this.connection.transaction('READ COMMITTED', async em => {
        // Reselect so we can update the tenant in the transaction
        const tenant: Tenant = await em
          .createQueryBuilder(Tenant, 'tenant')
          .innerJoinAndSelect('tenant.fallbackCoupon', 'fallbackCoupon')
          .where('tenant.id = :id', { id: tenantId })
          .getOne();
        if (!tenant || !tenant.fallbackCoupon) {
          throw new QueuePermanentFailureException(
            `Tenant ${
              tenant.id
            } doesn't exist or doesn't have a fallback coupon.`,
          );
        }
        const newFallbackCoupon = await this.couponService.processPendingShopifyCoupon(
          pendingFallbackCoupon,
          shopifyService,
          tenant,
        );
        const { fallbackCoupon: existingFallbackCoupon } = tenant;
        existingFallbackCoupon.expiresAt = addDays(new Date(), 3);
        tenant.fallbackCoupon = newFallbackCoupon;
        await em.save(Coupon, [newFallbackCoupon, existingFallbackCoupon]);
        await em.save(Tenant, tenant);

        const priceRule = find<PlatformData>(newFallbackCoupon.platformData, {
          key: PlatformDataKey.SHOPIFY_PRICERULE_ID,
        });
        if (!priceRule || !priceRule.value) {
          Logger.error(`Tried to update coupon ${newFallbackCoupon.id} with missing price rule id`);
          throw new Error();
        }

        await shopifyService.updatePartialPriceRule(parseInt(priceRule.value, 10), {ends_at: moment(existingFallbackCoupon.expiresAt).toISOString()});
        Logger.log(`Updated Shopify ends_at for Price Rule: ${priceRule.id} Coupon: ${existingFallbackCoupon.id} Tenant: ${tenant.externalId}`);
      });
    } catch (e) {
      throw new QueuePermanentFailureException(
        `Failed to rotate fallback coupon ${
          pendingFallbackCoupon.id
        } for tenant ${tenantId}`,
      );
    }
  }

  async cleanupExpiredFallbackCoupons(tenant: Tenant, coupons: Coupon[]) {

  }

  private createRotateCouponJob(
    newCoupon: Coupon,
    tenant: Tenant,
  ): ApiSyncQueue {
    return this.connection.getRepository(ApiSyncQueue).create({
      coupon: newCoupon,
      tenant,
      type: ApiSyncQueueType.ROTATE_FALLBACK_COUPON,
    });
  }
}
