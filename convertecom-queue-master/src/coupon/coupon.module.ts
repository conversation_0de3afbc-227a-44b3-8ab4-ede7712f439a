import { Module } from '@nestjs/common';
import { CouponService } from './coupon.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Coupon } from './coupon.entity';
import { TenantModule } from '../tenant/tenant.module';
import { ShopifyModule } from '../shopify/shopify.module';
import { FallbackCouponService } from './fallback-coupon.service';
import { RotateFallbackCouponJob } from './jobs/rotate-fallback-coupon/rotate-fallback-coupon.job';
import { Tenant } from '../tenant/tenant.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Coupon, Tenant]), ShopifyModule, TenantModule],
  providers: [CouponService, FallbackCouponService, RotateFallbackCouponJob],
  exports: [CouponService, FallbackCouponService],
})
export class CouponModule {}
