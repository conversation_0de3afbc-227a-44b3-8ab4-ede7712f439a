import { Test, TestingModule } from '@nestjs/testing';
import { FallbackCouponService } from './fallback-coupon.service';

describe('FallbackCouponService', () => {
  let service: FallbackCouponService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FallbackCouponService],
    }).compile();

    service = module.get<FallbackCouponService>(FallbackCouponService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
