import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { get, find, sampleSize } from 'lodash';
import { Coupon } from './coupon.entity';
import { ShopifyService } from '../shopify/shopify.service';
import { QueuePermanentFailureException } from '../lib/exceptions/QueuePermanentFailureException';
import { PlatformDataKey } from '../platform-data/platform-data.entity';
import { Tenant } from '../tenant/tenant.entity';
import { floatEqual } from '../lib/math';

@Injectable()
export class CouponService {
  constructor(
    @InjectRepository(Coupon)
    private readonly couponRepository: Repository<Coupon>,
  ) { }

  async generateCode() {
    const availableCharacters =
      process.env.AVAILABLE_CODE_CHARACTERS || 'ABCDEFGHJKLMNPQRTUVWXY346789'; // TODO maybe this should be tenant-configurable?
    return 'CONVERTECOM_' + sampleSize(availableCharacters, 10).join('');
  }

  async processPendingShopifyCoupon(
    coupon: Coupon,
    shopifyService: ShopifyService,
    tenant: Tenant,
  ): Promise<Coupon> {
    try {
      coupon.minimumCartSubtotal = coupon.couponAmount / (1 - tenant.minimumGrossPercentage);

      const priceRule = await shopifyService.createPriceRule(coupon);
      const discountCode = await shopifyService.createDiscountCode(
        priceRule.id,
        coupon.code,
      );

      const platformData = [
        {
          key: PlatformDataKey.SHOPIFY_PRICERULE_ID,
          value: priceRule.id.toString(),
        },
        {
          key: PlatformDataKey.SHOPIFY_DISCOUNTCODE_ID,
          value: discountCode.id.toString(),
        },
      ];

      const couponRes = await this.couponRepository.save({
        ...coupon,
        platformData,
      });
      Logger.log(`Finalized pending coupon ${coupon.id}`);
      return couponRes;
    } catch (e) {
      if (
        e.statusCode === 422 &&
        get(e, 'response.body.errors.code[0]') ===
        'must be unique. Please try a different code.'
      ) {
        // This code somehow passed our uniqueness constraints but already exists in Shopify.
        // TODO confirm correct behavior here. Generating a new code is fine for now.
        Logger.warn(
          `Pending coupon ${coupon.id} has code "${coupon.code
          }", which already exists in Shopify. Auto-generating a new code and trying again.`,
        );
        coupon.code = await this.generateCode();
        await this.couponRepository.save(coupon);
        throw new Error(
          'Pending coupon code already exists in platform. Regenerated code and will retry on the next loop.',
        );
      } else {
        Logger.error({ ...e, responseBody: e.response ? e.response.body : null }, e.stack);
        // TODO see if there are any errors we don't want to flag as permanent failures
        throw new QueuePermanentFailureException(e);
      }
    }
  }

  async processExistingShopifyCoupon(
    coupon: Coupon,
    shopifyService,
    tenant: Tenant,
  ): Promise<Coupon> {
    const savedPriceRule = find(coupon.platformData, {
      key: PlatformDataKey.SHOPIFY_PRICERULE_ID,
    });

    if (!savedPriceRule) {
      throw new QueuePermanentFailureException(
        `Tried to update coupon ${coupon} with missing price rule id`,
      );
    }

    if (coupon.isDisabled) {
      const minimumCartSubtotal = coupon.couponAmount / (1 - tenant.minimumGrossPercentage);

      if (!floatEqual(minimumCartSubtotal, coupon.minimumCartSubtotal)) {
        coupon.minimumCartSubtotal = minimumCartSubtotal;
      }

      coupon.isDisabled = false;
      await this.couponRepository.save(coupon);
    }

    const priceRule = await shopifyService.updatePriceRule(
      savedPriceRule.value,
      coupon,
    );
    Logger.log(`Updated existing coupon ${coupon.id} with new values`);

    return coupon;
  }

  async getDisabledCouponsByTenant(tenant: Tenant): Promise<Coupon[]> {
    return await this.couponRepository.find({
      where: {
        tenant,
        isDisabled: true,
      }
    });
  }
}
