import { Logger } from '@nestjs/common';
import * as Shopify from 'shopify-api-node';
import {
  IDiscountCode,
  IPriceRule,
  IUsageCharge,
  IWebhook,
} from 'shopify-api-node';
import { Tenant } from '../tenant/tenant.entity';
import { ShopifyServiceException } from './exceptions/shopify.exception';
import { Coupon } from '../coupon/coupon.entity';
import { Optin } from '../optin/optin.entity';

const scriptTagSrc = `http://localhost:8080/zing-bundle.js`;

export class ShopifyService {
  client: Shopify;
  tenant: Tenant;

  async init(tenant: Tenant) {
    if (tenant.platform !== 'shopify') {
      throw new ShopifyServiceException(
        `Cannot init Shopify client on non-Shopify tenant ${tenant.id}`,
      );
    }
    if (
      !tenant.externalId ||
      !tenant.apiCredentials ||
      (!tenant.apiCredentials.accessToken &&
        (!tenant.apiCredentials.apiKey || !tenant.apiCredentials.password))
    ) {
      throw new ShopifyServiceException(
        `API credentials not set on tenant ${tenant.id}`,
      );
    }

    this.tenant = tenant;

    const shopName = tenant.externalId.toString().split('.')[0];

    this.client = new Shopify({
      shopName,
      accessToken: tenant.apiCredentials.accessToken,
      apiKey: tenant.apiCredentials.apiKey,
      password: tenant.apiCredentials.password,
      apiVersion: '2024-04',
    });

    // Log a warning if this integration somehow starts getting close to the rate limit.
    this.client.on('callLimits', limits => {
      if (limits.remaining < 5) {
        Logger.warn(
          `This Shopify integration has ${limits.remaining} calls remaining.`,
        );
      }
    });
  }

  async getWebhooks(): Promise<IWebhook[]> {
    return this.client.webhook.list();
  }

  async createWebhook(data: Partial<IWebhook>): Promise<IWebhook> {
    return this.client.webhook.create({
      topic: data.topic,
      address: data.address,
    });
  }

  async countConvertEcomScriptTags() {
    return await this.client.scriptTag.count({ src: scriptTagSrc });
  }

  async createScriptTags() {
    return await this.client.scriptTag.create({
      event: 'onload',
      display_scope: 'online_store',
      src: scriptTagSrc,
    });
  }

  async createPriceRule(coupon: Coupon): Promise<IPriceRule> {
    return this.client.priceRule.create({
      title: coupon.code,
      target_type: 'line_item',
      target_selection: 'all',
      allocation_method: 'across',
      value_type: this.getPriceRuleValueTypeForCoupon(coupon),
      value: this.getPriceRuleValueForCoupon(coupon),
      usage_limit: this.getPriceRuleUsageLimitForCoupon(coupon),
      customer_selection: 'all',
      prerequisite_subtotal_range: this.getPriceRulePrerequisiteSubtotalRange(
        coupon,
      ),
      starts_at: '2019-05-01T00:22:06.991Z', // Start time doesn't matter, so it's okay to hard-code here
    });
  }

  async updatePriceRule(
    priceRuleId: number,
    coupon: Coupon,
  ): Promise<IPriceRule> {
    return this.client.priceRule.update(priceRuleId, {
      value_type: this.getPriceRuleValueTypeForCoupon(coupon),
      value: this.getPriceRuleValueForCoupon(coupon),
      usage_limit: this.getPriceRuleUsageLimitForCoupon(coupon),
      prerequisite_subtotal_range: this.getPriceRulePrerequisiteSubtotalRange(
        coupon,
      ),
    });
  }

  async updatePartialPriceRule(
    priceRuleId: number,
    rules: Partial<IPriceRule>,
  ): Promise<IPriceRule> {
    return this.client.priceRule.update(priceRuleId, rules);
  }

  async createDiscountCode(
    priceRuleId: number,
    code: string,
  ): Promise<IDiscountCode> {
    return this.client.discountCode.create(priceRuleId, { code });
  }

  async createUsageCharge(
    recurringApplicationChargeId: number,
    description: string,
    price: number,
  ): Promise<IUsageCharge> {
    return this.client.usageCharge.create(recurringApplicationChargeId, {
      description,
      price,
    });
  }

  async createCustomer(optin: Optin) {
    return this.client.customer.create({ email: optin.email, accepts_marketing: true });
  }

  async getShopDetails() {
    return this.client.shop.get();
  }

  private getPriceRuleValueTypeForCoupon(coupon: Coupon): string {
    if (coupon.isFallback) {
      return 'percentage';
    } else {
      return 'fixed_amount';
    }
  }

  private getPriceRuleValueForCoupon(coupon: Coupon): string {
    if (coupon.isFallback) {
      return (coupon.percentage * 100 * -1).toString();
    } else {
      return (coupon.couponAmount * -1).toString();
    }
  }

  private getPriceRuleUsageLimitForCoupon(coupon: Coupon): number {
    return coupon.isFallback ? null : 1;
  }

  private getPriceRulePrerequisiteSubtotalRange(coupon: Coupon): any {
    if (coupon.isFallback) {
      return {};
    } else {
      return {
        greater_than_or_equal_to: coupon.minimumCartSubtotal.toString(),
      };
    }
  }
}
