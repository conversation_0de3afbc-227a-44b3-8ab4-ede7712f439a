import { Injectable, Logger } from '@nestjs/common';
import Mailchimp = require('mailchimp-api-v3'); // Sadface
import { EmailResult } from '../optin/optin.service';
import { Tenant } from '../tenant/tenant.entity';

@Injectable()
export class MailchimpService {
  private readonly logger = new Logger(MailchimpService.name, false);
  private getClient(tenant: Tenant) {
    return new Mailchimp(tenant.emailPlatformCredentials.apiKey);
  }

  async addEmailsToList(emails: EmailResult[], tenant: Tenant): Promise<string[]> {
    try {
      const client = this.getClient(tenant);

      const endpoint = `/lists/${tenant.emailPlatformCredentials.listId}`;
      const params = {
        members: emails.map(o => {
          return {
            email_address: o.email,
            status: 'subscribed',
          };
        }),
        update_existing: true,
      };
      const { new_members, errors } = await client.post(endpoint, params);

      if (errors) {
        // Some emails failed to be added, but possibly not all
        this.logger.error(errors);
      }

      return new_members.map(o => o.email_address);
    } catch (e) {
      this.logger.error(`Failed to add emails to list for tenant ${tenant.id}`);
      this.logger.error(e);
      return [];
    }
  }
}
