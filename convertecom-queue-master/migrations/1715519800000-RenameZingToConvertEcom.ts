import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameZingToConvertEcom1715519800000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    // Rename zingDiscounts column to convertEcomDiscounts in order table
    await queryRunner.query(`
      ALTER TABLE "order" RENAME COLUMN "zingDiscounts" TO "convertEcomDiscounts";
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    // Revert convertEcomDiscounts column back to zingDiscounts in order table
    await queryRunner.query(`
      ALTER TABLE "order" RENAME COLUMN "convertEcomDiscounts" TO "zingDiscounts";
    `);
  }
}
