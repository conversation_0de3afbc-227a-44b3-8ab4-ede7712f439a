# zing-queue

This is the Zing Queuing Service. It's a job runner that helps the Zing API avoid hitting the rate limit when it calls the Shopify API.

## Jobs

#### Finalize Pending Coupons

* Every second, a process pulls all tenants where `tenant.platform = 'shopify'`.
* For each tenant, it pulls a quantity of `tenant.queue_batch_size` coupons that need to be uploaded to Shopify (ie: `coupon.external_coupon_id = NULL`). The queue_batch_size is configured to equal that account's rate limit (either 2 or 4) divided by the number of API calls that are necessary to create a new coupon (2).
* The process creates a `PriceRule` and `DiscountCoupon` in the Shopify API, then updates the coupon in the Zing database `coupon.external_coupon_id = {discountCoupon.id}`.

## Framework

[Nest](https://github.com/nestjs/nest)

## Requirements

* Node 10 (developed on 11.14)
* Postgres

## Installation

```bash
$ npm install
```

## Running the app

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Test

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Future

This service should be used in the future if the Zing API needs to integrate with any other APIs that we want to throttle to avoid rate limits.