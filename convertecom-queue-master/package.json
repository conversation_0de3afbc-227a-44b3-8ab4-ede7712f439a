{"name": "convertecom-queue", "version": "0.0.1", "description": "Queue service for ConvertEcom", "author": "", "license": "MIT", "scripts": {"build": "tsc -p tsconfig.build.json", "format": "prettier --write \"src/**/*.ts\"", "start": "ts-node -r tsconfig-paths/register src/main.ts", "start:dev": "nodemon", "start:debug": "nodemon --config nodemon-debug.json", "start:prod": "node dist/main.js", "lint": "tslint -p tsconfig.json -c tslint.json", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "engines": {"node": "20.x"}, "dependencies": {"@nestjs/common": "^6.0.0", "@nestjs/core": "^6.0.0", "@nestjs/platform-express": "^6.0.0", "@nestjs/schedule": "^0.4.0", "@nestjs/testing": "^6.0.0", "@nestjs/typeorm": "^6.0.0", "@sendgrid/mail": "^6.5.4", "@sentry/node": "5.6.2", "@types/express": "^4.16.0", "@types/jest": "^23.3.13", "@types/lodash": "^4.14.124", "@types/node": "^10.12.18", "@types/supertest": "^2.0.7", "axios": "^0.19.0", "date-fns": "^2.15.0", "dotenv": "^7.0.0", "jest": "^23.6.0", "mailchimp-api-v3": "^1.13.1", "nest-raven": "^5.0.0", "nodemon": "^1.18.9", "pg": "^8.0.0", "pg-connection-string": "^2.1.0", "prettier": "^1.15.3", "reflect-metadata": "^0.1.12", "rimraf": "^2.6.2", "rxjs": "^6.3.3", "shopify-api-node": "^2.25.1", "supertest": "^3.4.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "tsconfig-paths": "^3.7.0", "tslint": "5.12.1", "typeorm": "^0.2.20", "typescript": "^3.6"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}, "devDependencies": {"@types/date-fns": "^2.6.0"}}