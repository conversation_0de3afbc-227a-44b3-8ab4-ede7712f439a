{"_comment": "This file is DISABLED - admin-master now uses .env file exclusively", "_note": "Database configuration is now handled via environment variables in .env file", "_see": "src/app.module.ts for the new configuration logic", "type": "postgres", "host": "DISABLED_USE_ENV_FILE", "port": 5432, "username": "DISABLED_USE_ENV_FILE", "password": "DISABLED_USE_ENV_FILE", "database": "DISABLED_USE_ENV_FILE", "entities": ["dist/**/*.entity{.ts,.js}"], "synchronize": true, "migrations": ["dist/migrations/*.js"], "cli": {"migrationsDir": "src/migrations"}, "logging": true, "ssl": {"rejectUnauthorized": true}}