# ConvertEcom Admin

## Description

Admin backend for ConvertEcom

## Installation

```bash
$ yarn install
```

## Running the app

```bash
# development
$ yarn run start

# watch mode
$ yarn run start:dev

# production mode
$ yarn build && yarn run start:prod
```

## Test

```bash
# unit tests
$ yarn run test

# e2e tests
$ yarn run test:e2e

# test coverage
$ yarn run test:cov
```

## Creating and Running Migrations
Create the migration file and add SQL to the `up` and `down` methods
```bash
$ yarn migrations:create -n NameOfMigrationClass
```

Update the `.env` file to include the production build settings
```dotenv
TYPEORM_ENTITIES=./dist/**/*.entity.js
TYPEORM_MIGRATIONS=./dist/migrations/*.js
TYPEORM_MIGRATIONS_DIR=./dist/migrations
TYPEORM_MIGRATIONS_RUN=true
```

Run
```bash
$ yarn build && yarn start:prod
```

Update the `.env` file to be back to the development settings

### Reset Billing to $0
In April 2021 (commit `ea5c81408b4`), ConvertEcom moved to a $0/month + fees model. Most clients should be moved over to the new pricing, but if a
client is not migrated, i.e. they are still paying $xx/month + fees, they can be migrated by changing
`tenant.onboardingStatus.isBillingSetup` to `false` and re-accepting the billing.

### Coupon Reconciliation

ConvertEcom and Shopify may become out of sync overtime due to webhooks failing or ConvertEcom
failing to receive a webhook for some reason (maybe it's down). Shopify suggests
pulling all orders since a specific date periodically to ensure that the custom app
is up to date. This may be done in the future with ConvertEcom, but for now we have a way to
check which coupons are out of sync so they can be resolved manually.

All endpoints require a valid JWT with the role `superadmin`.

**Start running queued jobs**

```POST /coupon/reconciliation/start```

**Stop and delete all jobs and statuses**

```POST /coupon/reconciliation/stop```

**Queue reconciliation job for a tenant**

```
POST /coupon/reconciliation
{ "tenantId": "<tenant-uuid>" }
```

**Stop reconciliation job for tenant**

```
DELETE /coupon/reconciliation/:tenantId
```

**Get reconciliation job status for a tenant**

```
GET /coupon/reconciliation/:tenantId
```
