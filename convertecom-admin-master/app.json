{"name": "ConvertEcom Admin Backend", "description": "Admin backend for ConvertEcom", "scripts": {}, "env": {}, "formation": {"web": {"quantity": 1}}, "buildpacks": [{"url": "hero<PERSON>/nodejs"}], "environments": {"review": {"addons": ["heroku-postgresql:hobby-dev"], "env": {"SHOPIFY_TEST": true, "JWT_EXPIRES_SECONDS": 3600, "JWT_SECRET": {"generator": "secret"}, "PORT": 443, "SESSION_SECRET": {"generator": "secret"}, "TYPEORM_CONNECTION": "postgres", "TYPEORM_ENTITIES": "./dist/**/*.entity.js", "TYPEORM_MIGRATIONS": "./dist/migrations/*.js", "TYPEORM_MIGRATIONS_RUN": true}}, "test": {"formation": {"test": {"quantity": 1, "size": "standard-1x"}}, "scripts": {"test-setup": "yarn install", "test": "yarn lint"}}}}