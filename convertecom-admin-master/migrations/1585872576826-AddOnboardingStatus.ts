import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddOnboardingStatus1585872576826 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`ALTER TABLE "tenant"
                ADD "onboardingStatus" json
                DEFAULT '{"isBillingSetup": false, "arePopupsCustomized": false, "isCodeSnippetAdded": false, "isConvertEcomActivated": false}'`);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(
      `ALTER TABLE "tenant" DROP COLUMN "onboardingStatus"`,
    );
  }
}
