import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddTenantBillingCreds1570222011004 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "tenant" ADD "billingCredentials" json`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "tenant" DROP COLUMN "billingCredentials"`);
    }

}
