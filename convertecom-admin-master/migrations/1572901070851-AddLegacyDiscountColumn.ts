import {MigrationInterface, QueryRunner} from "typeorm";

export class AddLegacyDiscountColumn1572901070851 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "tenant" ADD "legacyDiscountPercentage" numeric(8,5) NOT NULL DEFAULT 0.15`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "tenant" DROP COLUMN "legacyDiscountPercentage"`);
    }

}
