import {MigrationInterface, QueryRunner} from 'typeorm';
import * as bcrypt from 'bcrypt';

const tenantId = 'fb129fb0-b924-451e-a283-d775e5aa7ecf';

export class Seeder1561424969130 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        const password = await bcrypt.hash('test', 10);

        await queryRunner.query(`
            INSERT INTO "tenant"
                ("id", "platform", "name", "externalId")
                VALUES
                ('${tenantId}', 'shopify', 'SourceStrike Dev', 'couponly.myshopify.com');
        `);

        await queryRunner.query(`
            INSERT INTO "user"
                ("tenantId", "email", "password")
                VALUES
                ('${tenantId}', '<EMAIL>', '${password}');
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`DELETE FROM "tenant" WHERE id='${tenantId}' CASCADE;`);
    }

}
