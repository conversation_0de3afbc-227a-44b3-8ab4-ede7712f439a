import {MigrationInterface, QueryRunner} from "typeorm";

export class AddMin<PERSON>rossFields1566428702234 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "coupon" ADD "minimumCartSubtotal" numeric(8,2)`);
        await queryRunner.query(`ALTER TABLE "coupon" ADD "isDisabled" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "api_sync_queue" ADD "type" character varying NOT NULL DEFAULT 'create-coupon'`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "api_sync_queue" DROP COLUMN "type"`);
        await queryRunner.query(`ALTER TABLE "coupon" DROP COLUMN "isDisabled"`);
        await queryRunner.query(`ALTER TABLE "coupon" DROP COLUMN "minimumCartSubtotal"`);
    }

}
