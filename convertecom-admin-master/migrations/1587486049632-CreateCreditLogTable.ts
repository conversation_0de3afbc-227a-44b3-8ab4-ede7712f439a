import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCreditLogTable1587486049632 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
        CREATE TABLE credit_log (
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                "tenantId" uuid NOT NULL,
                "createdById" uuid NOT NULL,
                amount float NOT NULL,
                description varchar NOT NULL,
                test bool NOT NULL,
                "externalId" integer,
                "createdAt" timestamp NOT NULL DEFAULT now(),
                "updatedAt" timestamp NOT NULL DEFAULT now(),
                CONSTRAINT "PK_credit_log_id" PRIMARY KEY (id)
            );
            `);

    await queryRunner.query(`
      ALTER TABLE credit_log ADD CONSTRAINT "FK_credit_log_tenant" FOREIGN KEY ("tenantId") REFERENCES tenant(id);
      ALTER TABLE credit_log ADD CONSTRAINT "FK_credit_log_user" FOREIGN KEY ("createdById") REFERENCES "user"(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`DROP TABLE credit_log;`);
  }
}
