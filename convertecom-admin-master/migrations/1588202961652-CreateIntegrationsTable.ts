import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateIntegrationsTable1588202961652
  implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
        CREATE TABLE integration_platform
        (
            id          uuid      NOT NULL DEFAULT uuid_generate_v4(),
            name        varchar   NOT NULL,
            "createdAt" timestamp NOT NULL DEFAULT now(),
            "updatedAt" timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_integration_id" PRIMARY KEY (id)
        );
    `);

    await queryRunner.query(`
        CREATE TABLE integration
        (
            id            uuid      NOT NULL DEFAULT uuid_generate_v4(),
            "tenantId"    uuid      NOT NULL,
            "platformId"  uuid      NOT NULL,
            "isEnabled"   boolean   NOT NULL DEFAULT FALSE,
            configuration jsonb      NOT NULL,
            "createdAt"   timestamp NOT NULL DEFAULT now(),
            "updatedAt"   timestamp NOT NULL DEFAULT now(),
            CONSTRAINT "PK_tenant_integration_id" PRIMARY KEY (id)
        );
    `);

    await queryRunner.query(`
        ALTER TABLE integration
            ADD CONSTRAINT "FK_integration_tenant" FOREIGN KEY ("tenantId") REFERENCES tenant (id);
        ALTER TABLE integration
            ADD CONSTRAINT "FK_integration_integration_platform" FOREIGN KEY ("platformId") REFERENCES "integration_platform" (id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`DROP TABLE integration;`);
    await queryRunner.query(`DROP TABLE integration_platform;`);
  }
}
