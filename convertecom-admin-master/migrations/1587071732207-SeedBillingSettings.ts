import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedBillingSettings1587071732207 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
            INSERT INTO "billing_settings"
                (scope, type, value)
            VALUES
                ('global', 'subscription', '{ "trialDays": 14 }')
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {}
}
