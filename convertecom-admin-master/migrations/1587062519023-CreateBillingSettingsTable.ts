import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBillingSettingsTable1587062519023
  implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
            CREATE TYPE billing_setting_scope as ENUM ('global');
            CREATE TYPE billing_setting_type as ENUM ('subscription');

            CREATE TABLE billing_settings (
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                scope billing_setting_scope NOT NULL,
                type billing_setting_type NOT NULL,
                value json NOT NULL,
                "createdAt" timestamp NOT NULL DEFAULT now(),
                "updatedAt" timestamp NOT NULL DEFAULT now(),
                CONSTRAINT "PK_billing_settings_id" PRIMARY KEY (id),
                UNIQUE (scope, type)
            );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
        DROP TABLE "billing_settings" CASCADE;
        DROP TYPE billing_setting_scope;
        DROP TYPE billing_setting_type;
    `);
  }
}
