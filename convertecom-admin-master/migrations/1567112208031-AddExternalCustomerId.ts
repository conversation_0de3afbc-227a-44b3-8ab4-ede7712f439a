import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddExternalCustomerId1567112208031 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "order" ADD "externalCustomerId" character varying`);
        await queryRunner.query(`CREATE INDEX "IDX_6dec2f4f68f7cf1c3e7485e8d0" ON "order" ("externalCustomerId") `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`DROP INDEX "IDX_6dec2f4f68f7cf1c3e7485e8d0"`);
        await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "externalCustomerId"`);
    }

}
