import {MigrationInterface, QueryRunner} from "typeorm";

export class TenantTrialEndsOn1587415416904 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            ALTER TABLE "tenant"
                ADD "trialEndsOn" date
            `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            ALTER TABLE "tenant" DROP COLUMN "trialEndsOn";
        `);
    }

}
