import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBillingPercentage1574191926284 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "tenant" ADD "billingPercentage" numeric(8,7) NOT NULL DEFAULT 0.015`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "tenant" DROP COLUMN "billingPercentage"`);
    }

}
