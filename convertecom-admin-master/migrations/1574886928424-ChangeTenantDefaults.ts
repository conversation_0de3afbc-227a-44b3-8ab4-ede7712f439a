import {MigrationInterface, QueryRunner} from "typeorm";

export class ChangeTenantDefaults1574886928424 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "tenant" ALTER COLUMN "couponPercentage" SET DEFAULT 0.12`);
        await queryRunner.query(`ALTER TABLE "tenant" ALTER COLUMN "reservationMinutes" SET DEFAULT 20`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "tenant" ALTER COLUMN "reservationMinutes" SET DEFAULT 10`);
        await queryRunner.query(`ALTER TABLE "tenant" ALTER COLUMN "couponPercentage" SET DEFAULT 0.05`);
    }

}
