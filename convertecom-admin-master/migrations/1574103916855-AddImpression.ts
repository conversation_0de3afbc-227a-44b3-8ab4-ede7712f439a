import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddImpression1574103916855 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`CREATE TABLE "impression" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "externalId" character varying, "tenantId" uuid, CONSTRAINT "PK_25f497fa55d89a690c9d334c2a5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "optin" ADD "impressionId" uuid`);
        await queryRunner.query(`ALTER TABLE "impression" ADD CONSTRAINT "FK_a539e7c4cbe358d1bbac8074314" FOREIGN KEY ("tenantId") REFERENCES "tenant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "optin" ADD CONSTRAINT "FK_057e97b04f737c7f75210de1b56" FOREIGN KEY ("impressionId") REFERENCES "impression"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "optin" DROP CONSTRAINT "FK_057e97b04f737c7f75210de1b56"`);
        await queryRunner.query(`ALTER TABLE "impression" DROP CONSTRAINT "FK_a539e7c4cbe358d1bbac8074314"`);
        await queryRunner.query(`ALTER TABLE "optin" DROP COLUMN "impressionId"`);
        await queryRunner.query(`DROP TABLE "impression"`);
    }

}
