import { MigrationInterface, QueryRunner } from 'typeorm';

export class TenantFeatureFlags1587162616823 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
            ALTER TABLE "tenant"
                ADD "featureFlags" json
                DEFAULT '{}'
            `);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
            ALTER TABLE "tenant" DROP COLUMN "featureFlags";
        `);
  }
}
