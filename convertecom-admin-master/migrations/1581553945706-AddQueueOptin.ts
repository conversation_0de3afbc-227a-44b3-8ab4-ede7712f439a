import {MigrationInterface, QueryRunner} from "typeorm";

export class AddQueueOptin1581553945706 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "api_sync_queue" ADD "optinId" uuid`);
        await queryRunner.query(`ALTER TABLE "api_sync_queue" ADD CONSTRAINT "FK_4a22e0180582693bb0ab058b7b7" FOREIGN KEY ("optinId") REFERENCES "optin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "api_sync_queue" DROP CONSTRAINT "FK_4a22e0180582693bb0ab058b7b7"`);
        await queryRunner.query(`ALTER TABLE "api_sync_queue" DROP COLUMN "optinId"`);
    }

}
