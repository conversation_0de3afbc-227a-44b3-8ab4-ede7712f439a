import {MigrationInterface, QueryRunner} from "typeorm";

export class AddBillingTable1568676649171 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`CREATE TABLE "billing_log" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" character varying NOT NULL, "rawResponse" text NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_7edbd2d13488ba454a956a80c85" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "billing" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "tenantId" uuid NOT NULL, "date" character varying NOT NULL, "amount" numeric(10,2) NOT NULL, "percentage" numeric(3,2) NOT NULL, "subtotalSum" numeric(10,2) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "billingLogId" uuid, CONSTRAINT "REL_2a6053fa4a0470b1fb46af8396" UNIQUE ("billingLogId"), CONSTRAINT "PK_d9043caf3033c11ed3d1b29f73c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_e798d8d6895ba763d888f96fec" ON "billing" ("tenantId", "date") `);
        await queryRunner.query(`ALTER TABLE "billing" ADD CONSTRAINT "FK_2eed07a51efdd8ea99d442bcf2c" FOREIGN KEY ("tenantId") REFERENCES "tenant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "billing" ADD CONSTRAINT "FK_2a6053fa4a0470b1fb46af83960" FOREIGN KEY ("billingLogId") REFERENCES "billing_log"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "billing" DROP CONSTRAINT "FK_2a6053fa4a0470b1fb46af83960"`);
        await queryRunner.query(`ALTER TABLE "billing" DROP CONSTRAINT "FK_2eed07a51efdd8ea99d442bcf2c"`);
        await queryRunner.query(`DROP INDEX "IDX_e798d8d6895ba763d888f96fec"`);
        await queryRunner.query(`DROP TABLE "billing"`);
        await queryRunner.query(`DROP TABLE "billing_log"`);
    }

}
