import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialMigration1561423682649 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        `);

        await queryRunner.query(`
            CREATE TABLE api_sync_queue (
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" timestamp NOT NULL DEFAULT now(),
                "startedAt" timestamp NULL,
                "finishedAt" timestamp NULL,
                "permanentFailure" bool NOT NULL DEFAULT false,
                "tenantId" uuid NULL,
                "couponId" uuid NULL,
                CONSTRAINT "PK_cb0ce86b4b4400d1584f1ab1556" PRIMARY KEY (id)
            );
        `);

        await queryRunner.query(`
            CREATE TABLE "comment" (
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                "namespace" varchar NOT NULL,
                body text NOT NULL,
                "createdAt" timestamp NOT NULL DEFAULT now(),
                "updatedAt" timestamp NOT NULL DEFAULT now(),
                "authorId" uuid NULL,
                "userId" uuid NULL,
                "couponId" uuid NULL,
                CONSTRAINT "PK_0b0e4bbc8415ec426f87f3a88e2" PRIMARY KEY (id)
            );
        `);

        await queryRunner.query(`
            CREATE TABLE coupon (
                code varchar NOT NULL,
                percentage numeric(8,5) NOT NULL,
                "createdAt" timestamp NOT NULL DEFAULT now(),
                "updatedAt" timestamp NOT NULL DEFAULT now(),
                "originatorOrderId" uuid NULL,
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                "tenantId" uuid NULL,
                "couponAmount" numeric(8,2) NOT NULL,
                "reservedId" varchar NULL,
                "reservedUntil" timestamp NULL,
                "isFallback" bool NOT NULL DEFAULT false,
                CONSTRAINT "PK_fcbe9d72b60eed35f46dc35a682" PRIMARY KEY (id),
                CONSTRAINT "UQ_18b7c717e47e67fb46bb5ed229b" UNIQUE ("originatorOrderId")
            );
        `);

        await queryRunner.query(`
            CREATE TABLE coupon_redemption_orders_order (
                "couponId" uuid NOT NULL,
                "orderId" uuid NOT NULL,
                CONSTRAINT "PK_7d1194067700a8b077570ea224f" PRIMARY KEY ("couponId", "orderId")
            );
        `);

        await queryRunner.query(`
            CREATE TABLE customer (
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                "externalCustomerId" varchar NULL,
                email varchar NULL,
                optin bool NOT NULL,
                CONSTRAINT "PK_a7a13f4cacb744524e44dfdad32" PRIMARY KEY (id)
            );
        `);

        await queryRunner.query(`
            CREATE TABLE optin (
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                email varchar NOT NULL,
                "createdAt" timestamp NOT NULL DEFAULT now(),
                "updatedAt" timestamp NOT NULL DEFAULT now(),
                "tenantId" uuid NULL,
                CONSTRAINT "PK_5f81b60607beafecc2fd6281262" PRIMARY KEY (id)
            );
        `);

        await queryRunner.query(`
            CREATE TABLE "order" (
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" timestamp NOT NULL DEFAULT now(),
                "updatedAt" timestamp NOT NULL DEFAULT now(),
                "tenantId" uuid NULL,
                "externalOrderId" varchar NOT NULL,
                "totalPrice" numeric(8,2) NOT NULL,
                "subtotalPrice" numeric(8,2) NOT NULL,
                "totalTax" numeric(8,2) NOT NULL,
                "totalDiscounts" numeric(8,2) NOT NULL,
                "convertEcomDiscounts" numeric(8,2) NOT NULL,
                "externalCreatedAt" timestamp NULL,
                "externalPaymentAt" timestamp NULL,
                CONSTRAINT "PK_1031171c13130102495201e3e20" PRIMARY KEY (id)
            );
        `);

        await queryRunner.query(`
            CREATE TABLE platform_data (
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                "key" varchar NOT NULL,
                value varchar NOT NULL,
                "createdAt" timestamp NOT NULL DEFAULT now(),
                "updatedAt" timestamp NOT NULL DEFAULT now(),
                "couponId" uuid NULL,
                CONSTRAINT "PK_6c699fcdb50e9843781612200a2" PRIMARY KEY (id)
            );
        `);

        await queryRunner.query(`
            CREATE TABLE tenant (
                platform varchar NOT NULL,
                "name" varchar NOT NULL,
                "couponPercentage" numeric(8,5) NOT NULL DEFAULT 0.05,
                "createdAt" timestamp NOT NULL DEFAULT now(),
                "updatedAt" timestamp NOT NULL DEFAULT now(),
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                "fallbackCouponId" uuid NULL,
                "reservationMinutes" int4 NOT NULL DEFAULT 10,
                "minimumGrossPercentage" numeric(8,5) NOT NULL DEFAULT 0.8,
                "externalId" varchar NULL,
                "apiCredentials" json NULL,
                "queueBatchSize" int4 NOT NULL DEFAULT 1,
                CONSTRAINT "PK_da8c6efd67bb301e810e56ac139" PRIMARY KEY (id),
                CONSTRAINT "UQ_d834dd0d417270076555156bc21" UNIQUE ("fallbackCouponId")
            );
        `);

        await queryRunner.query(`
            CREATE TABLE "user" (
                email varchar NOT NULL,
                "password" varchar NOT NULL,
                "createdAt" timestamp NOT NULL DEFAULT now(),
                "updatedAt" timestamp NOT NULL DEFAULT now(),
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                "tenantId" uuid NULL,
                "resetPasswordToken" varchar NULL,
                "resetPasswordSentAt" timestamp NULL,
                "rememberCreatedAt" timestamp NULL,
                "signInCount" int4 NOT NULL DEFAULT 0,
                "currentSignInAt" timestamp NULL,
                "lastSignInAt" timestamp NULL,
                "currentSignInIp" varchar NULL,
                "lastSignInIp" varchar NULL,
                CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY (id),
                CONSTRAINT "UQ_6699b8457beeaf928125b348e81" UNIQUE ("resetPasswordToken"),
                CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE (email)
            );
        `);

        // Foreign keys
        await queryRunner.query(`
            ALTER TABLE api_sync_queue ADD CONSTRAINT "FK_ca6b6f3b71a4db13ac90ec94278" FOREIGN KEY ("couponId") REFERENCES coupon(id);
        `);

        await queryRunner.query(`
            ALTER TABLE api_sync_queue ADD CONSTRAINT "FK_cc5c17b9e7a77ee0c40b8b8b629" FOREIGN KEY ("tenantId") REFERENCES tenant(id)
        `);

        await queryRunner.query(`
            ALTER TABLE "comment" ADD CONSTRAINT "FK_276779da446413a0d79598d4fbd" FOREIGN KEY ("authorId") REFERENCES "user"(id)
        `);

        await queryRunner.query(`
            ALTER TABLE "comment" ADD CONSTRAINT "FK_c0354a9a009d3bb45a08655ce3b" FOREIGN KEY ("userId") REFERENCES "user"(id)
        `);

        await queryRunner.query(`
            ALTER TABLE "comment" ADD CONSTRAINT "FK_f7b6052bd65f1ae1170de382600" FOREIGN KEY ("couponId") REFERENCES coupon(id)
        `);

        await queryRunner.query(`
            ALTER TABLE "coupon" ADD CONSTRAINT "FK_18b7c717e47e67fb46bb5ed229b" FOREIGN KEY ("originatorOrderId") REFERENCES "order"(id)
        `);

        await queryRunner.query(`
            ALTER TABLE "coupon" ADD CONSTRAINT "FK_86e95ee23640c91ea4028e40881" FOREIGN KEY ("tenantId") REFERENCES tenant(id)
        `);

        await queryRunner.query(`
            ALTER TABLE "coupon_redemption_orders_order"
                ADD CONSTRAINT "FK_086067b8384777253f1427fa311" FOREIGN KEY ("couponId") REFERENCES coupon(id) ON DELETE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "coupon_redemption_orders_order"
                ADD CONSTRAINT "FK_a4ccd90d53343573d56df74137e" FOREIGN KEY ("orderId") REFERENCES "order"(id) ON DELETE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "optin" ADD CONSTRAINT "FK_576ba679bd687e3cb6d8edb3072" FOREIGN KEY ("tenantId") REFERENCES tenant(id);
        `);

        await queryRunner.query(`
            ALTER TABLE "order" ADD CONSTRAINT "FK_7853202400ba8726242baa7a916" FOREIGN KEY ("tenantId") REFERENCES tenant(id);
        `);

        await queryRunner.query(`
            ALTER TABLE "platform_data" ADD CONSTRAINT "FK_b46db91b55d15995121b5b9b26b" FOREIGN KEY ("couponId") REFERENCES coupon(id);
        `);

        await queryRunner.query(`
            ALTER TABLE "tenant" ADD CONSTRAINT "FK_d834dd0d417270076555156bc21" FOREIGN KEY ("fallbackCouponId") REFERENCES coupon(id);
        `);

        await queryRunner.query(`
            ALTER TABLE "user" ADD CONSTRAINT "FK_685bf353c85f23b6f848e4dcded" FOREIGN KEY ("tenantId") REFERENCES tenant(id);
        `);

        // Indexes
        await queryRunner.query(`
            CREATE INDEX "IDX_d274234d37ebb267f205ab2219" ON comment USING btree (namespace);
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_3ca6ba25abbb2fe10d0472d2be" ON coupon USING btree ("reservedId");
        `);

        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_62d3c5b0ce63a82c48e86d904b" ON coupon USING btree (code);
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_086067b8384777253f1427fa31" ON coupon_redemption_orders_order USING btree ("couponId");
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_a4ccd90d53343573d56df74137" ON coupon_redemption_orders_order USING btree ("orderId");
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_9633b9159ab436d6fca61519f9" ON "order" USING btree ("externalOrderId");
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`DROP TABLE "api_sync_queue" CASCADE;`);
        await queryRunner.query(`DROP TABLE "comment" CASCADE;`);
        await queryRunner.query(`DROP TABLE "coupon" CASCADE;`);
        await queryRunner.query(`DROP TABLE "coupon_redemption_orders_order" CASCADE;`);
        await queryRunner.query(`DROP TABLE "customer" CASCADE;`);
        await queryRunner.query(`DROP TABLE "optin" CASCADE;`);
        await queryRunner.query(`DROP TABLE "order" CASCADE;`);
        await queryRunner.query(`DROP TABLE "platform_data" CASCADE;`);
        await queryRunner.query(`DROP TABLE "tenant" CASCADE;`);
        await queryRunner.query(`DROP TABLE "user" CASCADE;`);
    }

}
