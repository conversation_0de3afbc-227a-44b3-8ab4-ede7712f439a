import {MigrationInterface, QueryRunner} from "typeorm";

export class AddOrderOptinRelationship1562175369562 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "order" ADD "optinId" uuid`);
        await queryRunner.query(`ALTER TABLE "order" ADD CONSTRAINT "FK_094751fd0e0a7859f6898d35ec4" FOREIGN KEY ("optinId") REFERENCES "optin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "order" DROP CONSTRAINT "FK_094751fd0e0a7859f6898d35ec4"`);
        await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "optinId"`);
    }

}
