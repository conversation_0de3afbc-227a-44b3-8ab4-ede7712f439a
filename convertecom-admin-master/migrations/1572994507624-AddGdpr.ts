import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddGdpr1572994507624 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`CREATE TABLE "gdpr_log" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "action" character varying NOT NULL, "rawData" json NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "tenantId" uuid, CONSTRAINT "PK_39b1dbb110307c61a71479e1885" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "gdpr_log" ADD CONSTRAINT "FK_8a7c760d21557ad6c7482a2947e" FOREIGN KEY ("tenantId") REFERENCES "tenant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "gdpr_log" DROP CONSTRAINT "FK_8a7c760d21557ad6c7482a2947e"`);
        await queryRunner.query(`DROP TABLE "gdpr_log"`);
    }

}
