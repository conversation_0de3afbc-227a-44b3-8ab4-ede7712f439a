import { MigrationInterface, QueryRunner } from 'typeorm';

export class AuditLog1689052023103 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    const createQuery = `
CREATE TABLE IF NOT EXISTS "audit_log"
(
    "id"        uuid              NOT NULL DEFAULT uuid_generate_v4(),
    "eventType" character varying NOT NULL,
    "targetId"  uuid              NOT NULL,
    "metadata"  json              NOT NULL DEFAULT '{}'::json,
    "createdAt" TIMESTAMP         NOT NULL DEFAULT now(),
    "updatedAt" TIMESTAMP         NOT NULL DEFAULT now(),
    CONSTRAINT "PK_audit_log" PRIMARY KEY ("id")
);
`;

    const eventTypeTargetIndex = `
    CREATE INDEX IF NOT EXISTS "IDX_event_type_target_id" on "audit_log" ("eventType", "targetId");
    `;

    const targetEventTypeIndex = `
    CREATE INDEX IF NOT EXISTS "IDX_target_id_event_type" on "audit_log" ("targetId", "eventType");
    `;

    await queryRunner.query(createQuery);
    await queryRunner.query(eventTypeTargetIndex);
    await queryRunner.query(targetEventTypeIndex);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query('DROP TABLE IF EXISTS "audit_log";');
  }
}
