import { MigrationInterface, QueryRunner } from 'typeorm';

export class FallbackMaxCoupon1662151534044 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
        ALTER TABLE "tenant"
            ADD "fallbackCouponMaxPercentage" numeric(8,5) NOT NULL DEFAULT 0.5
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
        ALTER TABLE "tenant"
            DROP COLUMN "fallbackCouponMaxPercentage"
        `);
  }
}
