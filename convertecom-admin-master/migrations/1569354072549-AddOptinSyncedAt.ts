import {MigrationInterface, QueryRunner} from "typeorm";

export class AddOptinSyncedAt1569354072549 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "optin" ADD "syncedAt" TIMESTAMP`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "optin" DROP COLUMN "syncedAt"`);
    }

}
