import {MigrationInterface, QueryRunner} from "typeorm";

export class AddEmailPlatformCreds1569280603895 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "tenant" ADD "emailPlatformCredentials" json`);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "tenant" DROP COLUMN "emailPlatformCredentials"`);
    }

}
