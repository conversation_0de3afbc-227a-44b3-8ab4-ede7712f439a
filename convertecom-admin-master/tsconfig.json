{"compilerOptions": {"module": "commonjs", "types": ["@types/node"], "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es6", "sourceMap": true, "outDir": "./dist", "baseUrl": "./src", "skipLibCheck": true, "paths": {"@app/integration/*": ["integration/*"], "@app/auth/*": ["auth/*"], "@app/tenant/*": ["tenant/*"]}}, "exclude": ["node_modules", "dist"]}