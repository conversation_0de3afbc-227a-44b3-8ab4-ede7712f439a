import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, EntityManager } from 'typeorm';
import { User } from './user.entity';
import { LoginCredentialsDto } from '../auth/dto/login-credentials.dto';
import * as bcrypt from 'bcrypt';
import { FindAllDto } from './dto/find-all.dto';
import { Dir } from '../lib/types/typeorm';

export interface FindAndCountRes {
  total: number;
  data: User[];
}

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  private async hashPassword(password: string): Promise<string> {
    return await bcrypt.hash(password, 10);
  }

  private async validatePassword(
    password: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return await bcrypt.compare(password, hashedPassword);
  }

  async create(user: Partial<User>): Promise<User> {
    user.password = await this.hashPassword(user.password);
    return await this.userRepository.save(user);
  }

  async update(
    id: string,
    user: Partial<User>,
    em?: EntityManager,
  ): Promise<User> {
    if (user.password) {
      user.password = await this.hashPassword(user.password);
    } else if (user.password === '' || user.password === null) {
      delete user.password; // Ignore password if it's empty
    }
    const userRepository = em
      ? await em.getRepository(User)
      : this.userRepository;
    await userRepository.update(id, user);
    return userRepository.findOne(id);
  }

  async delete(id: string) {
    return await this.userRepository.delete(id);
  }

  async findOne(id: string): Promise<User> {
    return await this.userRepository.findOne(id);
  }

  async findOneByCredentials(
    loginCredentials: LoginCredentialsDto,
  ): Promise<User> {
    const user = await this.userRepository.findOne({
      email: loginCredentials.email,
    });
    if (!user) {
      throw new UnauthorizedException();
    }
    const isValidPassword = await this.validatePassword(
      loginCredentials.password,
      user.password,
    );
    if (!isValidPassword) {
      throw new UnauthorizedException();
    }
    return user;
  }

  async findAll(findAllDto: FindAllDto): Promise<FindAndCountRes> {
    const params: FindManyOptions<User> = {
      order: {
        [findAllDto.order || 'id']: (
          findAllDto.dir || 'ASC'
        ).toUpperCase() as Dir,
      },
      skip: findAllDto.skip || 0,
      take: findAllDto.take || 20,
    };

    const [data, total] = await this.userRepository.findAndCount(params);
    return { data, total };
  }
}
