import { BadRequestException, Body, Controller, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { classToPlain } from 'class-transformer';

import { omit } from 'lodash';

import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { FindAllDto } from './dto/find-all.dto';
import { Roles } from '../lib/decorators/roles.decorator';
import { Role } from '../lib/role';

@Controller('user')
@Roles(Role.SUPERADMIN)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  async findAll(@Req() req, @Query() findAllDto: FindAllDto) {
    const { data, total } = await this.userService.findAll(findAllDto);
    return {
      total,
      data: classToPlain(data),
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @Req() req) {
    const user = await this.userService.findOne(id);
    return { data: classToPlain(user) };
  }

  @Post()
  async create(@Body() createUserDto: CreateUserDto, @Req() req) {
    try {
      const user = await this.userService.create(createUserDto);
      return { data: classToPlain(user) };
    } catch (e) {
      this.handleUserDbError(e);
    }
  }

  @Patch(':id')
  async update(
    @Param('id') id,
    @Body() updateUserDto: UpdateUserDto,
    @Req() req,
  ) {
    const user = await this.userService.findOne(id);

    try {
      const data = omit(updateUserDto, ['passwordConfirmation']);
      const updatedUser = await this.userService.update(user.id, data);
      return { data: classToPlain(updatedUser) };
    } catch (e) {
      this.handleUserDbError(e);
    }
  }

  private handleUserDbError(e) {
    switch (e.code) {
      case '23505':
        // Unique email constraint failed
        const message = [
          {
            property: 'email',
            constraints: { exists: 'email is already registered' },
          },
        ];
        throw new BadRequestException(message);
      default:
        // Rethrow
        throw e;
    }
  }
}
