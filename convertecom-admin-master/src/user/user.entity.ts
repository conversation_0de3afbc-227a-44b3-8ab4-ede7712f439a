import {
  PrimaryGeneratedColumn,
  Column,
  Entity,
  OneToMany,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { Comment } from '../comment/comment.entity';
import { Tenant } from '../tenant/tenant.entity';

@Entity()
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'simple-array',
  })
  roles: string[];

  @Column({
    type: 'varchar',
    nullable: false,
    unique: true,
  })
  email: string;

  @Exclude()
  @Column({
    type: 'varchar',
    nullable: false,
  })
  password: string;

  @Exclude()
  @Column({
    type: 'varchar',
    nullable: true,
    unique: true,
  })
  resetPasswordToken: string;

  @Exclude()
  @Column({
    type: 'timestamp',
    nullable: true,
  })
  resetPasswordSentAt: Date;

  @Exclude()
  @Column({
    type: 'timestamp',
    nullable: true,
  })
  rememberCreatedAt: Date;

  @Column({
    type: 'integer',
    default: 0,
    nullable: false,
  })
  signInCount: number;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  currentSignInAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  lastSignInAt: Date;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  currentSignInIp: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  lastSignInIp: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(type => Tenant, tenant => tenant.users, { eager: true })
  tenant: Tenant;

  // Comments received by this user
  @OneToMany(type => Comment, comment => comment.user)
  comments: Promise<Comment[]>;

  // Comments written by this user
  @OneToMany(type => Comment, comment => comment.author)
  authoredComments: Comment[];

  constructor(partial: Partial<User>) {
    Object.assign(this, partial);
  }
}
