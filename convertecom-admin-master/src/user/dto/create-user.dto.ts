import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { IsEqualToField } from '../../lib/validators/IsEqualToField';

export class CreateUserDto {
  @IsNotEmpty()
  @IsEmail()
  readonly email: string;

  @IsNotEmpty()
  @MinLength(8)
  readonly password: string;

  @IsNotEmpty()
  @IsEqualToField('password', { message: 'Passwords must match' })
  readonly passwordConfirmation: string;
}
