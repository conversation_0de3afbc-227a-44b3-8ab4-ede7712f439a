import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>idate<PERSON>f } from 'class-validator';
import { IsEqualToField } from '../../lib/validators/IsEqualToField';

export class UpdateUserDto {
  @IsOptional()
  @IsEmail()
  readonly email: string;

  @ValidateIf(o => !!o.password)
  @MinLength(8)
  readonly password: string;

  @ValidateIf(o => !!o.password)
  @IsEqualToField('password', { message: 'Passwords must match' })
  readonly passwordConfirmation: string;
}
