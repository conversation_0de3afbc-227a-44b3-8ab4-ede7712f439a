import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { ApiSyncQueue, ApiSyncQueueType } from './api-sync-queue.entity';
import { Coupon } from '../coupon/coupon.entity';
import { Tenant } from '../tenant/tenant.entity';

@Injectable()
export class ApiSyncQueueService {
  constructor(
    @InjectRepository(ApiSyncQueue)
    private readonly apiSyncQueueRepository: Repository<ApiSyncQueue>,
  ) {}

  async addCouponJob(coupon: Coupon, tenant: Tenant): Promise<ApiSyncQueue> {
    return await this.apiSyncQueueRepository.save({
      coupon,
      tenant,
      type: ApiSyncQueueType.CREATE_COUPON,
    });
  }

  async addCouponChangedJob(
    coupon: Coupon,
    tenant: Tenant,
  ): Promise<ApiSyncQueue> {
    return await this.apiSyncQueueRepository.save({
      coupon,
      tenant,
      type: ApiSyncQueueType.UPDATE_COUPON,
    });
  }

  async addTenantOrderHistoryJob(tenant: Tenant): Promise<ApiSyncQueue> {
    return await this.apiSyncQueueRepository.save({
      tenant,
      type: ApiSyncQueueType.IMPORT_ALL_ORDERS,
    });
  }

  async addTenantMinimumGrossChangedJob(
    tenant: Tenant,
    entityManager: EntityManager,
  ): Promise<ApiSyncQueue> {
    return await entityManager.getRepository(ApiSyncQueue).save({
      tenant,
      type: ApiSyncQueueType.UPDATE_MIN_GROSS,
    });
  }
  async addTenantFallbackCouponMaxPercentage(
    tenant: Tenant,
  ): Promise<ApiSyncQueue> {
    return await this.apiSyncQueueRepository.save({
      tenant,
      type: ApiSyncQueueType.UPDATE_FALLBACK_MAX_COUPON,
    });
  }

  addCreateFallbackCouponToRotateJob(tenant: Tenant): Promise<ApiSyncQueue> {
    return this.apiSyncQueueRepository.save({
      tenant,
      type: ApiSyncQueueType.CREATE_FALLBACK_COUPON_TO_ROTATE,
    });
  }
}
