import { EmailClient } from '../abstract.email-client';
import { QueryDto } from '../dto/query.dto';
import { EmailServiceList } from '../interfaces/email-service-lists.interface';
import Axios, { AxiosInstance } from 'axios';
import { InternalServerErrorException, Logger } from '@nestjs/common';

export class KlaviyoEmailClient implements EmailClient {
  private client: AxiosInstance;
  private readonly logger = new Logger(KlaviyoEmailClient.name);

  constructor(apiKey: string) {
    this.client = this.getClient(apiKey);
  }

  async getLists(): Promise<EmailServiceList[]> {
    try {
      const response = await this.client.get('/v2/lists');
      return response.data.map(l => {
        return { id: l.list_id, name: l.list_name };
      });
    } catch (e) {
      this.logger.log(`Failed to call the Klayvio API: ${e?.message}`);
      return [];
    }
  }

  private getClient(apiKey: string) {
    return Axios.create({
      timeout: 5000,
      baseURL: 'https://a.klaviyo.com/api/',
      headers: { 'Content-Type': 'application/json', 'api-key': apiKey },
    });
  }
}
