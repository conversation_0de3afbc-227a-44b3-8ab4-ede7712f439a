import { EmailClient } from '../abstract.email-client';
import { EmailServiceList } from '../interfaces/email-service-lists.interface';
import { InternalServerErrorException, Logger } from '@nestjs/common';
import Mailchimp = require('mailchimp-api-v3');

export class MailchimpEmailClient implements EmailClient {
  private client: Mailchimp;

  constructor(apiKey: string) {
    this.client = this.getClient(apiKey);
  }

  async getLists(): Promise<EmailServiceList[]> {
    try {
      const { lists } = await this.client.get({
        path: '/lists',
        query: {
          fields: ['id', 'name'],
          count: 200,
        },
      });
      return lists;
    } catch (e) {
      Logger.error(e);
      throw new InternalServerErrorException(
        'Failed to call the Mailchimp API',
      );
    }
  }

  private getClient(apiKey: string) {
    return new Mailchimp(apiKey);
  }
}
