import { BadRequestException, Injectable } from '@nestjs/common';
import { QueryDto } from './dto/query.dto';
import { EmailPlatform } from '../tenant/interfaces/email-platform-credentials.interface';
import { KlaviyoEmailClient } from './clients/klaviyo.email-client';
import { MailchimpEmailClient } from './clients/mailchimp.email-client';
import { EmailServiceList } from './interfaces/email-service-lists.interface';

@Injectable()
export class EmailService {
  async getLists(params: QueryDto): Promise<EmailServiceList[]> {
    const { platform, apiKey } = params;
    const client = this.getClient(platform, apiKey);
    return client.getLists();
  }

  private getClient(platform: EmailPlatform, apiKey: string) {
    switch (platform) {
      case EmailPlatform.KLAVIYO: {
        return new KlaviyoEmailClient(apiKey);
      }
      case EmailPlatform.MAILCHIMP: {
        return new MailchimpEmailClient(apiKey);
      }
      default:
        throw new BadRequestException('Please provide a valid platform.');
    }
  }
}
