import { CacheInterceptor, ExecutionContext, Injectable } from '@nestjs/common';
import { Tenant } from '../../../tenant/tenant.entity';

@Injectable()
export class TenantAwareCacheInterceptor extends CacheInterceptor {
  trackBy(context: ExecutionContext): string | undefined {
    // if (process.env.NODE_ENV !== 'production') {
    //   return undefined;
    // }
    // Use the base class to get the key
    const baseKey = super.trackBy(context);
    if (typeof baseKey === 'undefined') {
      return undefined;
    }
    try {
      const req = context.switchToHttp().getRequest();
      const tenant: Tenant = req.authUser.tenant;
      if (!tenant) {
        return undefined;
      }
      return `${baseKey}-Tenant-${tenant.id}`;
    } catch (e) {
      return undefined;
    }
  }
}
