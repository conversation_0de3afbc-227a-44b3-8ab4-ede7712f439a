export function floatEqual(n1, n2, precision = 0.00001): boolean {
  const float1 = parseFloat(n1);
  const float2 = parseFloat(n2);

  return Math.abs(float1 - float2) <= precision;
}

/**
 * Safely divide two numbers, even if the divisor could be 0
 */
export function safeDivideByZero(quotient: number, divisor: number): number {
  if (divisor === 0) {
    return 0;
  }

  return quotient / divisor;
}
