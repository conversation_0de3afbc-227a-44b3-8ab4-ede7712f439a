export enum ResultType {
  ERROR = 'error',
  SUCCESS = 'success',
}

export interface ResultError<E = any> {
  type: ResultType.ERROR;
  error: E;
}

export interface ResultSuccess<V = any> {
  type: ResultType.SUCCESS;
  value: V;
}

export type Result<V = any, E = any> = ResultSuccess<V> | ResultError<E>;

export function isErrorResult(
  result: ResultError | ResultSuccess,
): result is ResultError {
  return result.type === ResultType.ERROR;
}

export function isResultSuccess(
  result: ResultError | ResultSuccess,
): result is ResultSuccess {
  return result.type === ResultType.SUCCESS;
}

export function successResult<V = unknown>(value: V): ResultSuccess<V> {
  return {
    value,
    type: ResultType.SUCCESS,
  };
}

export function errorResult<E = unknown>(error: E): ResultError<E> {
  return {
    error,
    type: ResultType.ERROR,
  };
}
