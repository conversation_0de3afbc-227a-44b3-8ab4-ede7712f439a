import { createParamDecorator } from '@nestjs/common';
import * as LosslessJSON from 'lossless-json';

// Shopify's ids are expressed as integers and are more than 15 significant figures
// This is a problem, because JavaScript only supports 15 significant digits on the "number" type,
// so we need to use lossless JSON parsing
export const ShopifyWebhookBody = createParamDecorator((data, req) => {
  return LosslessJSON.parse(req.rawBody);
});
