/**
 * Build a prepared statement like `where predicate1 and predicate2 and predicate3`
 */
export function withWheres() {
  let locked = false;
  const predicates = [];

  function add(predicate: string, value: unknown) {
    if (locked) {
      throw new Error(
        'Cannot add a predicate after the statement has been built',
      );
    }
    predicates.push([predicate, value]);
  }

  function hasWheres() {
    return predicates.length > 0;
  }

  function getPreparedStatement(joinWith: 'and' | 'or' = 'and') {
    locked = true;
    return !hasWheres()
      ? ''
      : `where ${predicates
          .map(([predicate], index) => `${predicate} $${index + 1}`)
          .join(` ${joinWith} `)}`;
  }

  function getParameters() {
    return predicates.map(([_, value]) => value);
  }

  return {
    add,
    hasWheres,
    getPreparedStatement,
    getParameters,
  };
}
