import {
  ValidationOptions,
  registerDecorator,
  ValidatorConstraintInterface,
  ValidatorConstraint,
  ValidationArguments,
} from 'class-validator';

// The "@Equals" decorator only checks that the field is equal to a hard-coded value. This decorator checks that one field is equal to another
export function IsEqualToField(
  property: string,
  validationOptions?: ValidationOptions,
) {
  return (object: object, propertyName: string) => {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [property],
      validator: IsEqualToFieldConstraint,
    });
  };
}

@ValidatorConstraint({ name: 'isEqualToField' })
class IsEqualToFieldConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const [relatedPropertyName] = args.constraints;
    const relatedValue = (args.object as any)[relatedPropertyName];
    return value === relatedValue;
  }
}
