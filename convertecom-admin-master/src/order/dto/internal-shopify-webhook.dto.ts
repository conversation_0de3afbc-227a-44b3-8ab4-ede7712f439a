export interface NoteAttribute {
  name: string,
  value: string,
}

export interface DiscountCode {
  code: string,
  amount: number | string,
  type: string,
}

/**
 * Send this to convertecom-api to simulate a Shopify Webhook
 */
export class InternalShopifyWebhook {
  readonly id: string; // JavaScript's number type only supports up to 15 significant digits, and Shopify ids are 18 digits, so use it as a string.
  readonly token: string;
  readonly cart_token: string;
  readonly created_at: Date;
  readonly updated_at: Date;
  readonly line_items: object[];
  readonly name: string;
  readonly discount_codes: DiscountCode[];
  readonly note_attributes: NoteAttribute[];
  readonly total_discounts: number;
  readonly total_line_items_price: number;
  readonly total_price: number;
  readonly total_tax: number;
  readonly subtotal_price: number;
  readonly customer: any;
}
