import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { ShopifyModule } from '../third-party/shopify/shopify.module';
import { Order } from './order.entity';
import { OrderReconciliationService } from './order-reconciliation.service';
import { OrderReconciliationController } from './order-reconciliation.controller';
import { TenantModule } from '@app/tenant/tenant.module';

@Module({
  imports: [TypeOrmModule.forFeature([Order]), ShopifyModule, TenantModule],
  providers: [OrderReconciliationService],
  controllers: [OrderReconciliationController],
  exports: [],
})
export class OrderModule {}
