import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  ParseUUIDPipe,
  Post,
} from '@nestjs/common';
import { Roles } from '../lib/decorators/roles.decorator';
import { Role } from '../lib/role';
import { ReconcileTenantOrdersDto } from './dto/reconcile-tenant-orders.dto';
import { OrderReconciliationService } from './order-reconciliation.service';

@Roles(Role.SUPERADMIN)
@Controller('order')
export class OrderReconciliationController {
  constructor(
    private readonly orderReconciliationService: OrderReconciliationService,
  ) {}

  /**
   * Start all reconciliation jobs
   */
  @Post('reconciliation/start')
  start() {
    this.orderReconciliationService.startReconciliationJobs();
  }

  /**
   * Stop processing all reconciliation jobs
   */
  @Post('reconciliation/stop')
  stop() {
    this.orderReconciliationService.stopReconciliationJobs();
  }

  /**
   * Queue a reconciliation job if one doesn't exist for the selected tenant.
   */
  @Post('reconciliation')
  @HttpCode(HttpStatus.ACCEPTED)
  async reconcile(@Body() body: ReconcileTenantOrdersDto) {
    return await this.orderReconciliationService.createReconciliation(body);
  }

  /**
   * Stop a reconciliation job if one exists and delete it from the queue. The status
   * will stay.
   */
  @Delete('reconciliation/:tenantId')
  @HttpCode(HttpStatus.NO_CONTENT)
  stopReconciliation(@Param('tenantId', ParseUUIDPipe) tenantId: string) {
    this.orderReconciliationService.stopReconciliation(tenantId);
  }

  /**
   * Get the results of the current or latest reconciliation job for a tenant
   */
  @Get('reconciliation/:tenantId')
  getReconciliation(@Param('tenantId', ParseUUIDPipe) tenantId: string) {
    const result = this.orderReconciliationService.findReconciliationStatus(
      tenantId,
    );
    if (!result) {
      throw new NotFoundException(
        `No reconciliation status found for tenant ${tenantId}.`,
      );
    }

    return result;
  }
}
