import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Tenant } from '@app/tenant/tenant.entity';
import { OrderReconciliationJobStatus } from './dto/order-reconciliation-job-status';
import { TenantService } from '@app/tenant/tenant.service';
import { ReconciliationJob } from './dto/reconciliation-job';
import { ShopifyService } from '../third-party/shopify/shopify.service';
import { nanoid } from 'nanoid';
import { SchedulerRegistry } from '@nestjs/schedule';
import { OrderJobFailedException } from './exceptions/order-job-failed.exception';
import {
  OrderDto,
  OrderResponseDto,
} from '../third-party/shopify/dto/order.dto';
import { ReconcileTenantOrdersDto } from './dto/reconcile-tenant-orders.dto';
import axios from 'axios';
import { InjectRepository } from '@nestjs/typeorm';
import { Order } from './order.entity';
import { In, Repository } from 'typeorm';

@Injectable()
export class OrderReconciliationService {
  private static RECONCILIATION_INTERVAL = 'reconile-orders';
  private log = new Logger(OrderReconciliationService.name, false);
  /*
   Map of tenantId to status
   This is not a great implementation. We can use it because there is only one instance of the admin service running.
   */
  private tenantJobStatuses = new Map<string, OrderReconciliationJobStatus>();

  /*
  Map of tenantId to job.
   */
  private tenantJobs = new Map<string, ReconciliationJob>();

  private isStopped = true;

  constructor(
    private readonly shopifyService: ShopifyService,
    private readonly tenantService: TenantService,
    private readonly schedulerRegistry: SchedulerRegistry,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
  ) { }

  /**
   * Dynamically start processing all jobs
   */
  startReconciliationJobs() {
    if (this.isRunningJobs()) {
      this.log.log('Reconciliation jobs already running.');
      return;
    }
    const intervalJob = () => {
      this.processAllJobs().catch(err => {
        this.log.error(`Unknown error processing reconciliation jobs. ${err}`);
        this.stopReconciliationJobs();
      });
    };

    this.isStopped = false;
    const interval = setInterval(intervalJob, 1000);
    this.schedulerRegistry.addInterval(
      OrderReconciliationService.RECONCILIATION_INTERVAL,
      interval,
    );
  }

  /**
   * Dynamically stop processing all jobs and clears all statuses.
   */
  stopReconciliationJobs() {
    if (!this.isRunningJobs()) {
      this.log.log('Reconciliation jobs already stopped.');
    } else {
      this.isStopped = true;
      this.schedulerRegistry.deleteInterval(
        OrderReconciliationService.RECONCILIATION_INTERVAL,
      );
      this.log.log('Stopping reconciliation jobs');

      for (const [tenantId, job] of this.tenantJobs) {
        // Only delete unlocked jobs. Locked jobs will cleanup themselves if isStopped is true
        if (!job.locked) {
          this.deleteJobAndStatus(tenantId);
        }
      }
    }
  }

  async createReconciliation(
    dto: ReconcileTenantOrdersDto,
  ): Promise<OrderReconciliationJobStatus> {
    const { tenantId, sinceDate, untilDate } = dto;
    const tenant: Tenant = await this.tenantService.findOne(tenantId);
    if (!tenant) {
      throw new NotFoundException(`Cannot find tenant with ID ${tenantId}`);
    }

    const id = nanoid();
    const status: OrderReconciliationJobStatus = {
      jobId: id,
      tenantId,
      processedCount: 0,
      startedAt: new Date().toISOString(),
      finishedAt: undefined,
      ordersWithError: [],
    };

    const pageParams = `limit=50&status=any${sinceDate ? `&created_at_min=${sinceDate}` : ''
      }${untilDate ? `&created_at_max=${untilDate}` : ''}`;
    const fields =
      'fields=id,cart_token,created_at,updated_at,discount_codes,note_attributes' +
      ',total_discounts,total_line_items_price,total_price,subtotal_price,customer';

    const job: ReconciliationJob = {
      id,
      tenantId,
      page: `orders.json?${pageParams}&${fields}`,
      locked: false,
    };
    this.stopReconciliation(tenantId);
    this.tenantJobStatuses.set(tenantId, status);
    this.tenantJobs.set(tenantId, job);

    return status;
  }

  stopReconciliation(tenantId: string) {
    if (this.tenantJobs.has(tenantId)) {
      this.stopJob(this.tenantJobs.get(tenantId), `Manually stopped.`);
    }
  }

  findReconciliationStatus(tenantId: string): OrderReconciliationJobStatus {
    return this.tenantJobStatuses.get(tenantId);
  }

  async processAllJobs() {
    for (const [tenantId, job] of this.tenantJobs) {
      if (job.locked) {
        this.log.log(`Job ${job.id} for ${tenantId} is locked. Continuing.`);
        continue;
      }
      if (!job.page) {
        this.stopJob(job, 'No next page');
        continue;
      }
      this.tenantJobs.set(tenantId, { ...job, locked: true });
      this.processJob(job)
        .then(() => {
          // Jobs may be processing when we stop them
          if (this.isStopped) {
            this.deleteJobAndStatus(tenantId);
          } else if (this.tenantJobs.has(tenantId)) {
            this.tenantJobs.set(tenantId, {
              ...this.tenantJobs.get(tenantId),
              locked: false,
            });
          }
        })
        .catch(err => {
          let message = 'Unknown error.';
          if (err instanceof Error) {
            message = err.message;
          }
          this.log.error(`Failed to process job for ${tenantId}: ${message}`);
          this.stopJob(job, `Failed to process job because ${message}`);
        });
    }
  }

  /**
   * Fetch all orders since date and add them to ConvertEcom IF they used a ConvertEcom coupon
   *
   * https://shopify.dev/api/admin/rest/reference/discounts/pricerule#index-2020-04
   */
  async processJob(job: ReconciliationJob) {
    const { tenantId, page, id } = job;
    this.log.log(
      `Starting order reconciliation job ${id} for tenant ${tenantId} on page ${page}`,
    );

    const tenant = await this.tenantService.findOne(tenantId);

    if (!tenant) {
      throw new OrderJobFailedException(
        `Failed to find tenant with id ${tenantId}.`,
      );
    }
    let orders: OrderDto[];
    let linkHeader: string;
    let nextUrl: string;

    try {
      const res = await this.shopifyService.get<OrderResponseDto>(tenant, page);
      orders = res.data.orders;
      linkHeader = res.headers.link;
    } catch (e) {
      throw new OrderJobFailedException('Error reaching Shopify.');
    }
    const orderIds = orders.map(o => o.id.toString(10));

    const existingOrders = await this.orderRepository.find({
      select: ['externalOrderId'],
      where: { externalOrderId: In(orderIds) },
    });
    this.log.log(`Found ${existingOrders.length} orders`);
    const existingExternalIds = new Set(
      existingOrders.map(o => o.externalOrderId),
    );

    const missingOrders = orders.filter(
      order => !existingExternalIds.has(order.id.toString(10)),
    );
    const missingOrderCount = missingOrders.length;
    this.log.log(`Missing ${missingOrderCount} orders`);

    // Each of these orders will process a job and pull another from the queue (missingOrders)
    // until the queue is exhausted.
    const failedOrders = (await Promise.all([
      this.worker(tenantId, missingOrders),
      this.worker(tenantId, missingOrders),
      this.worker(tenantId, missingOrders),
      this.worker(tenantId, missingOrders),
      this.worker(tenantId, missingOrders),
    ])).reduce((acc, result) => [...acc, ...result], []);

    this.log.log(`Failed to process ${failedOrders.length} orders`);
    const ordersWithErrors = failedOrders.map(o => o.id.toString(10));

    if (linkHeader) {
      const parts = linkHeader.split(',');
      const nextPart = parts.find(part => part.includes('rel="next"'));
      if (!nextPart) {
        nextUrl = undefined;
      } else {
        const nextLinkRegex = /<(?:.*?)(?<nextUrl>orders\.json\?.*?)>/i;
        const result = nextLinkRegex.exec(nextPart);
        nextUrl = result.groups.nextUrl;
      }
    }

    // Force stop if processed.
    if (this.isStopped) {
      this.deleteJobAndStatus(tenantId);
      return;
    }

    const jobStatus = this.tenantJobStatuses.get(tenantId);
    // The job is now done!
    this.tenantJobStatuses.set(tenantId, {
      ...jobStatus,
      processedCount: jobStatus.processedCount + missingOrderCount,
      finishedAt: !nextUrl ? new Date().toISOString() : jobStatus.finishedAt,
      ordersWithError: [...jobStatus.ordersWithError, ...ordersWithErrors],
    });
    if (!nextUrl) {
      this.stopJob(job, 'Reached end of pages.');
    } else {
      this.log.log(`Queuing next job for page ${nextUrl}`);
      // Queue the next job
      const latestJobRef = this.tenantJobs.get(tenantId);
      if (!latestJobRef) {
        return;
      }
      this.tenantJobs.set(tenantId, { ...latestJobRef, page: nextUrl });
    }
  }

  /**
   * A little async worker. Since objects are pass by reference, and Javascript
   * is single threaded, this is a thread safe way to pull items from a queue.
   */
  private async worker(tenantId, orders: OrderDto[]) {
    const deadLetterQueue: OrderDto[] = [];
    while (orders.length !== 0) {
      const nextOrder = orders.pop();

      try {
        const response = await axios.post(
          `${process.env.CONVERTECOM_API_URL
          }/webhook/internal/${tenantId}/reconcile-order`,
          nextOrder,
          {
            headers: { 'x-convertecom-service-token': process.env.CONVERTECOM_SERVICE_TOKEN },
            timeout:
              60 *
              1000 /* 60 seconds, this call takes ~10 seconds on average, but be safe! */,
          },
        );
        if (!response.data.status) {
          this.log.error(
            `Failed to process order, status is false ${nextOrder.id}`,
          );
          deadLetterQueue.push(nextOrder);
        }
      } catch (e) {
        this.log.error(
          `Failed to process order, some error ${nextOrder.id
          }. ${JSON.stringify(e)}`,
        );
        deadLetterQueue.push(nextOrder);
      }
      // wait 0 - 250 ms to process the next item.
      await new Promise(res => setTimeout(res, Math.ceil(Math.random() * 250)));
    }

    return deadLetterQueue;
  }

  /**
   * Dequeues the current job if it exists
   */
  private stopJob(job: ReconciliationJob, reason: string) {
    const { id, tenantId } = job;
    this.log.log(`Stopping job ${id} for tenant ${tenantId}: ${reason}`);
    if (this.tenantJobs.has(tenantId)) {
      this.log.log(`Deleting job ${job.id} for tenant ${tenantId}`);
      this.tenantJobs.delete(tenantId);
    }
    if (this.tenantJobStatuses.has(tenantId)) {
      const status = this.tenantJobStatuses.get(tenantId);
      if (status.finishedAt) {
        this.log.log(`Job ${id} for tenant ${tenantId} was already stopped.`);
      } else {
        this.tenantJobStatuses.set(tenantId, {
          ...status,
          finishedAt: new Date().toISOString(),
        });
        this.log.log(`Job ${id} for tenant ${tenantId} stopped.`);
      }
    }
  }

  private deleteJobAndStatus(tenantId: string) {
    this.tenantJobs.delete(tenantId);
    this.tenantJobStatuses.delete(tenantId);
  }

  /**
   * Helper to check if the interval is running already
   */
  private isRunningJobs() {
    let existingInterval: unknown;
    try {
      existingInterval = this.schedulerRegistry.getInterval(
        OrderReconciliationService.RECONCILIATION_INTERVAL,
      );
    } catch (e) {
      existingInterval = undefined;
    }

    return !!existingInterval;
  }
}
