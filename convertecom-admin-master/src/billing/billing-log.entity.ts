import { Entity, OneToOne, CreateDateColumn, UpdateDateColumn, PrimaryGeneratedColumn, Column } from 'typeorm';
import { Billing } from './billing.entity';

@Entity()
export class BillingLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @OneToOne(type => Billing)
  billing: Billing;

  @Column()
  status: string;

  @Column({ type: 'text' })
  rawResponse: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}