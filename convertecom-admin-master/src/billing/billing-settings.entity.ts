import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { BillingSettingScope } from './enums/billing-setting-scope.enum';
import { BillingSettingType } from './enums/billing-setting-type.enum';
import { BillingSettingsValue } from './interfaces/billing-settings-value.interface';

@Entity()
export class BillingSettings {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: BillingSettingScope,
  })
  scope: BillingSettingScope;

  @Column({
    type: 'enum',
    enum: BillingSettingType,
  })
  type: BillingSettingType;

  @Column({
    type: 'json',
  })
  value: BillingSettingsValue;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

}
