import { Injectable } from '@nestjs/common';
import { CreateCreditDto } from '../dto/create-credit.dto';
import { Tenant } from '../../tenant/tenant.entity';
import { CreateApplicationCreditDto } from '../../third-party/shopify/dto/create-application-credit.dto';
import { ShopifyService } from '../../third-party/shopify/shopify.service';
import { Connection, Repository } from 'typeorm';
import { CreditLog } from '../credit-log.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from '../../user/user.entity';
import { errorResult, Result, successResult } from '../../lib/result';
import { ShopifyApiErrorDto } from '../../third-party/shopify/dto/shopify-api-error.dto';

@Injectable()
export class CreditService {
  constructor(
    private readonly shopifyService: ShopifyService,
    private readonly connection: Connection,
    @InjectRepository(CreditLog)
    private readonly creditLogRepository: Repository<CreditLog>,
  ) {}
  async create(
    tenant: Tenant,
    createdBy: User,
    dto: CreateCreditDto,
  ): Promise<Result<CreditLog, string[]>> {
    const { amount, description, test } = dto;
    const shopifyDto: CreateApplicationCreditDto = {
      amount,
      description,
      test: test ? test : null,
    };

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    try {
      await queryRunner.startTransaction();
      const entity = await queryRunner.manager.save<CreditLog>(
        this.creditLogRepository.create({
          amount,
          description,
          tenant,
          createdBy,
          test: shopifyDto.test,
        }),
      );
      const { data } = await this.shopifyService.post(
        tenant,
        'application_credits.json',
        { application_credit: shopifyDto },
      );
      if (typeof data.errors !== 'undefined') {
        const { errors } = data as ShopifyApiErrorDto;
        await queryRunner.rollbackTransaction();
        return errorResult(errors.base);
      }
      const credit = data.application_credit;
      await queryRunner.manager.update(
        CreditLog,
        { id: entity.id },
        { externalId: credit.id },
      );
      await queryRunner.commitTransaction();

      return successResult(
        await queryRunner.manager.findOne(CreditLog, entity.id),
      );
    } catch (e) {
      await queryRunner.rollbackTransaction();
      return errorResult(['Failed to create credit log entry.']);
    } finally {
      await queryRunner.release();
    }
  }
}
