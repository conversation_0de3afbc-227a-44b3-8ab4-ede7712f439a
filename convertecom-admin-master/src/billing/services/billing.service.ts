import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { get } from 'lodash';
import {
  FindConditions,
  FindManyOptions,
  getConnection,
  Repository,
} from 'typeorm';
import { Tenant } from '@app/tenant/tenant.entity';
import { Billing } from '../billing.entity';
import { TenantService } from '@app/tenant/tenant.service';
import {
  BillingPeriod,
  BillingSummary,
} from '../interfaces/billing-summary.interface';
import { FindAllDto } from '../dto/find-all.dto';
import { Dir } from '../../lib/types/typeorm';
import { BillingSettingsRepository } from '../billing-settings.repository';
import { BillingSettingType } from '../enums/billing-setting-type.enum';
import { hasFeatureFlag } from '../../lib/feature-flags';
import { ShopifyService } from '../../third-party/shopify/shopify.service';
import { ShopifyBillingService } from '../../third-party/shopify/shopify-billing.service';
import { BillingSettingsSubscription } from '../interfaces/billing-settings-value.interface';
import * as moment from 'moment';
import {
  RecurringApplicationCharge,
  RecurringApplicationChargeResponse,
} from '../../third-party/shopify/dto/billing.dto';

export interface FindAndCountRes {
  total: number;
  data: Billing[];
}

@Injectable()
export class BillingService {
  private readonly shopifyBillingPeriodDays = 30;
  private readonly shopifyMonthlyFee = 0;
  constructor(
    @InjectRepository(Billing)
    private readonly billingRepository: Repository<Billing>,
    private readonly billingSettingsRepository: BillingSettingsRepository,
    private readonly tenantService: TenantService,
    private readonly shopifyService: ShopifyService,
    private readonly shopifyBillingService: ShopifyBillingService,
  ) { }

  async findAll(findAllDto: FindAllDto): Promise<FindAndCountRes> {
    let where: FindConditions<Billing> = {};

    if (findAllDto.date) {
      where.date = findAllDto.date;
    }

    if (findAllDto.tenantId) {
      where.tenantId = findAllDto.tenantId;
    }

    const params: FindManyOptions<Billing> = {
      relations: ['tenant', 'billingLog'],
      order: {
        [findAllDto.order || 'id']: (
          findAllDto.dir || 'ASC'
        ).toUpperCase() as Dir,
      },
      skip: findAllDto.skip || 0,
      take: findAllDto.take || 20,
      where,
    };

    const [data, total] = await this.billingRepository.findAndCount(params);
    return { data, total };
  }

  async findAllForExport(): Promise<
    { date: string; name: string; subtotalSum: number; amount: number }[]
  > {
    // Raw query so we can keep this super lean
    return await this.billingRepository.query(
      `select b.date, t.name, b."subtotalSum", b.amount from billing b join tenant t on t.id = b."tenantId"`,
    );
  }

  async findOne(id: string): Promise<Billing> {
    return await this.billingRepository.findOne(id, {
      relations: ['tenant', 'billingLog'],
    });
  }

  async getTenantBillingSummary(tenant: Tenant): Promise<BillingSummary> {
    const charge: RecurringApplicationCharge = await this.getTenantRecurringCharge(
      tenant,
    );
    let recoveredRevenue;
    // Subtract one day because the period is inclusive of the first day
    const toDate = moment(charge.billing_on)
      .subtract(1, 'days')
      .format('YYYY-MM-DD');
    const fromDate = moment(charge.billing_on)
      .subtract(this.shopifyBillingPeriodDays, 'days')
      .format('YYYY-MM-DD');
    // If a tenant has cancelled billing, charge.billing_on could be null
    if (
      hasFeatureFlag('billingRecoveredRevenue', tenant.featureFlags) &&
      charge.billing_on
    ) {
      recoveredRevenue = await this.calculateRecoveredRevenueForBillingPeriod(
        tenant.id,
        fromDate,
        toDate,
      );
    }

    const billingPeriods = this.buildBillingPeriods(
      charge.trial_ends_on,
      charge.billing_on,
    );
    const currentPeriod = billingPeriods[0];
    return {
      billingPeriods,
      recoveredRevenue,
      ...currentPeriod,
      billingCycle: `Period ending ${toDate}`,
      monthlyFee: this.getMonthlyFeeByDate(fromDate),
      usageRate: tenant.billingPercentage,
      pendingUsage: charge.balance_used,
    };
  }

  async getHistoricTenantBillingSummary(tenant: Tenant, billingPeriod: number) {
    const charge = await this.getTenantRecurringCharge(tenant);

    const maxPeriods = Math.ceil(
      Math.abs(
        moment(charge.trial_ends_on).diff(moment(charge.billing_on), 'days'),
      ) / this.shopifyBillingPeriodDays,
    );
    if (billingPeriod > maxPeriods || billingPeriod < 1) {
      throw new BadRequestException(
        `${billingPeriod} is out of the billing period range.`,
      );
    }
    /*
    Calculate the from/to dates on the billing period from trial_ends_on + (30 days * period)
    From date: trial_ends_on + (30 * (period - 1))
    To Date: trial_ends_on + (30 * period) - 1
    Next billing date: trial_ends_on + (30 * period)
    */
    const fromDate = moment(charge.trial_ends_on).add(
      this.shopifyBillingPeriodDays * (billingPeriod - 1),
      'days',
    );
    const toDate = moment(charge.trial_ends_on)
      .add(this.shopifyBillingPeriodDays * billingPeriod, 'days')
      .subtract('1', 'days');

    const recoveredRevenue = await this.calculateRecoveredRevenueForBillingPeriod(
      tenant.id,
      fromDate.format('YYYY-MM-DD'),
      toDate.format('YYYY-MM-DD'),
    );

    const { pendingUsage } = await this.billingRepository
      .createQueryBuilder('billing')
      .select('SUM(billing.amount)', 'pendingUsage')
      .where('billing.tenantId = :id', { id: tenant.id })
      .andWhere('billing.createdAt >= :fromDate', {
        fromDate: fromDate.format('YYYY-MM-DD'),
      })
      .andWhere('billing.createdAt <= :toDate', {
        toDate: toDate.format('YYYY-MM-DD'),
      })
      .getRawOne();

    const billingPeriods = this.buildBillingPeriods(
      charge.trial_ends_on,
      charge.billing_on,
    );
    const currentPeriod = billingPeriods[billingPeriods.length - billingPeriod];

    return {
      ...currentPeriod,
      billingPeriods,
      recoveredRevenue,
      pendingUsage: pendingUsage || 0,
      billingCycle: `Period ending ${toDate.format('YYYY-MM-DD')}`,
      monthlyFee: this.getMonthlyFeeByDate(fromDate),
      usageRate: tenant.billingPercentage,
    };
  }

  async createShopifyRecurringCharge(
    tenant: Tenant,
  ): Promise<{ redirectUrl: string }> {
    const billingSettings = await this.billingSettingsRepository.globalSettings(
      BillingSettingType.SUBSCRIPTION,
    );

    const test = hasFeatureFlag('billingTest', tenant.featureFlags)
      ? true
      : null;

    const trialDays = this.trialDaysForTenant(tenant, billingSettings.value);
    const data = {
      recurring_application_charge: {
        name: 'ConvertEcom Usage',
        test,
        price: this.shopifyMonthlyFee,
        capped_amount: 7500,
        terms: '1.5% service fee on all orders using a ConvertEcom discount.',
        return_url: `${process.env.BASE_URL}/billing/setup/shopify/redirect?t=${tenant.id
          }`,
        trial_days: trialDays,
      },
    };
    const response = await this.shopifyService.post(
      tenant,
      'recurring_application_charges.json',
      data,
    );

    const redirectUrl = get(
      response,
      'data.recurring_application_charge.confirmation_url',
      null,
    );
    return { redirectUrl };
  }

  async handleBillingSetupRedirect(
    tenantId: string,
    recurringChargeId: string,
  ): Promise<{ redirectUrl: string }> {
    const tenant = await this.tenantService.findOne(tenantId);
    if (!tenant) {
      throw new BadRequestException('Invalid tenant');
    }

    const response = await this.shopifyBillingService.activateRecurringCharges(
      tenant,
      recurringChargeId,
    );

    const billingCredentials = {
      platform: 'shopify',
      externalId: recurringChargeId,
    };

    const trialEndsOn = get(
      response,
      'data.recurring_application_charge.trial_ends_on',
      null,
    );

    const { onboardingStatus } = tenant;

    await this.tenantService.update(tenant.id, {
      billingCredentials,
      onboardingStatus: { ...onboardingStatus, isBillingSetup: true },
      // Tenant does not get another trial if they had one already
      trialEndsOn: tenant.trialEndsOn ? tenant.trialEndsOn : trialEndsOn,
    });

    return { redirectUrl: `${process.env.UI_URL}/settings` };
  }

  private trialDaysForTenant(
    tenant: Tenant,
    billingSettings: BillingSettingsSubscription,
  ) {
    if (
      tenant.id === 'de093d83-15fd-4049-be93-a245f33b22ce' ||
      tenant.id === '5a2d52eb-7c07-497b-88a4-edeb5b41581f'
    ) {
      // Special setting for votcbrand store
      return 30;
    }
    const billingSettingTrialDays = get(billingSettings, 'trialDays', 0);
    const tenantHadTrial = !!tenant.trialEndsOn;

    return tenantHadTrial ? 0 : billingSettingTrialDays;
  }

  /**
   * Calculate a tenant's recovered revenue for a billing period. Shopify billing period is 30 days.
   */
  private async calculateRecoveredRevenueForBillingPeriod(
    tenantId: string,
    fromDate: string,
    toDate: string,
  ): Promise<number> {
    /*
    Recovered revenue is the difference between the industry average discount (15%) the tenant would've discounted on ConvertEcom orders and the
    amount they actually discounted on ConvertEcom orders. Reference: Dashboard statistics
     */
    const [{ recoveredRevenue }] = await getConnection().query(
      `select
              coalesce((sum(case when "convertEcomDiscounts" is not null and "convertEcomDiscounts" > 0 then "subtotalPrice" else 0 end) * 0.15), 0)
                - coalesce(sum("convertEcomDiscounts"), 0) as "recoveredRevenue"
             from "order" o
             where o."tenantId" = $1
                and
                o."createdAt" >= $2 and o."createdAt" <= $3
            `,
      [tenantId, fromDate, toDate],
    );

    return parseFloat(recoveredRevenue);
  }

  private async getTenantRecurringCharge(
    tenant: Tenant,
  ): Promise<RecurringApplicationCharge> {
    const response = await this.shopifyService.get<
      RecurringApplicationChargeResponse
    >(
      tenant,
      `recurring_application_charges/${tenant.billingCredentials.externalId
      }.json`,
    );

    return response.data.recurring_application_charge;
  }

  private buildBillingPeriods(
    billingStartDate: string,
    nextBillingDate: string,
  ): BillingPeriod[] {
    const periodCount = Math.ceil(
      Math.abs(moment(billingStartDate).diff(moment(nextBillingDate), 'days')) /
      this.shopifyBillingPeriodDays,
    );
    const billingPeriods: BillingPeriod[] = [];

    let nextPeriodStart = moment(nextBillingDate);

    for (let i = periodCount; i > 0; i--) {
      const thisPeriodStart = nextPeriodStart
        .clone()
        .subtract(this.shopifyBillingPeriodDays, 'days');
      const thisPeriodEnd = nextPeriodStart.clone().subtract(1, 'days');

      billingPeriods.push({
        startDate: thisPeriodStart.format('YYYY-MM-DD'),
        endDate: thisPeriodEnd.format('YYYY-MM-DD'),
        periodNumber: i,
      });
      nextPeriodStart = thisPeriodStart;
    }

    return billingPeriods;
  }

  /**
   * The price per month changed to 0 from 29 on 4/28/2021
   * @private
   */
  public getMonthlyFeeByDate(dateToCheck: string | moment.Moment) {
    const priceSwitchDate = moment('04/28/2021');
    return moment(dateToCheck).isAfter(priceSwitchDate)
      ? this.shopifyMonthlyFee
      : 29; // TODO: Change this to isSameOrAfter
  }
}
