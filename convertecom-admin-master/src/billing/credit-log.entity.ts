import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Tenant } from '../tenant/tenant.entity';
import { User } from '../user/user.entity';

@Entity()
export class CreditLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'float',
  })
  amount: number;

  @Column()
  description: string;

  @Column()
  test: boolean;

  @Column({
    type: 'integer',
  })
  externalId: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(type => Tenant, tenant => tenant.creditLog)
  tenant: Tenant;

  @ManyToOne(type => User)
  createdBy: User;
}
