import { <PERSON>N<PERSON>ber, IsOptional, IsIn, <PERSON>Int, <PERSON>, <PERSON>, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

export class FindAllDto {
  @IsOptional()
  date: string;

  @IsOptional()
  @IsUUID('4')
  tenantId: string;

  @IsOptional()
  @IsIn(['tenantId', 'date', 'amount', 'subtotalSum', 'createdAt', 'updatedAt'])
  order: string;

  @IsOptional()
  @IsIn(['ASC', 'asc', 'DESC', 'desc'])
  dir: string;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  skip: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(50)
  take: number;
}