import { BillingSettings } from './billing-settings.entity';
import { EntityRepository, Repository } from 'typeorm';
import { BillingSettingType } from './enums/billing-setting-type.enum';
import { BillingSettingScope } from './enums/billing-setting-scope.enum';

@EntityRepository(BillingSettings)
export class BillingSettingsRepository extends Repository<BillingSettings> {
  async globalSettings(type: BillingSettingType) {
    return await this.findOne({ type, scope: BillingSettingScope.GLOBAL });
  }
}
