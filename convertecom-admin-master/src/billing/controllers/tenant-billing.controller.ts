import {
  BadRequestException,
  Controller,
  Get,
  HttpStatus,
  Param,
  Query,
} from '@nestjs/common';
import { Between, EntityManager } from 'typeorm';
import { Roles } from '../../lib/decorators/roles.decorator';
import { Role } from '../../lib/role';
import { Billing } from '../billing.entity';

@Controller('tenant')
export class TenantBillingController {
  constructor(private readonly entityManager: EntityManager) {}

  @Roles(Role.SUPERADMIN)
  @Get(':tenantId/billing-history')
  async tenantBillingSummary(
    @Param('tenantId') tenantId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const parsedStart = this.parseDate(startDate);
    const parsedEnd = this.parseDate(endDate);

    if ((parsedStart && !parsedEnd) || (parsedEnd && !parsedStart)) {
      throw new BadRequestException(
        'startDate and endDate are required together',
        HttpStatus.BAD_REQUEST.toString(),
      );
    }
    if (parsedStart > parsedEnd) {
      throw new BadRequestException(
        'start date is after end date',
        HttpStatus.BAD_REQUEST.toString(),
      );
    }

    const wheres: { [key: string]: any } = {
      tenantId,
    };
    if (parsedStart) {
      wheres.createdAt = Between(parsedStart, parsedEnd);
    }

    const repository = await this.entityManager.getRepository(Billing);

    let query = repository
      .createQueryBuilder('b')
      .where('b.tenantId = :tenantId', { tenantId });
    if (parsedStart) {
      query = query
        .andWhere('b.createdAt >= :startDate', { startDate: parsedStart })
        .andWhere('b.createdAt <= :endDate', { endDate: parsedEnd });
    } else {
      query = query
        .andWhere(
          "b.createdAt >= date_trunc('month', CURRENT_DATE) - '1 year'::interval",
        )
        .andWhere('b.createdAt <= CURRENT_TIMESTAMP');
    }

    const results: Array<{ month: string; billed_amount: number }> = await query
      .select(
        "date_trunc('month', b.createdAt) as month, COALESCE(sum(b.amount), 0) as billed_amount",
      )
      .groupBy("date_trunc('month', b.createdAt)")
      .orderBy('month', 'DESC')
      .getRawMany();

    return {
      data: {
        tenantId,
        monthlyBillingHistory: results.map(({ month, billed_amount }) => ({
          month,
          billedAmount: billed_amount,
        })),
      },
    };
  }

  private parseDate(value?: string) {
    if (!value) {
      return undefined;
    }
    const maybeDate = Date.parse(value);
    if (isNaN(maybeDate)) {
      throw new BadRequestException(
        `${value} is not a valid date.`,
        HttpStatus.BAD_REQUEST.toString(),
      );
    }
    return new Date(maybeDate);
  }
}
