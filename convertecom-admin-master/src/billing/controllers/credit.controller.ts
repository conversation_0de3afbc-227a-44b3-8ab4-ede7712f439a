import {
  Body,
  Controller,
  Get,
  InternalServerErrorException,
  NotFoundException,
  Param,
  Post,
} from '@nestjs/common';
import { CreateCreditDto } from '../dto/create-credit.dto';
import { Roles } from '../../lib/decorators/roles.decorator';
import { Role } from '../../lib/role';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Tenant } from '../../tenant/tenant.entity';
import { AuthUser } from '../../auth/decorators/auth-user.decorator';
import { User } from '../../user/user.entity';
import { CreditService } from '../services/credit.service';
import { isErrorResult } from '../../lib/result';
import { CreditLog } from '../credit-log.entity';

@Controller('')
@Roles(Role.SUPERADMIN)
export class CreditController {
  constructor(
    @InjectRepository(Tenant)
    readonly tenantRepository: Repository<Tenant>,
    readonly creditService: CreditService,
    @InjectRepository(CreditLog)
    readonly creditLogRepository: Repository<CreditLog>,
  ) {}

  @Post('credit')
  async create(@AuthUser() user: User, @Body() body: CreateCreditDto) {
    const { tenantId } = body;
    const tenant = await this.tenantRepository.findOne(tenantId);

    if (!tenant) {
      throw new NotFoundException();
    }

    const result = await this.creditService.create(tenant, user, body);
    if (isErrorResult(result)) {
      throw new InternalServerErrorException(result.error.join('. '));
    }
    return { data: result.value };
  }

  @Get('tenant/:tenantId/credit-log')
  async forTenant(@Param('tenantId') tenantId: string) {
    const tenant = await this.tenantRepository.findOne(tenantId);
    if (typeof tenant === 'undefined') {
      throw new NotFoundException();
    }
    return { data: await tenant.creditLog };
  }
}
