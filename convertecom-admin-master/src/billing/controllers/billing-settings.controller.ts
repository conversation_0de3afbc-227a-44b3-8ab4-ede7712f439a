import { BadRequestException, Body, Controller, Get, HttpCode, HttpStatus, NotFoundException, Param, Patch } from '@nestjs/common';
import { BillingSettingType } from '../enums/billing-setting-type.enum';
import { BillingSettingsSubscription } from '../interfaces/billing-settings-value.interface';
import { validateOrReject } from 'class-validator';
import { UpdateBillingSettingDto } from '../dto/update-billing-setting.dto';
import { BillingSettingScope } from '../enums/billing-setting-scope.enum';
import { Roles } from '../../lib/decorators/roles.decorator';
import { BillingSettingsRepository } from '../billing-settings.repository';
import { Role } from '../../lib/role';

@Controller('billing-settings')
@Roles(Role.SUPERADMIN, Role.FF_DEV)
export class BillingSettingsController {
  constructor(
    readonly billingSettingsRepository: BillingSettingsRepository,
  ) {}

  @Get()
  all() {
    return this.billingSettingsRepository.find();
  }

  @Get(':id')
  view(@Param('id') id: string) {
    return this.billingSettingsRepository.findOne({ id });
  }

  @Patch(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async update(@Param('id') id: string, @Body() body: UpdateBillingSettingDto) {
    // TODO: Move this to a service if it grows
    const settings = await this.billingSettingsRepository.findOne({ id });
    if (typeof settings === 'undefined') {
      throw new NotFoundException();
    }
    if (settings.scope !== BillingSettingScope.GLOBAL) {
      throw new BadRequestException('Tenant billing settings are read-only.');
    }
    if (settings.type === BillingSettingType.SUBSCRIPTION) {
      try {
        const value = new BillingSettingsSubscription();
        value.trialDays = body.value.trialDays;
        await validateOrReject(value);
        await this.billingSettingsRepository.update({ id }, { value });
      } catch (e) {
        throw new BadRequestException();
      }
    } else {
      throw new BadRequestException();
    }
  }
}
