import {
  CACHE_MANAGER,
  Controller,
  Get,
  Header,
  Inject,
  Param,
  Post,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { BillingService } from '../services/billing.service';
import { Roles } from '../../lib/decorators/roles.decorator';
import { FindAllDto } from '../dto/find-all.dto';
import { SetupShopifyRedirectDto } from '../dto/setup-shopify-redirect.dto';
import { parse } from 'json2csv';
import { Role } from '../../lib/role';
import { Cache } from 'cache-manager';

@Controller('billing')
export class BillingController {
  constructor(
    private readonly billingService: BillingService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  @Get('summary')
  // Re-enable caching once we can cache by query param
  // @CacheTTL(600)
  // @CacheByQueryParams('billingPeriod')
  // @UseInterceptors(TenantAwareCacheInterceptor)
  getLatestTenantBillingSummary(
    @Req() req,
    @Query('billingPeriod') billingPeriod?: string,
  ) {
    let billingPeriodAsInt;
    if (billingPeriod) {
      billingPeriodAsInt = parseInt(billingPeriod, 10);
    }

    return Number.isInteger(billingPeriodAsInt)
      ? this.billingService.getHistoricTenantBillingSummary(
          req.authUser.tenant,
          billingPeriodAsInt,
        )
      : this.billingService.getTenantBillingSummary(req.authUser.tenant);
  }

  @Roles(Role.SUPERADMIN)
  @Get('export')
  @Header('Content-Type', 'text/csv')
  async export() {
    const logs = await this.billingService.findAllForExport();

    if (logs.length === 0) {
      return '';
    }

    return parse(logs);
  }

  // TODO ability to pass params for sorting/paging

  @Roles(Role.SUPERADMIN)
  @Get()
  async findAll(@Req() req, @Query() findAllDto: FindAllDto) {
    return await this.billingService.findAll(findAllDto);
  }

  @Post('setup/shopify')
  async setupBilling(@Req() req) {
    return await this.billingService.createShopifyRecurringCharge(
      req.authUser.tenant,
    );
  }

  @Get('setup/shopify/redirect')
  async handleBillingSetupRedirect(
    @Query() queryDto: SetupShopifyRedirectDto,
    @Res() res,
  ) {
    const {
      redirectUrl,
    } = await this.billingService.handleBillingSetupRedirect(
      queryDto.t,
      queryDto.charge_id,
    );
    return res.redirect(redirectUrl);
  }

  @Roles(Role.SUPERADMIN)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.billingService.findOne(id);
  }
}
