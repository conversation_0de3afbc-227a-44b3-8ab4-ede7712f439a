import { Modu<PERSON> } from '@nestjs/common';
import { BillingController } from './controllers/billing.controller';
import { BillingService } from './services/billing.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Billing } from './billing.entity';
import { TenantModule } from '../tenant/tenant.module';
import { BillingSettingsController } from './controllers/billing-settings.controller';
import { BillingSettingsRepository } from './billing-settings.repository';
import { CacheModule } from '../lib/cache/cache.module';
import { ShopifyModule } from '../third-party/shopify/shopify.module';
import { CreditLog } from './credit-log.entity';
import { CreditService } from './services/credit.service';
import { CreditController } from './controllers/credit.controller';
import { TenantBillingController } from './controllers/tenant-billing.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([Billing, BillingSettingsRepository, CreditLog]),
    TenantModule,
    CacheModule,
    ShopifyModule,
  ],
  controllers: [
    BillingController,
    BillingSettingsController,
    CreditController,
    TenantBillingController,
  ],
  providers: [BillingService, CreditService],
  exports: [BillingService],
})
export class BillingModule {}
