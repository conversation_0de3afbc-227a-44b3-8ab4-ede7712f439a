import { Controller, Get, Query, Req } from '@nestjs/common';
import { QueryStringDto } from './dto/query-string.dto';
import { DashboardStatistics } from './interfaces/dashboard-statistics.interface';
import { StatisticService } from './statistic.service';
import { Tenant } from '@app/tenant/tenant.entity';
import { DashboardStatisticsService } from './dashboard-statistics.service';

@Controller('statistic')
export class StatisticController {
  constructor(
    private readonly dashboardStatisticsService: DashboardStatisticsService,
    private readonly statisticService: StatisticService,
  ) {}

  @Get('dashboard')
  async getDashboardStatistics(
    @Req() req,
    @Query() queryString: QueryStringDto,
  ) {
    const tenant: Tenant = req.authUser.tenant;
    let data: DashboardStatistics;
    if (tenant) {
      data = await this.dashboardStatisticsService.getDashboardStatistics(
        tenant,
        queryString,
      );
    } else {
      data = await this.dashboardStatisticsService.getSuperAdminDashboardStats(
        queryString,
      );
    }
    return { data };
  }

  @Get('reports')
  async getReportStatistics(@Req() req, @Query() queryString: QueryStringDto) {
    const tenant = req.authUser.tenant;
    const { startDate, endDate, interval } = queryString;

    const data = await this.statisticService.getReportStatistics(
      tenant,
      startDate,
      endDate,
      interval,
    );
    return { data };
  }
}
