import { Test, TestingModule } from '@nestjs/testing';
import { StatisticController } from './statistic.controller';
import { StatisticService } from './statistic.service';
import { DashboardStatisticsService } from './dashboard-statistics.service';

describe('Statistic Controller', () => {
  let controller: StatisticController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [StatisticController],
      providers: [StatisticService, DashboardStatisticsService]
    }).compile();

    controller = module.get<StatisticController>(StatisticController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
