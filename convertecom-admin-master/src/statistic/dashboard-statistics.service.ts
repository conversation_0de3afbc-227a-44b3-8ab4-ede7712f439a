import { Injectable } from '@nestjs/common';
import { Tenant } from '@app/tenant/tenant.entity';
import {
  DashboardImpressionsStats,
  DashboardOptinOrderStats,
  DashboardOrderStats,
  DashboardStatistics,
} from './interfaces/dashboard-statistics.interface';
import { getConnection } from 'typeorm';
import { QueryStringDto } from './dto/query-string.dto';
import { withWheres } from '../lib/util/database';
import { plainToClass } from 'class-transformer';
import { safeDivideByZero } from '../lib/math';

@Injectable()
export class DashboardStatisticsService {
  async getDashboardStatistics(
    tenant: Tenant,
    query: QueryStringDto,
  ): Promise<DashboardStatistics> {
    const orderStats = await this.getOrderStats(query, tenant.id);
    const impressionStats = await this.getImpressionStats(query, tenant.id);
    const optinOrderStats = await this.getOptinOrderStats(query, tenant.id);

    return DashboardStatisticsService.calculateDashboardStatistics(
      orderStats,
      impressionStats,
      optinOrderStats,
    );
  }

  async getSuperAdminDashboardStats(
    query: QueryStringDto,
  ): Promise<DashboardStatistics> {
    const orderStats = await this.getOrderStats(query, undefined);
    const impressionStats = await this.getImpressionStats(query, undefined);
    const optinOrderStats = await this.getOptinOrderStats(query, undefined);

    return DashboardStatisticsService.calculateDashboardStatistics(
      orderStats,
      impressionStats,
      optinOrderStats,
    );
  }

  /**
   * Get the fixed order statistics based only on ConvertEcom orders, not all orders
   * @param query
   * @param tenantId
   */
  async getOrderStats(
    query: QueryStringDto,
    tenantId: string,
  ): Promise<DashboardOrderStats> {
    const wheres = withWheres();
    if (tenantId) {
      wheres.add('o."tenantId" =', tenantId);
    }
    if (query.startDate) {
      wheres.add('o."createdAt" >=', query.startDate);
    }
    if (query.endDate) {
      wheres.add('o."createdAt" <=', query.endDate);
    }
    const convertEcomDiscountsExists =
      '"convertEcomDiscounts" is not null and "convertEcomDiscounts" > 0';

    const [orderStats] = await getConnection().query(
      `
      select
        coalesce((sum(case when ${convertEcomDiscountsExists} then "subtotalPrice" else 0 end) * 0.15), 0) as "legacyDiscountSum",
        coalesce(sum("convertEcomDiscounts"), 0) as "convertEcomDiscountSum",
        coalesce(sum(case when ${convertEcomDiscountsExists} then "subtotalPrice" else 0 end), 0) as "convertEcomSubtotalSum",
        coalesce(sum("subtotalPrice"), 0) as "storeSubtotalSum",
        coalesce((sum(case when ${convertEcomDiscountsExists} then "subtotalPrice" else 0 end) * 0.15), 0)
          - coalesce(sum("convertEcomDiscounts"), 0) as "recoveredRevenue",
        count(o.id) as "totalOrders",
        count(distinct "externalCustomerId") as "uniqueCustomers",
        coalesce(sum(case when ${convertEcomDiscountsExists} then 1 else 0 end), 0) "totalConvertEcomOrders",
        (coalesce(sum(case when ${convertEcomDiscountsExists} then (tenant."billingPercentage" * "subtotalPrice") else 0 end), 0)) "totalCostOfConvertEcom"
      from "order" o
      join tenant on tenant.id = o."tenantId"
      ${wheres.getPreparedStatement()}
    `,
      wheres.getParameters(),
    );

    return orderStats;
  }

  async getImpressionStats(
    query: QueryStringDto,
    tenantId: string,
  ): Promise<DashboardImpressionsStats> {
    const wheres = withWheres();
    if (tenantId) {
      wheres.add('i."tenantId" =', tenantId);
    }
    if (query.startDate) {
      wheres.add('i."createdAt" >=', query.startDate);
    }
    if (query.endDate) {
      wheres.add('i."createdAt" <=', query.endDate);
    }
    const [impressionStats] = await getConnection().query(
      `
      select count(i.id) as "totalVisitors" from impression i
        ${wheres.getPreparedStatement()}
      `,
      wheres.getParameters(),
    );
    return impressionStats;
  }

  async getOptinOrderStats(
    query: QueryStringDto,
    tenantId: string,
  ): Promise<DashboardOptinOrderStats> {
    const wheres = withWheres();
    if (tenantId) {
      wheres.add('opt."tenantId" =', tenantId);
    }
    if (query.startDate) {
      wheres.add('opt."createdAt" >=', query.startDate);
    }
    if (query.endDate) {
      wheres.add('opt."createdAt" <=', query.endDate);
    }

    // You have to use `t` as the alias because it's set in the "wheres"
    const [optinOrderStats] = await getConnection().query(
      `
        select count(distinct opt.email) as "uniqueVisitorCustomers",
        count(opt.id) "totalVisitorOptins",
        count(o.id) "totalVisitorOrders"
      from optin opt
      left join "order" o on o."optinId" = opt.id
      ${wheres.getPreparedStatement()}
    `,
      wheres.getParameters(),
    );

    return optinOrderStats;
  }

  static calculateDashboardStatistics(
    orderStats: DashboardOrderStats,
    impressionStats: DashboardImpressionsStats,
    optinOrderStats: DashboardOptinOrderStats,
  ): DashboardStatistics {
    const {
      totalOrders,
      totalConvertEcomOrders,
      uniqueCustomers,
      recoveredRevenue,
      totalCostOfConvertEcom,
    } = orderStats;
    const {
      totalVisitorOptins,
      uniqueVisitorCustomers,
      totalVisitorOrders,
    } = optinOrderStats;
    const { totalVisitors } = impressionStats;
    const visitorConversionRate = safeDivideByZero(
      totalVisitorOptins,
      totalVisitors,
    );
    const orderConversionRate = safeDivideByZero(totalConvertEcomOrders, totalOrders);
    const uniqueCustomerConversionRate = safeDivideByZero(
      uniqueVisitorCustomers,
      uniqueCustomers,
    );
    const pipelineConversionRate = safeDivideByZero(
      totalVisitorOrders,
      totalVisitors,
    );
    const roiYieldPercentage = safeDivideByZero(
      recoveredRevenue - totalCostOfConvertEcom,
      totalCostOfConvertEcom,
    );

    return plainToClass(
      DashboardStatistics,
      {
        ...orderStats,
        ...impressionStats,
        ...optinOrderStats,
        visitorConversionRate,
        orderConversionRate,
        uniqueCustomerConversionRate,
        pipelineConversionRate,
        roiYieldPercentage,
      },
      { excludeExtraneousValues: true },
    );
  }
}
