import { Test, TestingModule } from '@nestjs/testing';
import { DashboardStatisticsService } from './dashboard-statistics.service';
import {
  DashboardImpressionsStats,
  DashboardOptinOrderStats,
  DashboardOrderStats,
  DashboardStatistics,
} from './interfaces/dashboard-statistics.interface';
import { plainToClass } from 'class-transformer';

describe('DashboardStatisticsService', () => {
  let service: DashboardStatisticsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [DashboardStatisticsService],
    }).compile();

    service = module.get(DashboardStatisticsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('#calculateDashboardStatistics', () => {
    it('calculates statistics', () => {
      const orderStats: DashboardOrderStats = {
        legacyDiscountSum: 450,
        convertEcomDiscountSum: 500,
        convertEcomSubtotalSum: 3000,
        storeSubtotalSum: 4000,
        recoveredRevenue: 50,
        totalOrders: 100,
        uniqueCustomers: 70,
        totalConvertEcomOrders: 70,
        totalCostOfConvertEcom: 3000 * .025,
      };
      const impressionStats: DashboardImpressionsStats = {
        totalVisitors: 400,
      };
      const optinOrderStats: DashboardOptinOrderStats = {
        uniqueVisitorCustomers: 70,
        totalVisitorOptins: 120,
        totalVisitorOrders: 70,
      };

      const expectedResults: DashboardStatistics = plainToClass<DashboardStatistics, DashboardStatistics>(DashboardStatistics, {
        legacyDiscountSum: 450,
        convertEcomDiscountSum: 500,
        convertEcomSubtotalSum: 3000,
        storeSubtotalSum: 4000,
        recoveredRevenue: 50,
        totalOrders: 100,
        uniqueCustomers: 70,
        totalConvertEcomOrders: 70,
        totalVisitors: 400,
        uniqueVisitorCustomers: 70,
        totalVisitorOptins: 120,
        totalVisitorOrders: 70,
        visitorConversionRate: 0.3,
        orderConversionRate: 0.7,
        uniqueCustomerConversionRate: 1,
        pipelineConversionRate: 0.175,
        roiYieldPercentage: -1 / 3,
      });

      const result = DashboardStatisticsService.calculateDashboardStatistics(orderStats, impressionStats, optinOrderStats);
      expect(result).toEqual(expectedResults);
    });
  });

});
