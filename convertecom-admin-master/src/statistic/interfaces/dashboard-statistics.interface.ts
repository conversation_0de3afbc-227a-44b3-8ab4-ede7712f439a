import { Expose, Transform } from 'class-transformer';
import { transformToFloat, transformToInt } from '../../lib/util/class-transforms';

export interface DashboardOrderStats {
  // Sum of all ConvertEcom orders multiplied by 0.15
  legacyDiscountSum: number;
  // Sum of all ConvertEcom discounts
  convertEcomDiscountSum: number;
  // Sum of all subtotal prices for ConvertEcom orders
  convertEcomSubtotalSum: number;
  // Sum of all order subtotals
  storeSubtotalSum: number;
  // legacyDiscountSum - convertEcomDiscountSum, but in the database
  recoveredRevenue: number;
  // Count of all orders
  totalOrders: number;
  // Count of unique customers
  uniqueCustomers: number;
  // Count of all ConvertEcom orders
  totalConvertEcomOrders: number;
  // How much the tenant has been charged for using ConvertEcom
  totalCostOfConvertEcom: number;
}

export interface DashboardImpressionsStats {
  totalVisitors: number;
}

export interface DashboardOptinOrderStats {
  uniqueVisitorCustomers: number;
  totalVisitorOptins: number;
  totalVisitorOrders: number;
}

export class DashboardStatistics {
  @Expose()
  @Transform(transformToFloat)
  legacyDiscountSum: number;
  @Expose()
  @Transform(transformToFloat)
  convertEcomDiscountSum: number;
  @Expose()
  @Transform(transformToFloat)
  convertEcomSubtotalSum: number;
  @Expose()
  @Transform(transformToFloat)
  storeSubtotalSum: number;
  @Expose()
  @Transform(transformToFloat)
  recoveredRevenue: number;
  @Expose()
  @Transform(transformToInt)
  totalOrders: number;
  @Expose()
  @Transform(transformToInt)
  uniqueCustomers: number;
  @Expose()
  @Transform(transformToInt)
  totalConvertEcomOrders: number;
  @Expose()
  @Transform(transformToInt)
  totalVisitors: number;
  @Expose()
  @Transform(transformToInt)
  uniqueVisitorCustomers: number;
  @Expose()
  @Transform(transformToInt)
  totalVisitorOptins: number;
  @Expose()
  @Transform(transformToInt)
  totalVisitorOrders: number;
  @Expose()
  @Transform(transformToFloat)
  visitorConversionRate: number;
  @Expose()
  @Transform(transformToFloat)
  orderConversionRate: number;
  @Expose()
  @Transform(transformToFloat)
  uniqueCustomerConversionRate: number;
  @Expose()
  @Transform(transformToFloat)
  pipelineConversionRate: number;
  @Expose()
  @Transform(transformToFloat)
  roiYieldPercentage: number;
}
