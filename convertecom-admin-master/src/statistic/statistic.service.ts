import { Injectable } from '@nestjs/common';
import { EntityManager, getConnection } from 'typeorm';
import * as moment from 'moment-timezone';
import { concat } from 'lodash';
import { Tenant } from '../tenant/tenant.entity';
import { DashboardStatistics } from './interfaces/dashboard-statistics.interface';
import { Billing } from '../billing/billing.entity';

@Injectable()
export class StatisticService {
  constructor(private readonly entityManager: EntityManager) { }
  async getReportStatistics(
    tenant: Tenant,
    startDate?: string,
    endDate?: string,
    interval?: string,
  ): Promise<{ category: string; key: string; value: number }[]> {
    const orderData = await this.getOrderStatistics(
      tenant,
      startDate,
      endDate,
      interval,
    );
    const optinData = await this.getOptinStatistics(
      tenant,
      startDate,
      endDate,
      interval,
    );
    return concat(orderData, optinData);
  }

  async getOrderStatistics(
    tenant: Tenant,
    startDate?: string,
    endDate?: string,
    interval?: string,
  ): Promise<{ category: string; key: string; value: number }[]> {
    const seriesCount = this.getSeriesCount(
      tenant,
      startDate,
      endDate,
      interval,
    );
    const format = this.getFormat(interval);

    const params = [interval, format, seriesCount];
    const wheres = [];

    if (tenant) {
      params.push(tenant.id);
      wheres.push(`o."tenantId" = $${params.length}`);
    }

    if (startDate) {
      params.push(startDate);
      wheres.push(
        `(o."createdAt" is null or o."createdAt" >= $${params.length})`,
      );
    }

    if (endDate) {
      params.push(endDate);
      wheres.push(
        `(o."createdAt" is null or o."createdAt" <= $${params.length})`,
      );
    }

    const query = `select
        d.date,
        count(o.id) "orderCount",
        sum(case when o."convertEcomDiscounts" is not null and o."convertEcomDiscounts" > 0 then 1 else 0 end) "convertEcomOrderCount",
        sum(case when o."subtotalPrice" is not null then o."subtotalPrice" else 0 end) "subtotalSum",
        sum(case when o."convertEcomDiscounts" is not null and o."convertEcomDiscounts" > 0 then o."subtotalPrice" else 0 end) "billableSubtotalSum",
        sum(o."totalDiscounts") "totalDiscountSum",
        sum(o."convertEcomDiscounts") "convertEcomDiscountSum",
        sum(t."legacyDiscountPercentage" * o."subtotalPrice") "legacyDiscountSum",
        avg(case when o."totalDiscounts" is not null then o."totalDiscounts" else 0 end) "averageDiscount",
        avg(case when o."convertEcomDiscounts" is not null then o."convertEcomDiscounts" else 0 end) "averageConvertEcomDiscount",
        avg(case when o."subtotalPrice" is not null and o."subtotalPrice" > 0 then (o."convertEcomDiscounts"/o."subtotalPrice") else 0 end) "averageConvertEcomPercentage"
      from (
        select to_char(date_trunc($1, (current_timestamp - (offs || ' ' || $1)::interval)), $2) AS date
        FROM generate_series(0, $3, 1) AS offs
      ) d
      left outer join "order" o on d.date = to_char(date_trunc($1, o."createdAt"), $2)
      left join tenant t on t.id = o."tenantId"
      ${wheres.length > 0 ? 'where ' + wheres.join(' and ') : ''}
      group by d.date
      order by d.date asc`;

    const rows = await getConnection().query(query, params);

    const data = [];

    for (const row of rows) {
      const legacyDiscountSum = parseFloat(row.legacyDiscountSum || 0);
      const convertEcomDiscountSum = parseFloat(row.convertEcomDiscountSum || 0);
      const recoveredRevenue = legacyDiscountSum - convertEcomDiscountSum;
      const costOfConvertEcom = parseFloat(row.billableSubtotalSum || 0) * 0.01;
      const convertEcomYieldPercentage =
        costOfConvertEcom > 0 ? (recoveredRevenue - costOfConvertEcom) / costOfConvertEcom : 0;

      data.push({
        category: 'orderCount',
        key: row.date,
        value: parseInt(row.orderCount || 0),
      });
      data.push({
        category: 'convertEcomOrderCount',
        key: row.date,
        value: parseInt(row.convertEcomOrderCount || 0),
      });
      data.push({
        category: 'averageConvertEcomDiscount',
        key: row.date,
        value: parseFloat(row.averageConvertEcomDiscount || 0).toFixed(2),
      });
      data.push({
        category: 'averageConvertEcomPercentage',
        key: row.date,
        value: parseFloat(row.averageConvertEcomPercentage || 0),
      });
      data.push({
        category: 'totalDiscountSum',
        key: row.date,
        value: parseFloat(row.totalDiscountSum || 0).toFixed(2),
      });
      data.push({
        category: 'convertEcomDiscountSum',
        key: row.date,
        value: convertEcomDiscountSum.toFixed(2),
      });
      data.push({
        category: 'legacyDiscountSum',
        key: row.date,
        value: legacyDiscountSum.toFixed(2),
      });
      data.push({
        category: 'recoveredRevenue',
        key: row.date,
        value: recoveredRevenue.toFixed(2),
      });
      data.push({
        category: 'convertEcomYieldPercentage',
        key: row.date,
        value: convertEcomYieldPercentage,
      });
    }

    return data;
  }

  async getOptinStatistics(
    tenant: Tenant,
    startDate?: string,
    endDate?: string,
    interval?: string,
  ): Promise<{ category: string; key: string; value: number }[]> {
    const seriesCount = this.getSeriesCount(
      tenant,
      startDate,
      endDate,
      interval,
    );
    const format = this.getFormat(interval);

    const params = [interval, format, seriesCount];
    const wheres = [];

    if (tenant) {
      params.push(tenant.id);
      wheres.push(`t."tenantId" = $${params.length}`);
    }

    if (startDate) {
      params.push(startDate);
      wheres.push(
        `(t."createdAt" is null or t."createdAt" >= $${params.length})`,
      );
    }

    if (endDate) {
      params.push(endDate);
      wheres.push(
        `(t."createdAt" is null or t."createdAt" <= $${params.length})`,
      );
    }

    const visitorCounts = await this.getVisitorCountsByTimespan(params, wheres);
    const optinCounts = await this.getOptinCountsByTimespan(params, wheres);
    const orderCounts = await this.getOrderCountsByTimespan(params, wheres);

    const data = [];

    for (const row of visitorCounts) {
      data.push({
        category: 'totalVisitors',
        key: row.date,
        value: parseInt(row.totalVisitors || 0),
      });
    }

    for (const row of optinCounts) {
      data.push({
        category: 'totalOptins',
        key: row.date,
        value: parseInt(row.totalOptins || 0),
      });
      data.push({
        category: 'uniqueOptins',
        key: row.date,
        value: parseInt(row.uniqueOptins || 0),
      });
    }

    for (const row of orderCounts) {
      data.push({
        category: 'totalOrders',
        key: row.date,
        value: parseInt(row.totalOrders || 0),
      });
      data.push({
        category: 'uniqueCustomers',
        key: row.date,
        value: parseInt(row.uniqueCustomers || 0),
      });
    }

    return data;
  }

  private async getVisitorCountsByTimespan(
    params: (string | number)[],
    wheres: string[],
  ) {
    const query = `select
        d.date,
        count(t.id) as "totalVisitors"
      from (
        select to_char(date_trunc($1, (current_timestamp - (offs || ' ' || $1)::interval)), $2) AS date
        FROM generate_series(0, $3, 1) AS offs
      ) d
      left outer join "impression" t on d.date = to_char(date_trunc($1, t."createdAt"), $2)
      ${wheres.length > 0 ? 'where ' + wheres.join(' and ') : ''}
      group by d.date
      order by d.date asc`;

    return await getConnection().query(query, params);
  }

  private async getOptinCountsByTimespan(
    params: (string | number)[],
    wheres: string[],
  ) {
    const query = `select
      d.date,
      count(t.id) as "totalOptins",
      count(distinct t."email") as "uniqueOptins"
    from (
      select to_char(date_trunc($1, (current_timestamp - (offs || ' ' || $1)::interval)), $2) AS date
      FROM generate_series(0, $3, 1) AS offs
    ) d
    left outer join "optin" t on d.date = to_char(date_trunc($1, t."createdAt"), $2)
    ${wheres.length > 0 ? 'where ' + wheres.join(' and ') : ''}
    group by d.date
    order by d.date asc`;

    return await getConnection().query(query, params);
  }

  private async getOrderCountsByTimespan(
    params: (string | number)[],
    wheres: string[],
  ) {
    const query = `select
      d.date,
      count(t.id) as "totalOrders",
      count(distinct t."externalCustomerId") as "uniqueCustomers",
      count(distinct t."optinId") as "optedInOrders"
    from (
      select to_char(date_trunc($1, (current_timestamp - (offs || ' ' || $1)::interval)), $2) AS date
      FROM generate_series(0, $3, 1) AS offs
    ) d
    left outer join "order" t on d.date = to_char(date_trunc($1, t."createdAt"), $2)
    ${wheres.length > 0 ? 'where ' + wheres.join(' and ') : ''}
    group by d.date
    order by d.date asc`;

    return await getConnection().query(query, params);
  }

  private getSeriesCount(
    tenant: Tenant,
    startDate?: string,
    endDate?: string,
    interval?: string,
  ): number {
    switch (interval) {
      case 'month':
        return this.countMonthsBetween(
          startDate || (tenant ? tenant.createdAt : '2019-06-01'),
          endDate,
        );
      case 'hour':
        return 24;
      default:
        return this.countDaysBetween(
          startDate || (tenant ? tenant.createdAt : '2019-06-28'),
          endDate,
        );
    }
  }

  private getFormat(interval?: string): string {
    switch (interval) {
      case 'month':
        return 'MM/YYYY';
      case 'hour':
        return 'YYYY-MM-DD HH24:00';
      default:
        return 'YYYY-MM-DD';
    }
  }

  private countDaysBetween(
    startDate?: string | Date,
    endDate?: string | Date,
  ): number {
    const dateA = moment(startDate).startOf('day');
    const dateB = moment(endDate).endOf('day');
    return Math.abs(dateB.diff(dateA, 'days')) || 1;
  }

  private countMonthsBetween(
    startDate?: string | Date,
    endDate?: string | Date,
  ): number {
    const dateA = moment(startDate).startOf('month');
    const dateB = moment(endDate).endOf('month');
    return Math.abs(dateB.diff(dateA, 'months')) || 1;
  }
}
