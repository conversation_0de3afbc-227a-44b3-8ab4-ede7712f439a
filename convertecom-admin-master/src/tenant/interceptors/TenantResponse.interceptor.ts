import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class TenantResponseInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    return next.handle()
      .pipe(map(response => {
        // Hide apiCredentials if it's a tenant (non-user) token
        if (!request.authUser.id) {
          delete response.data.apiCredentials;
        }
        return response;
      }));
  }
}