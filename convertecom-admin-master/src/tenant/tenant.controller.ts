import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { TenantService } from './tenant.service';
import { CreateTenantDto } from './dto/create-tenant.dto';
import { UpdateTenantDto } from './dto/update-tenant.dto';
import { TenantResponseInterceptor } from './interceptors/TenantResponse.interceptor';
import { Roles } from '../lib/decorators/roles.decorator';
import { uploadFile } from './aws-upload';
import { FindAllDto } from './dto/find-all.dto';
import { classToPlain } from 'class-transformer';
import { Request } from 'express';  // Import Express' Request type
import { Multer } from 'multer';
import { Role } from '../lib/role';
import { UpdateFeatureFlagsDto } from '@app/tenant/dto/update-feature-flags.dto';

const s3Url = 'https://convertecompopupimages.s3-us-east-2.amazonaws.com/';

@Controller('tenant')
export class TenantController {
  private readonly logger = new Logger(TenantController.name, true);
  constructor(private readonly tenantService: TenantService) { }

  @Post()
  @Roles(Role.SUPERADMIN)
  async create(@Body() createTenantDto: CreateTenantDto) {
    const tenant = await this.tenantService.create(createTenantDto);
    return { data: tenant };
  }

  @Get()
  @Roles(Role.SUPERADMIN)
  async findAll(@Query() findAllDto: FindAllDto) {
    const { data, total } = await this.tenantService.findAll(findAllDto);
    return {
      total,
      data: classToPlain(data),
    };
  }

  @UseInterceptors(new TenantResponseInterceptor())
  @Patch('me')
  async update(@Body() updateTenantDto: UpdateTenantDto, @Req() req) {
    const tenant = await this.tenantService.update(
      req.authUser.tenant.id,
      updateTenantDto,
    );
    return { data: tenant };
  }

  // Get info about the tenant on the provided JWT
  @UseInterceptors(new TenantResponseInterceptor())
  @Get('me')
  async get(@Req() req: any) {
    return { data: req.authUser.tenant };
  }

  @UseInterceptors(new TenantResponseInterceptor())
  @Roles(Role.SUPERADMIN)
  @Get(':id')
  async view(@Param('id') id: string) {
    const tenant = await this.tenantService.findOne(id);
    if (typeof tenant === 'undefined') {
      throw new NotFoundException();
    }
    const latestActivationAction = await this.tenantService.latestActivationAction(
      tenant.id,
      tenant.active,
    );
    return {
      data: {
        ...classToPlain(tenant),
        latestActivationAction,
      },
    };
  }

  @Patch('fallback-coupon')
  @UseInterceptors(new TenantResponseInterceptor())
  async updateFallbackCoupon(@Body() updateFallbackCouponDto: any, @Req() req) {
    const tenant = await this.tenantService.updateFallbackCoupon(
      updateFallbackCouponDto,
      req.authUser.tenant,
    );
    return { data: classToPlain(tenant) };
  }

  @Patch('fallback-max-coupon')
  @UseInterceptors(new TenantResponseInterceptor())
  async updateFallbackMaxCoupon(
    @Body() updateFallbackMaxCouponDto: any,
    @Req() req,
  ) {
    const tenant = await this.tenantService.update(
      updateFallbackMaxCouponDto,
      req.authUser.tenant,
    );
    return { data: classToPlain(tenant) };
  }

  @Post('s3-upload')
  @UseInterceptors(FileInterceptor('file'))
  async s3Upload(@UploadedFile() file: Express.Multer.File) {
    const filename = await uploadFile(file);
    return { filename };
  }

  @Post(':id/rotate-fallback')
  @Roles(Role.SUPERADMIN)
  async rotateFallback(@Param('id') id: string) {
    await this.tenantService.queueRotatingFallbackCoupon(id);
    return 'OK';
  }

  @Patch(':id/feature-flags')
  @HttpCode(HttpStatus.NO_CONTENT)
  @Roles(Role.SUPERADMIN)
  async updateFeatureFlags(
    @Param('id') id: string,
    @Body() featureFlags: UpdateFeatureFlagsDto,
  ) {
    console.log(featureFlags);
    await this.tenantService.updateFeatureFlags(id, featureFlags);
  }
}
