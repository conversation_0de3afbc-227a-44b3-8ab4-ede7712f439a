import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Tenant } from './tenant.entity';
import { TenantService } from './tenant.service';
import { TenantController } from './tenant.controller';
import { ApiSyncQueueModule } from '../api-sync-queue/api-sync-queue.module';
import { CouponModule } from '../coupon/coupon.module';
import { AuditLogModule } from '../audit-log/audit-log.module';
import { Integration } from '../integration/entities/integration.entity';
import { CreditLog } from '../billing/credit-log.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Tenant, Integration, CreditLog]),
    ApiSyncQueueModule,
    CouponModule,
    AuditLogModule,
  ],
  providers: [TenantService],
  exports: [TenantService, TypeOrmModule],
  controllers: [TenantController],
})
export class TenantModule {}
