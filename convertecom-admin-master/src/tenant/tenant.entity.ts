import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>reate<PERSON>ate<PERSON><PERSON><PERSON>n,
  <PERSON>tity,
  <PERSON>in<PERSON>ol<PERSON>n,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiCredentials } from './interfaces/api-credentials.interface';
import { EmailPlatformCredentials } from './interfaces/email-platform-credentials.interface';
import { User } from '../user/user.entity';
import { Coupon } from '../coupon/coupon.entity';
import { Order } from '../order/order.entity';
import { Billing } from '../billing/billing.entity';
import { BillingCredentials } from './interfaces/billing-credentials.interface';
import { Template } from './interfaces/template.interface';
import { GdprLog } from '../gdpr-log/gdpr-log.entity';
import { Impression } from '../impression/impression.entity';
import {
  defaultOnboardingStatus,
  OnboardingStatus,
} from './interfaces/onboarding-status.interface';
import { TenantFeatureFlags } from './interfaces/tenant-feature-flags.interface';
import { CreditLog } from '../billing/credit-log.entity';
import { Integration } from '@app/integration/entities/integration.entity';
import moment = require('moment-timezone');

@Entity()
export class Tenant {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('varchar')
  platform: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalId: string | number;

  @Column('varchar')
  name: string;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 5,
    default: 0.12,
  })
  couponPercentage: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 5,
    default: 0.15,
  })
  legacyDiscountPercentage: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 5,
    default: 0.8,
  })
  minimumGrossPercentage: number;

  @Column({ default: 20 })
  reservationMinutes: number;

  @Column({
    type: 'json',
    nullable: true,
  })
  apiCredentials: ApiCredentials;

  @Column({
    type: 'json',
    nullable: true,
  })
  emailPlatformCredentials: EmailPlatformCredentials;

  @Column({
    type: 'json',
    nullable: true,
  })
  billingCredentials: BillingCredentials;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 7,
    default: 0.015,
  })
  billingPercentage: number;

  @Column({
    type: 'json',
    nullable: true,
  })
  template: Template;

  @Column({
    type: 'integer',
    default: 1,
  })
  queueBatchSize: number;

  @Column({
    type: 'boolean',
    default: false,
  })
  active: boolean;

  @Column({
    type: 'json',
    default: defaultOnboardingStatus,
  })
  onboardingStatus: OnboardingStatus;

  @Column({
    type: 'json',
    default: {},
  })
  featureFlags: TenantFeatureFlags;

  @Column({
    type: 'date',
    nullable: true,
  })
  trialEndsOn: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(type => User, users => users.tenant)
  users: User[];

  @OneToMany(type => Coupon, coupons => coupons.tenant)
  coupons: Coupon[];

  @OneToMany(type => Order, orders => orders.tenant)
  orders: Order[];

  @OneToMany(type => Impression, impression => impression.tenant)
  impressions: Impression[];

  @OneToMany(type => Billing, billing => billing.tenant)
  billing: Billing[];

  @OneToOne(type => Coupon, { eager: true, cascade: true })
  @JoinColumn()
  fallbackCoupon: Coupon;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 5,
    nullable: true,
  })
  fallbackCouponMaxPercentage: number;

  @OneToMany(type => GdprLog, gdprLog => gdprLog.tenant)
  gdprLogs: GdprLog[];

  @OneToMany(type => CreditLog, creditLog => creditLog.tenant)
  creditLog: Promise<CreditLog[]>;

  @OneToMany(type => Integration, integration => integration.tenant)
  integrations: Promise<Integration[]>;

  trialDaysRemaining?: number;

  @AfterLoad()
  calculateTrialDaysRemaining(): number | undefined {
    const trialEndsOn = this.trialEndsOn;
    if (!trialEndsOn || typeof trialEndsOn === 'undefined') {
      this.trialDaysRemaining = undefined;
      return;
    }
    // Add +1 because we include the day the trial ends on.
    const remainingDays =
      moment(trialEndsOn).diff(moment(Date.now()), 'days') + 1;
    this.trialDaysRemaining = remainingDays > 0 ? remainingDays : undefined;
  }
}
