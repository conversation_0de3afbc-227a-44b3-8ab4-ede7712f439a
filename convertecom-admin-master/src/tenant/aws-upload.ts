import * as AWS from 'aws-sdk';
const createDOMPurify = require('dompurify');
import { JSDOM } from 'jsdom';
const uuidv1 = require('uuid/v1');

const s3Client = new AWS.S3({
    accessKeyId: process.env.AWS_KEY,
    secretAccessKey: process.env.AWS_SECRET,
    region: 'us-east-2',
});

function resolveExtension(mimetype) {
    const extension: string = mimetype.split('/').pop();
    if (extension === 'svg+xml') {
        return 'svg';
    }
    return extension;
}

function sanitizeSvg(buffer: Buffer): Buffer {
    const window = new JSDOM('').window;
    const DOMPurify = createDOMPurify(window);
    const cleaned = DOMPurify.sanitize(buffer.toString());
    return Buffer.from(cleaned);
}

export const uploadFile = async (file: Express.Multer.File) => {
    const extension = resolveExtension(file.mimetype);
    const Body = extension === 'svg' ? sanitizeSvg(file.buffer) : file.buffer;
    const filename = uuidv1() + '.' + extension;
    const params = {
        Body,
        ACL: 'public-read',
        Bucket: 'convertecompopupimages',
        Key: filename,
        ContentType: file.mimetype,
    };
    try {
        const response = await s3Client.upload(params).promise();
        return response.Key;
    } catch (error) {
        throw error;
    }
};
