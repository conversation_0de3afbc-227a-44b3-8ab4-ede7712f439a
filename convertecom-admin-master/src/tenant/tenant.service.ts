import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  DeepPartial,
  EntityManager,
  FindManyOptions,
  FindOneOptions,
  Repository,
} from 'typeorm';
import { sampleSize } from 'lodash';
import { Tenant } from './tenant.entity';
import { ApiSyncQueueService } from '../api-sync-queue/api-sync-queue.service';
import { CouponService } from '../coupon/coupon.service';
import { floatEqual } from '../lib/math';
import { FindAllDto } from './dto/find-all.dto';
import { Dir } from '../lib/types/typeorm';
import { TenantFeatureFlags } from '@app/tenant/interfaces/tenant-feature-flags.interface';
import { AuditLogService } from '../audit-log/audit-log.service';

export interface FindAndCountRes {
  total: number;
  data: Tenant[];
}

@Injectable()
export class TenantService {
  constructor(
    @InjectRepository(Tenant)
    private readonly tenantRepository: Repository<Tenant>,
    private readonly apiSyncQueueService: ApiSyncQueueService,
    private readonly couponService: CouponService,
    private readonly entityManager: EntityManager,
    private readonly auditLogService: AuditLogService,
  ) { }

  async generateCode() {
    // TODO maybe this should be tenant-configurable?
    const availableCharacters =
      process.env.AVAILABLE_CODE_CHARACTERS || 'ABCDEFGHJKLMNPQRTUVWXY346789';
    return 'CONVERTECOM_' + sampleSize(availableCharacters, 10).join('');
  }

  async create(tenant: Partial<Tenant>): Promise<Tenant> {
    return await this.tenantRepository.save(tenant);
  }

  async update(id: string, tenant: DeepPartial<Tenant>): Promise<Tenant> {
    const existing = await this.findOne(id);

    if (tenant.emailPlatformCredentials) {
      tenant.emailPlatformCredentials.startDate = new Date();
    }

    return this.entityManager.transaction(async entityManager => {
      const tenantRepository = entityManager.getRepository(Tenant);
      await tenantRepository.update(id, tenant);
      const updated = await tenantRepository.findOne(id);

      if (existing.active !== updated.active) {
        if (!updated.active) {
          await this.auditLogService.logDeactivate(updated.id, entityManager);
        } else {
          await this.auditLogService.logActivate(updated.id, entityManager);
        }
      }

      // Handle changed minimum gross percentage
      if (
        !floatEqual(
          updated.minimumGrossPercentage,
          existing.minimumGrossPercentage,
        )
      ) {
        // Disable all available coupons until they can be updated by the queuing service
        await this.couponService.disableAllAvailableForTenant(
          updated,
          entityManager,
        );
        // Queue the job to update the disabled coupons
        await this.apiSyncQueueService.addTenantMinimumGrossChangedJob(
          updated,
          entityManager,
        );
      }

      // Handle changed fallback max coupon percentage
      // JD: Commenting this out for now because the max percentage job handler doesn't exist and it's breaking things
      // if (!floatEqual(updated.fallbackCouponMaxPercentage, existing.fallbackCouponMaxPercentage)) {
      //   // Disable all available coupons until they can be updated by the queuing service
      //   await this.couponService.disableAllAvailableForTenant(updated);
      //   // Queue the job to update the disabled coupons
      //   await this.apiSyncQueueService.addTenantFallbackCouponMaxPercentage(updated);
      // }

      return updated;
    });
  }

  async findAll(findAllDto: FindAllDto): Promise<FindAndCountRes> {
    const params: FindManyOptions<Tenant> = {
      order: {
        [findAllDto.order || 'id']: (
          findAllDto.dir || 'ASC'
        ).toUpperCase() as Dir,
      },
      skip: findAllDto.skip || 0,
      take: findAllDto.take || 20,
      where: {
        ...(typeof findAllDto.active !== 'undefined'
          ? { active: findAllDto.active }
          : {}),
      },
      select: ['id', 'createdAt', 'externalId', 'name', 'platform', 'active'],
    };

    const [data, total] = await this.tenantRepository.findAndCount(params);
    return { data, total };
  }

  async findOne(id: string): Promise<Tenant> {
    return await this.tenantRepository.findOne(id);
  }

  async findOneByExternalId(id: string): Promise<Tenant> {
    return await this.tenantRepository.findOne({ externalId: id });
  }

  async delete(id: string): Promise<any> {
    return await this.tenantRepository.delete(id);
  }

  async updateFallbackCoupon(data: any, tenant: Tenant): Promise<Tenant> {
    let coupon;

    if (!tenant.fallbackCoupon) {
      // Create a new coupon
      coupon = await this.couponService.create({
        isFallback: true,
        tenant,
        couponAmount: 0,
        percentage: data.percentage,
        code: await this.generateCode(),
      });

      await this.update(tenant.id, { fallbackCoupon: coupon });
    } else {
      coupon = await this.couponService.update(tenant.fallbackCoupon.id, {
        percentage: data.percentage,
      });
    }

    // Trigger an API sync if the percentage changed (or is new)
    if (
      !tenant.fallbackCoupon ||
      !floatEqual(tenant.fallbackCoupon.percentage, data.percentage)
    ) {
      await this.apiSyncQueueService.addCouponChangedJob(coupon, tenant);
    }

    return await this.findOne(tenant.id);
  }

  async queueRotatingFallbackCoupon(tenantId: string) {
    const tenant = await this.tenantRepository.findOneOrFail(tenantId);
    await this.apiSyncQueueService.addCreateFallbackCouponToRotateJob(tenant);
  }

  async updateFeatureFlags(tenantId: string, featureFlags: TenantFeatureFlags) {
    const tenant = await this.findOne(tenantId);
    if (!tenant) {
      throw new NotFoundException();
    }
    tenant.featureFlags = { ...tenant.featureFlags, ...featureFlags };
    await this.tenantRepository.save(tenant);
  }

  async latestActivationAction(tenantId: string, isActive: boolean) {
    const latestAction = await this.auditLogService.latestActivationAction(
      tenantId,
      isActive,
    );
    return latestAction.length === 1 ? latestAction[0] : undefined;
  }
}
