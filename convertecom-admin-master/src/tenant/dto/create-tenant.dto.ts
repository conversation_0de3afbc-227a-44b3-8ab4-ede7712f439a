import {
  IsNotEmpty,
  <PERSON>Number,
  IsOptional,
  IsIn,
  IsJSON,
  IsNumberString,
} from 'class-validator';
import { ApiCredentials } from '../interfaces/api-credentials.interface';
// TODO move this to separate declaration file
export enum Platform {
  SHOPIFY = 'shopify',
}

export class CreateTenantDto {
  @IsNotEmpty()
  @IsIn(Object.values(Platform))
  readonly platform: Platform;

  @IsOptional()
  readonly externalId?: string;

  @IsNotEmpty()
  readonly name: string;

  @IsOptional()
  @IsNumber()
  readonly couponPercentage?: number;

  // TODO should validate ApiCredentials depending on what platform is
  @IsOptional()
  readonly apiCredentials?: ApiCredentials;

  @IsOptional()
  @IsNumber()
  readonly queueBatchSize?: number;
}
