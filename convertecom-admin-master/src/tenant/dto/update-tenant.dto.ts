import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>In,
  IsN<PERSON>berString,
  IsBoolean,
} from 'class-validator';
import { ApiCredentials } from '../interfaces/api-credentials.interface';
import { Platform } from './create-tenant.dto';
import { EmailPlatformCredentials } from '../interfaces/email-platform-credentials.interface';
import { Template } from '../interfaces/template.interface';
import { OnboardingStatus } from '../interfaces/onboarding-status.interface';

export class UpdateTenantDto {
  @IsOptional()
  @IsIn(Object.values(Platform))
  readonly platform?: Platform;

  @IsOptional()
  readonly externalId?: string;

  @IsOptional()
  readonly name?: string;

  @IsOptional()
  @IsNumber() // TODO 0 <> 1
  readonly couponPercentage?: number;

  @IsOptional()
  @IsNumber()
  readonly fallbackCouponMaxPercentage?: number;

  @IsOptional()
  @IsNumber()
  readonly reservationMinutes?: number;

  // TODO should validate ApiCredentials depending on what platform is
  @IsOptional()
  readonly apiCredentials?: ApiCredentials;

  @IsOptional()
  readonly emailPlatformCredentials?: EmailPlatformCredentials;

  @IsOptional()
  @IsNumber()
  readonly queueBatchSize?: number;

  @IsOptional()
  readonly template?: Template;

  @IsOptional()
  @IsBoolean()
  readonly active: boolean;

  @IsOptional()
  readonly onboardingStatus: OnboardingStatus;
}
