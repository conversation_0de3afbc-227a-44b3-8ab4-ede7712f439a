import { IsBoolean, IsOptional } from 'class-validator';
import { TenantFeatureFlags } from '@app/tenant/interfaces/tenant-feature-flags.interface';

export class UpdateFeatureFlagsDto implements TenantFeatureFlags {
  @IsBoolean()
  @IsOptional()
  billingTrial?: boolean;

  @IsBoolean()
  @IsOptional()
  billingTest?: boolean;

  @IsBoolean()
  @IsOptional()
  couponsRotateFallback?: boolean;

  @IsBoolean()
  @IsOptional()
  statisticsDashboardUpdated?: boolean;

  @IsBoolean()
  @IsOptional()
  billingRecoveredRevenue?: boolean;

  @IsBoolean()
  @IsOptional()
  billingSummaryHistory?: boolean;
}
