import {
  IsBooleanString,
  IsIn,
  IsInt,
  IsNumber,
  IsO<PERSON>al,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';

export class FindAllDto {
  @IsOptional()
  @IsIn(['id', 'name', 'platform', 'externalId', 'createdAt', 'updatedAt'])
  order: string;

  @IsOptional()
  @IsIn(['ASC', 'asc', 'DESC', 'desc'])
  dir: string;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  skip: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(50)
  take: number;

  @IsOptional()
  @IsBooleanString()
  active?: boolean;
}
