import { TemplateValue } from './template-value.interface';

export interface Template {
  mainHeader: TemplateValue;
  mainSubHeader: TemplateValue;
  optIn: TemplateValue;
  optInButton: TemplateValue;
  optOutLink: TemplateValue;
  optOutExtra: TemplateValue;
  optInBackground?: TemplateValue;
  optInResultHeader: TemplateValue;
  optInResult: TemplateValue;
  optInResultLink: TemplateValue;
  cart: TemplateValue;
  cartBackground?: TemplateValue;
  cartButton: TemplateValue;
  cartAmount: TemplateValue;
  cartTime: TemplateValue;
  cartExpired: TemplateValue;
  cartExpiredButton: TemplateValue;
  cartInvalid: TemplateValue;
  cartInvalidButton: TemplateValue;
  cartPopupPickOne: TemplateValue;
  cartPopupHeader: TemplateValue;
  cartPopupAmount: TemplateValue;
  cartPopupTime: TemplateValue;
  cartPopupLink: TemplateValue;
  cartPopupCustomImage: TemplateValue;
  popupDelay: number;
  optOutTtl: number;
}
