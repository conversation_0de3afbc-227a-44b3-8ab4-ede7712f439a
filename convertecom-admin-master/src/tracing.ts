import { HoneycombSDK } from '@honeycombio/opentelemetry-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';

export async function tracer() {
  if (process.env.HONEYCOMB_API_KEY && process.env.OTEL_SERVICE_NAME) {
    // uses HONEYCOMB_API_KEY and OTEL_SERVICE_NAME environment variables
    const sdk = new HoneycombSDK({
      apiKey: process.env.HONEYCOMB_API_KEY,
      serviceName: process.env.OTEL_SERVICE_NAME,
      debug: false,
      instrumentations: [
        getNodeAutoInstrumentations(
          {
            '@opentelemetry/instrumentation-nestjs-core': { enabled: true },
            '@opentelemetry/instrumentation-fs': { enabled: false },
          },

          /*{
          '@opentelemetry/instrumentation-pg': { enabled: true },
          '@opentelemetry/instrumentation-express': { enabled: true },
          '@opentelemetry/instrumentation-http': { enabled: true },
        }*/
        ),
      ],
    });

    await sdk
      .start()
      .then(() => {
        console.log('Tracing initialized');
      })
      .catch(error => console.log('Error initializing tracing', error));
    // gracefully shut down the SDK on process exit
    process.on('SIGTERM', () => {
      sdk
        .shutdown()
        .then(
          () => console.log('SDK shut down successfully'),
          err => console.log('Error shutting down SDK', err),
        )
        .finally(() => process.exit(0));
    });
  } else {
    console.log('Tracing not configured');
  }
}
