import {
  IntegrationConfiguration,
  IntegrationPlatformName,
} from '@app/integration/configuration/integrations';
import { IsBoolean, IsOptional } from 'class-validator';

export class UpdateIntegrationDto {
  @IsBoolean()
  @IsOptional()
  isEnabled?: boolean;

  @IsOptional()
  configuration?: Partial<
    Pick<
      IntegrationPlatformName,
      Exclude<keyof IntegrationConfiguration, '__platform'>
    >
  >;
}
