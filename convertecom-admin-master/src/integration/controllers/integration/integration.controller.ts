import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import { AuthTenant } from '@app/auth/decorators/auth-tenant.decorator';
import { Tenant } from '@app/tenant/tenant.entity';
import { CreateIntegrationDto } from '@app/integration/dto/create-integration.dto';
import { TenantIntegrationService } from '@app/integration/services/tenant-integration.service';
import { EntityNotFoundError } from 'typeorm/error/EntityNotFoundError';
import { IntegrationConfiguration } from '@app/integration/configuration/integrations';
import { UpdateIntegrationDto } from '@app/integration/dto/update-integration.dto';

@Controller('integration')
export class IntegrationController {
  constructor(readonly tenantIntegrationService: TenantIntegrationService) {}

  @Get('')
  async all(@AuthTenant() tenant: Tenant) {
    // TODO: Allow superadmins to access this if required
    return { data: await tenant.integrations };
  }

  @Post('')
  async create(
    @AuthTenant() tenant: Tenant,
    @Body() body: CreateIntegrationDto,
  ) {
    try {
      const { platformId, configuration, isEnabled = false } = body;
      const integration = await this.tenantIntegrationService.create(
        tenant,
        platformId,
        configuration,
        isEnabled,
      );
      return { data: integration };
    } catch (e) {
      if (e instanceof EntityNotFoundError) {
        throw new BadRequestException('Invalid integration.');
      }
      throw new BadRequestException();
    }
  }

  @Patch(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async update(
    @AuthTenant() tenant: Tenant,
    @Param('id') id: string,
    @Body() body: UpdateIntegrationDto,
  ) {
    try {
      await this.tenantIntegrationService.update(tenant, id, body);
    } catch (e) {
      if (e instanceof EntityNotFoundError) {
        throw new NotFoundException();
      }
      throw new BadRequestException();
    }
  }
}
