import { Controller, Get } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IntegrationPlatform } from '@app/integration/entities/integration-platform.entity';
import { Repository } from 'typeorm';

@Controller('integration-platform')
export class IntegrationPlatformController {
  constructor(
    @InjectRepository(IntegrationPlatform)
    readonly repository: Repository<IntegrationPlatform>,
  ) {
  }

  @Get('')
  async all() {
    return { data: await this.repository.find() };
  }
}
