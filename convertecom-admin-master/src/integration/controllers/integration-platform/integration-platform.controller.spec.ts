import { Test, TestingModule } from '@nestjs/testing';
import { IntegrationPlatformController } from './integration-platform.controller';

describe('Integration Controller', () => {
  let controller: IntegrationPlatformController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [IntegrationPlatformController],
    }).compile();

    controller = module.get<IntegrationPlatformController>(IntegrationPlatformController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
