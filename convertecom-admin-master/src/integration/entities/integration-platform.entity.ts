import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Integration } from '@app/integration/entities/integration.entity';
import { IntegrationPlatformName } from '@app/integration/configuration/integrations';

@Entity()
export class IntegrationPlatform {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  name: IntegrationPlatformName;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(type => Integration, ti => ti.platform)
  integrations: Promise<Integration[]>;
}
