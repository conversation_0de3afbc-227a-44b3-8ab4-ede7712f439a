import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { IntegrationPlatform } from '@app/integration/entities/integration-platform.entity';
import {
  IntegrationConfiguration,
  IntegrationConfigurations,
  IntegrationPlatformName,
  RecartConfiguration,
} from '@app/integration/configuration/integrations';
import { Type } from 'class-transformer';
import { Tenant } from '@app/tenant/tenant.entity';

@Entity()
export class Integration {
  @PrimaryColumn('uuid')
  id: string;

  @Column('jsonb')
  @Type(() => IntegrationConfiguration, {
    discriminator: {
      property: '__platform',
      subTypes: [
        { name: IntegrationPlatformName.RECART, value: RecartConfiguration },
      ],
    },
  })
  configuration: IntegrationConfigurations;

  @Column()
  isEnabled: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(type => IntegrationPlatform, platform => platform.integrations)
  @JoinColumn()
  platform: IntegrationPlatform;

  @ManyToOne(type => Tenant, tenant => tenant.integrations, { lazy: true })
  tenant: Tenant;
}
