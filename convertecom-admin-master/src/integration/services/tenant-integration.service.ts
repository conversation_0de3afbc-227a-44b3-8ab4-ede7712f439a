import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Integration } from '@app/integration/entities/integration.entity';
import { DeepPartial, Repository } from 'typeorm';
import { IntegrationPlatform } from '@app/integration/entities/integration-platform.entity';
import {
  IntegrationConfiguration,
  IntegrationPlatformName,
  RecartConfiguration,
} from '@app/integration/configuration/integrations';
import { Tenant } from '@app/tenant/tenant.entity';
import { validateOrReject } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { UpdateIntegrationDto } from '@app/integration/dto/update-integration.dto';

@Injectable()
export class TenantIntegrationService {
  constructor(
    @InjectRepository(Tenant) private tenantRepository: Repository<Tenant>,
    @InjectRepository(Integration)
    private integrationRepo: Repository<Integration>,
    @InjectRepository(IntegrationPlatform)
    private integrationPlatformRepo: Repository<IntegrationPlatform>,
  ) {}

  async create(
    tenant: Tenant | string,
    platformId: string,
    configuration: IntegrationConfiguration,
    isEnabled: boolean,
  ) {
    const platform = await this.integrationPlatformRepo.findOneOrFail(
      platformId,
    );
    const integrationTenant =
      typeof tenant === 'string'
        ? await this.tenantRepository.findOneOrFail(tenant)
        : tenant;

    if (await this.hasPlatformIntegration(integrationTenant, platform)) {
      throw new BadRequestException(
        `An integration for ${platform.name} exists already.`,
      );
    }

    const { name } = platform;

    const validatedConfiguration = await this.validateConfiguration(
      name,
      configuration,
    );

    const entity = await this.integrationRepo.save(
      this.integrationRepo.create({
        platform,
        tenant: integrationTenant,
        configuration: validatedConfiguration,
        isEnabled,
      }),
    );

    return await this.integrationRepo.findOne(entity.id);
  }

  /**
   * Update all configuration parameters except for the platform
   * @param tenant
   * @param tenantIntegrationId
   * @param params
   */
  async update(
    tenant: string | Tenant,
    tenantIntegrationId: string,
    params: UpdateIntegrationDto,
  ) {
    const integrationTenant =
      typeof tenant === 'string'
        ? await this.tenantRepository.findOneOrFail(tenant)
        : tenant;
    const tenantIntegration = await this.integrationRepo.findOneOrFail({
      id: tenantIntegrationId,
      tenant: integrationTenant,
    });
    const platform = tenantIntegration.configuration.__platform;

    const { configuration = {}, isEnabled } = params;

    const validatedConfiguration = await this.validateConfiguration(platform, {
      ...tenantIntegration.configuration,
      ...configuration,
      __platform: platform,
    });

    await this.integrationRepo.update(tenantIntegrationId, {
      configuration: validatedConfiguration,
      isEnabled:
        typeof isEnabled !== 'undefined'
          ? isEnabled
          : tenantIntegration.isEnabled,
    });
  }

  async hasPlatformIntegration(tenant: Tenant, platform: IntegrationPlatform) {
    return (await tenant.integrations).some(i => i.platform.id === platform.id);
  }

  private async validateConfiguration(
    name: IntegrationPlatformName,
    configuration: Pick<
      IntegrationConfiguration,
      Exclude<keyof IntegrationConfiguration, 'toString'>
    >,
  ) {
    switch (name) {
      case IntegrationPlatformName.RECART: {
        const config = plainToClass(RecartConfiguration, {
          __platform: name,
          ...configuration,
        });
        await validateOrReject(config, { forbidUnknownValues: true });
        return config;
      }
      default:
        throw new Error('No integration found.');
    }
  }
}
