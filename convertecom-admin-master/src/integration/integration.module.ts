import { Modu<PERSON> } from '@nestjs/common';
import { IntegrationPlatformController } from './controllers/integration-platform/integration-platform.controller';
import { IntegrationController } from './controllers/integration/integration.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IntegrationPlatform } from '@app/integration/entities/integration-platform.entity';
import { Integration } from '@app/integration/entities/integration.entity';
import { TenantIntegrationService } from '@app/integration/services/tenant-integration.service';
import { Tenant } from '@app/tenant/tenant.entity';

@Module({
  controllers: [IntegrationPlatformController, IntegrationController],
  providers: [TenantIntegrationService],
  imports: [
    TypeOrmModule.forFeature([IntegrationPlatform, Integration, Tenant]),
  ],
  exports: [TypeOrmModule],
})
export class IntegrationModule {}
