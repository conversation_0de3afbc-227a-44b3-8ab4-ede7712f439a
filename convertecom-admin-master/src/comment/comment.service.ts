import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Comment } from './comment.entity';
import { User } from '../user/user.entity';

@Injectable()
export class CommentService {
  constructor(
    @InjectRepository(Comment)
    private readonly commentRepository: Repository<Comment>,
  ) {}

  async create(comment: Partial<Comment>): Promise<Comment> {
    return await this.commentRepository.save(comment);
  }

  async delete(id: string) {
    return await this.commentRepository.delete(id);
  }

  async findByUser(user: User) {
    return await this.commentRepository.find({ user });
  }

  async findAll() {
    return await this.commentRepository.find(); // TODO pagination
  }
}
