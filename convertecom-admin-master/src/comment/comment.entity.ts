import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
  ManyToOne,
} from 'typeorm';
import { User } from '../user/user.entity';
import { Coupon } from '../coupon/coupon.entity';

@Entity()
export class Comment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @Column('varchar')
  namespace: string;

  @Column('text')
  body: string;

  @Column({
    type: 'timestamp',
    nullable: false,
    default: () => 'NOW()',
  })
  createdAt: Date;

  @Column({
    type: 'timestamp',
    nullable: false,
    default: () => 'NOW()',
    onUpdate: 'NOW()',
  })
  updatedAt: Date;

  @ManyToOne(type => User, author => author.authoredComments, { eager: true })
  author: User;

  @ManyToOne(type => User, user => user.comments, { eager: true })
  user: User;

  @ManyToOne(type => Coupon, coupon => coupon.comments, { eager: true })
  coupon: Coupon;
}
