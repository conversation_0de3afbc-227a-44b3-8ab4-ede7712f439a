import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Optin } from './optin.entity';
import { Repository, Not } from 'typeorm';
import { Tenant } from '../tenant/tenant.entity';
import { Dir } from '../lib/types/typeorm';

interface FindAllParams {
  skip: number;
  take: number;
  order: string;
  dir: string;
  tenant: Tenant;
}

interface EmailResult {
  email: string;
}

export interface FindAllResult {
  data: Optin[];
  total: number;
}

@Injectable()
export class OptinService {
  constructor(
    @InjectRepository(Optin)
    private readonly optinRepository: Repository<Optin>,
  ) {}

  async findAll(params: FindAllParams): Promise<FindAllResult> {
    const take = params.take || 20;
    const skip = params.skip || 0;

    const [data, total] = await this.optinRepository.findAndCount({
      where: {
        tenant: params.tenant,
        email: Not(''),
      },
      order: {
        [params.order || 'id']: (params.dir || 'ASC').toUpperCase() as Dir,
      },
      take,
      skip,
    });

    return { data, total };
  }

  async findUniqueEmailsForTenant(tenant: Tenant): Promise<EmailResult[]> {
    return await this.optinRepository.query(
      `SELECT DISTINCT ON (lower("email")) "email" FROM "optin" WHERE "tenantId" = $1 and "email" != ''`,
      [tenant.id],
    );
  }
}
