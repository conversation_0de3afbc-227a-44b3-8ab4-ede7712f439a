import { <PERSON>, Get, UseGuards, Req, Query, Post, Res, HttpCode, Header } from '@nestjs/common';
import { OptinService } from './optin.service';
import { FindAllDto } from './dto/find-all.dto';
import { AuthGuard } from '@nestjs/passport';
import { parse } from 'json2csv';

@Controller('optin')
export class OptinController {
  constructor(
    private readonly optinService: OptinService
  ) {}

  @Get()
  async findAll(@Req() req, @Query() params: FindAllDto) {
    // TODO if params.tenantId !== auth tenant and user isn't superadmin, throw error <-- could be a global guard (if non-superadmin passes "tenantId" in req body throw 400)
    return await this.optinService.findAll({
      ...params,
      tenant: req.authUser.tenant,
    });
  }

  @Get('export')
  @Header('Content-Type', 'text/csv')
  async export(@Req() req) {
    const emails = await this.optinService.findUniqueEmailsForTenant(req.authUser.tenant);

    if (emails.length === 0) {
      return '';
    }

    return parse(emails);
  }
}
