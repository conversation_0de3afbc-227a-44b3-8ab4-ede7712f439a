import { <PERSON>N<PERSON>ber, <PERSON>Optional, IsIn, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class FindAllDto {
  @IsOptional()
  @IsIn(['id', 'email', 'createdAt', 'updatedAt'])
  order: string;

  @IsOptional()
  @IsIn(['ASC', 'asc', 'DESC', 'desc'])
  dir: string;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  skip: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(50)
  take: number;
}