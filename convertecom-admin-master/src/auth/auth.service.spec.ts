import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UserService } from '../user/user.service';
import { TenantService } from '@app/tenant/tenant.service';
import { JwtService } from '@nestjs/jwt';

jest.mock('../user/user.service');
jest.mock('@app/tenant/tenant.service');
jest.mock('@nestjs/jwt');

describe('AuthService', () => {
  let service: AuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AuthService, UserService, TenantService, JwtService],
    }).compile();

    service = module.get<AuthService>(AuthService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
