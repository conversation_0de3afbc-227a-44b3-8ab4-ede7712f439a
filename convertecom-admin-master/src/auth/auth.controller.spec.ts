import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from '@app/auth/auth.service';
import { UserService } from '../user/user.service';
jest.mock('@app/auth/auth.service');
jest.mock('../user/user.service');

describe('Auth Controller', () => {
  let controller: AuthController;


  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [AuthService, UserService],
    }).compile();

    controller = module.get<AuthController>(AuthController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
