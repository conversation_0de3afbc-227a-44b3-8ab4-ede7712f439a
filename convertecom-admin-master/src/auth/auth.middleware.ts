import {
  Injectable,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import * as jwt from 'jsonwebtoken';
import { UserService } from '../user/user.service';
import { get, has } from 'lodash';
import { TenantService } from '../tenant/tenant.service';

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(
    private readonly tenantService: TenantService,
    private readonly userService: UserService,
  ) { }

  async use(req: any, res: Response, next: any) {
    // Decode the JWT and attach the user to the request
    // Will throw if token is invalid
    const rawToken = get(req, 'headers.authorization', '').replace(
      'Bearer ',
      '',
    );

    let token;

    try {
      token = jwt.verify(rawToken, process.env.JWT_SECRET);
    } catch (e) {
      throw new UnauthorizedException();
    }

    if (has(token, 'id')) {
      // User token
      const authUser = await this.userService.findOne(get(token, 'id'));

      if (!authUser) {
        throw new UnauthorizedException();
      }

      req.authUser = authUser;
    } else if (has(token, 'tenantId')) {
      // Tenant token
      const tenant = await this.tenantService.findOne(get(token, 'tenantId'));
      console.log("istenant available?", tenant);
      if (!tenant) {
        throw new UnauthorizedException();
      }

      req.authUser = { tenant };
    }

    next();
  }
}
