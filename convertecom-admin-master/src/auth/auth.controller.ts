import { Body, Controller, Post, Req } from '@nestjs/common';
import * as requestIp from 'request-ip';
import { AuthService } from './auth.service';
import { LoginCredentialsDto } from './dto/login-credentials.dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('token')
  async createToken(
    @Body() loginCredentialsDto: LoginCredentialsDto,
    @Req() req,
  ): Promise<any> {
    return await this.authService.login(
      loginCredentialsDto.email,
      loginCredentialsDto.password,
      requestIp.getClientIp(req),
    );
  }
}
