import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';
import { Reflector } from '@nestjs/core';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const controllerRoles = this.reflector.get<string[]>(
      'roles',
      context.getClass(),
    );
    const routeRoles = this.reflector.get<string[]>(
      'roles',
      context.getHandler(),
    );
    if (!controllerRoles && !routeRoles) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.authUser;
    // Check @Roles decorators on the controller and the route, return true if there are no roles on the item.
    const hasControllerRole = () =>
      !controllerRoles
        ? true
        : user.roles.some(role => controllerRoles.includes(role));
    const hasRouteRole = () =>
      !routeRoles ? true : user.roles.some(role => routeRoles.includes(role));

    return user && user.roles && hasControllerRole() && hasRouteRole();
  }
}
