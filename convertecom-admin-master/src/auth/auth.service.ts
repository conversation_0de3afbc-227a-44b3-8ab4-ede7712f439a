import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserService } from '../user/user.service';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { TenantService } from '../tenant/tenant.service';
import { User } from '../user/user.entity';
import { Tenant } from '../tenant/tenant.entity';
import { LoginCredentialsDto } from '@app/auth/dto/login-credentials.dto';
import { EntityManager } from 'typeorm';
import { AuditLogService } from '../audit-log/audit-log.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly tenantService: TenantService,
    private readonly jwtService: JwtService,
    private readonly entityManager: EntityManager,
    private readonly auditLogService: AuditLogService,
  ) {}

  async login(email: string, password: string, ipAddress: string) {
    const user = await this.userService.findOneByCredentials({
      email,
      password,
    });

    return this.entityManager.transaction(async em => {
      const data = {
        signInCount: user.signInCount + 1,
        lastSignInAt: user.currentSignInAt,
        lastSignInIp: user.currentSignInIp,
        currentSignInAt: new Date(),
        currentSignInIp: ipAddress,
      };

      await this.userService.update(user.id, data, em);
      await this.auditLogService.logUserAccess(user.id, em);

      return await this.createToken({
        id: user.id,
        tenantId: user.tenant ? user.tenant.id : null,
        roles: user.roles,
      });
    });
  }

  async createToken(payload: JwtPayload) {
    const accessToken = this.jwtService.sign(payload);
    return {
      expiresIn: process.env.JWT_EXPIRE_SECONDS
        ? parseInt(process.env.JWT_EXPIRE_SECONDS, 10)
        : 3600,
      accessToken,
    };
  }

  async validateUser(payload: JwtPayload): Promise<User | Tenant> {
    if (payload.id) {
      return await this.userService.findOne(payload.id);
    } else {
      return await this.tenantService.findOne(payload.tenantId);
    }
  }
}
