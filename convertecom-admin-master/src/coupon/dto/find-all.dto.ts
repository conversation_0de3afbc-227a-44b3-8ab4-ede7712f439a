import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  IsIn,
  IsNumberString,
  IsInt,
} from 'class-validator';
import { Exclude, Type } from 'class-transformer';

// TODO this can probably be moved into a common DTO (or at least "FindAllUsersDto extends FindAllDto")
export class FindAllDto {
  // TODO these aren't working
  private static orderOptions = ['id', 'createdAt', 'updatedAt']; // On default, just id and timestamps
  private static dirOptions = ['ASC', 'asc', 'DESC', 'desc'];

  @IsOptional()
  @IsIn(FindAllDto.orderOptions)
  order: string;

  @IsOptional()
  @IsIn(FindAllDto.dirOptions)
  dir: string;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  skip: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(50)
  take: number;
}
