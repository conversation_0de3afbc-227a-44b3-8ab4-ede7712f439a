import {
  <PERSON>,
  Get,
  Param,
  Req,
  UnauthorizedException,
  UseGuards,
  Query,
} from '@nestjs/common';
import { CouponService } from './coupon.service';
import { AuthGuard } from '@nestjs/passport';
import { FindAllDto } from './dto/find-all.dto';

@Controller('coupon')
export class CouponController {
  constructor(private readonly couponService: CouponService) {}

  @Get()
  async findAll(@Req() req, @Query() findAllDto: FindAllDto) {
    return await this.couponService.findAll(findAllDto, {
      tenant: req.authUser.tenant,
      isFallback: false,
    });
  }

  @Get(':id')
  async findOne(@Param('id') id, @Req() req) {
    const coupon = await this.couponService.findOne(id);
    if (!coupon.tenantId || coupon.tenantId !== req.authUser.tenant.id) {
      throw new UnauthorizedException();
    }
    return { data: coupon };
  }
}
