import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Coupon } from './coupon.entity';
import { EntityManager, Repository } from 'typeorm';
import { FindAllDto } from './dto/find-all.dto';
import { Dir } from '../lib/types/typeorm';
import { Tenant } from '../tenant/tenant.entity';

export interface FindAndCountRes {
  total: number;
  data: Coupon[];
} // TODO common interface w/ generic data type

@Injectable()
export class CouponService {
  constructor(
    @InjectRepository(Coupon)
    private readonly couponRepository: Repository<Coupon>,
  ) {}

  async findAll(
    findAllDto: FindAllDto,
    filters?: any,
  ): Promise<FindAndCountRes> {
    const params = {
      relations: ['originatorOrder', 'redemptionOrders'],
      where: filters,
      order: {
        [findAllDto.order || 'id']: (
          findAllDto.dir || 'ASC'
        ).toUpperCase() as Dir,
      },
      skip: findAllDto.skip || 0,
      take: findAllDto.take || 20,
    };

    const [data, total] = await this.couponRepository.findAndCount(params);
    return { data, total };
  }

  async findOne(id: string): Promise<Coupon> {
    return await this.couponRepository.findOne(id);
  }

  async create(data: Partial<Coupon>): Promise<Coupon> {
    return await this.couponRepository.save(data);
  }

  async update(id: string, data: Partial<Coupon>): Promise<Coupon> {
    await this.couponRepository.update(id, data);
    return this.findOne(id);
  }

  async disableAllAvailableForTenant(
    tenant: Tenant,
    entityManager: EntityManager,
  ): Promise<any> {
    return await entityManager.getRepository(Coupon).query(
      `UPDATE coupon SET "isDisabled" = true
      WHERE id in (
        SELECT c.id FROM coupon c
        JOIN platform_data pd ON pd."couponId" = c.id AND pd.key = $1
        WHERE c."tenantId" = $2
        AND c."isFallback" = false
        AND (c."reservedUntil" IS NULL OR c."reservedUntil" < NOW())
        AND (SELECT COUNT(*) FROM coupon_redemption_orders_order x WHERE x."couponId" = c.id) = 0
        FOR UPDATE SKIP LOCKED
      );`,
      ['shopify_discountcode_id', tenant.id],
    );
  }
}
