import { forwardRef, Module } from '@nestjs/common';
import { CouponService } from './coupon.service';
import { CouponController } from './coupon.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Coupon } from './coupon.entity';
import { CouponReconciliationService } from './coupon-reconciliation.service';
import { CouponReconciliationController } from './coupon-reconciliation.controller';
import { ShopifyModule } from '../third-party/shopify/shopify.module';
import { PlatformData } from '../platform-data/platform-data.entity';
import { TenantModule } from '@app/tenant/tenant.module';
import { Tenant } from '@app/tenant/tenant.entity';
import { Comment } from '../comment/comment.entity';
import { Integration } from '@app/integration/entities/integration.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Coupon, PlatformData]),
    ShopifyModule,
    forwardRef(() => TenantModule),
  ],
  providers: [CouponService, CouponReconciliationService],
  controllers: [CouponController, CouponReconciliationController],
  exports: [CouponService, CouponReconciliationService],
})
export class CouponModule {}
