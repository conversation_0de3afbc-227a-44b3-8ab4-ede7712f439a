import { Body, Controller, Delete, Get, HttpCode, HttpStatus, NotFoundException, Param, ParseUUIDPipe, Post } from '@nestjs/common';
import { Roles } from '../lib/decorators/roles.decorator';
import { Role } from '../lib/role';
import { CouponReconciliationService } from './coupon-reconciliation.service';
import { ReconcileTenantCouponsDto } from './dto/reconcile-tenant-coupons.dto';

@Roles(Role.SUPERADMIN)
@Controller('coupon')
export class CouponReconciliationController {
  constructor(
    private readonly couponReconciliationService: CouponReconciliationService,
  ) {}

  /**
   * Start all reconciliation jobs
   */
  @Post('reconciliation/start')
  start() {
    this.couponReconciliationService.startReconciliationJobs();
  }

  /**
   * Stop processing all reconciliation jobs
   */
  @Post('reconciliation/stop')
  stop() {
    this.couponReconciliationService.stopReconciliationJobs();
  }

  /**
   * Queue a reconciliation job if one doesn't exist for the selected tenant.
   */
  @Post('reconciliation')
  @HttpCode(HttpStatus.ACCEPTED)
  reconcile(@Body() body: ReconcileTenantCouponsDto) {
    return this.couponReconciliationService.createReconciliation(body.tenantId);
  }

  /**
   * Stop a reconciliation job if one exists and delete it from the queue. The status
   * will stay.
   */
  @Delete('reconciliation/:tenantId')
  @HttpCode(HttpStatus.NO_CONTENT)
  stopReconciliation(@Param('tenantId', ParseUUIDPipe) tenantId: string) {
    this.couponReconciliationService.stopReconciliation(tenantId);
  }

  /**
   * Get the results of the current or latest reconciliation job for a tenant
   */
  @Get('reconciliation/:tenantId')
  getReconciliation(@Param('tenantId', ParseUUIDPipe) tenantId: string) {
    const result = this.couponReconciliationService.findReconciliationStatus(
      tenantId,
    );
    if (!result) {
      throw new NotFoundException(
        `No reconciliation status found for tenant ${tenantId}.`,
      );
    }

    return result;
  }
}
