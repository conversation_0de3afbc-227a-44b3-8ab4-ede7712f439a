import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Tenant } from '@app/tenant/tenant.entity';
import { ReconciliationJobStatus } from './dto/reconciliation-job-status';
import { TenantService } from '@app/tenant/tenant.service';
import { ReconciliationJob } from './dto/reconciliation-job';
import { ShopifyService } from '../third-party/shopify/shopify.service';
import {
  PriceRule,
  PriceRuleResponseDto,
} from '../third-party/shopify/dto/price-rule.dto';
import { nanoid } from 'nanoid';
import { In, Repository } from 'typeorm';
import {
  PlatformData,
  PlatformDataKey,
} from '../platform-data/platform-data.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CouponJobFailedException } from './exceptions/coupon-job-failed.exception';

@Injectable()
export class CouponReconciliationService {
  private static RECONCILIATION_INTERVAL = 'reconile-coupons';
  private logger = new Logger(CouponReconciliationService.name, false);
  /*
   Map of tenantId to status
   This is not a great implementation. We can use it because there is only one instance of the admin service running.
   */
  private tenantJobStatuses = new Map<string, ReconciliationJobStatus>();

  /*
  Map of tenantId to job.
   */
  private tenantJobs = new Map<string, ReconciliationJob>();

  private isStopped = true;

  constructor(
    private readonly shopifyService: ShopifyService,
    private readonly tenantService: TenantService,
    @InjectRepository(PlatformData)
    private readonly platformDataRepository: Repository<PlatformData>,
    private readonly schedulerRegistry: SchedulerRegistry,
  ) {}

  /**
   * Dynamically start processing all jobs
   */
  startReconciliationJobs() {
    if (this.isRunningJobs()) {
      this.logger.log('Reconciliation jobs already running.');
      return;
    }
    const intervalJob = () => {
      this.processAllJobs().catch(err => {
        this.logger.error(
          `Unknown error processing reconciliation jobs. ${err}`,
        );
        this.stopReconciliationJobs();
      });
    };

    this.isStopped = false;
    const interval = setInterval(intervalJob, 1000);
    this.schedulerRegistry.addInterval(
      CouponReconciliationService.RECONCILIATION_INTERVAL,
      interval,
    );
  }

  /**
   * Dynamically stop processing all jobs and clears all statuses.
   */
  stopReconciliationJobs() {
    if (!this.isRunningJobs()) {
      this.logger.log('Reconciliation jobs already stopped.');
    } else {
      this.isStopped = true;
      this.schedulerRegistry.deleteInterval(
        CouponReconciliationService.RECONCILIATION_INTERVAL,
      );
      this.logger.log('Stopping reconciliation jobs');

      for (const [tenantId, job] of this.tenantJobs) {
        // Only delete unlocked jobs. Locked jobs will cleanup themselves if isStopped is true
        if (!job.locked) {
          this.deleteJobAndStatus(tenantId);
        }
      }
    }
  }

  async createReconciliation(
    tenantId: string,
  ): Promise<ReconciliationJobStatus> {
    const tenant: Tenant = await this.tenantService.findOne(tenantId);
    if (!tenant) {
      throw new NotFoundException(`Cannot find tenant with ID ${tenantId}`);
    }

    const id = nanoid();
    const status: ReconciliationJobStatus = {
      jobId: id,
      tenantId,
      processedCount: 0,
      startedAt: new Date().toISOString(),
      finishedAt: undefined,
      unsyncedCoupons: [],
    };

    const job: ReconciliationJob = {
      id,
      tenantId,
      page: `price_rules.json?limit=250&times_used=1`,
      locked: false,
    };

    this.tenantJobStatuses.set(tenantId, status);
    this.tenantJobs.set(tenantId, job);

    return status;
  }

  stopReconciliation(tenantId: string) {
    if (this.tenantJobs.has(tenantId)) {
      this.stopJob(this.tenantJobs.get(tenantId), `Manually stopped.`);
    }
  }

  findReconciliationStatus(tenantId: string): ReconciliationJobStatus {
    return this.tenantJobStatuses.get(tenantId);
  }

  async processAllJobs() {
    for (const [tenantId, job] of this.tenantJobs) {
      if (job.locked) {
        this.logger.log(`Job ${job.id} for ${tenantId} is locked. Continuing.`);
        continue;
      }
      if (!job.page) {
        this.stopJob(job, 'No next page');
        continue;
      }
      this.tenantJobs.set(tenantId, { ...job, locked: true });
      this.processJob(job)
        .then(() => {
          // Jobs may be processing when we stop them
          if (this.isStopped) {
            this.deleteJobAndStatus(tenantId);
          } else if (this.tenantJobs.has(tenantId)) {
            this.tenantJobs.set(tenantId, {
              ...this.tenantJobs.get(tenantId),
              locked: false,
            });
          }
        })
        .catch(err => {
          let message = 'Unknown error.';
          if (err instanceof Error) {
            message = err.message;
          }
          this.logger.error(
            `Failed to process job for ${tenantId}: ${message}`,
          );
          this.stopJob(job, `Failed to process job because ${message}`);
        });
    }
  }

  /**
   * Fetch all price rules since date and check for coupons that aren't marked as used
   *
   * https://shopify.dev/api/admin/rest/reference/discounts/pricerule#index-2020-04
   */
  async processJob(job: ReconciliationJob) {
    const { tenantId, page, id } = job;
    this.logger.log(
      `Starting job ${id} for tenant ${tenantId} on page ${page}`,
    );

    const tenant = await this.tenantService.findOne(tenantId);

    if (!tenant) {
      throw new CouponJobFailedException(
        `Failed to find tenant with id ${tenantId}.`,
      );
    }
    let priceRules: PriceRule[];
    let linkHeader: string;
    let nextUrl: string;

    try {
      const res = await this.shopifyService.get<PriceRuleResponseDto>(
        tenant,
        page,
      );
      priceRules = res.data.price_rules;
      linkHeader = res.headers.link;
    } catch (e) {
      throw new CouponJobFailedException('Error reaching Shopify.');
    }

    if (linkHeader) {
      const parts = linkHeader.split(',');
      const nextPart = parts.find(part => part.includes('rel="next"'));
      if (!nextPart) {
        return;
      }
      const nextLinkRegex = /<(?:.*?)(?<nextUrl>price_rules\.json\?.*?)>/i;
      const result = nextLinkRegex.exec(nextPart);
      nextUrl = result.groups.nextUrl;
    }

    const ruleIds = priceRules.map(rule => rule.id);

    // It's possible for 0 price rules to be returned
    const platformData =
      priceRules.length === 0
        ? []
        : await this.platformDataRepository.find({
            where: {
              value: In(ruleIds),
              key: PlatformDataKey.SHOPIFY_PRICERULE_ID,
            },
            relations: ['coupon', 'coupon.redemptionOrders'],
          });

    const missingCoupons: string[] = [];
    for (const priceRule of platformData) {
      const { coupon } = priceRule;
      if (!coupon) {
        this.logger.warn(`Missing coupon for price rule ${priceRule.id}`);
        continue;
      }
      const { redemptionOrders } = coupon;

      // The price rule has been used one time, but no related order has been found
      if (!redemptionOrders || redemptionOrders.length === 0) {
        missingCoupons.push(coupon.code);
      }
    }

    // Force stop if processed.
    if (this.isStopped) {
      this.deleteJobAndStatus(tenantId);
      return;
    }

    const jobStatus = this.tenantJobStatuses.get(tenantId);
    // The job is now done!
    this.tenantJobStatuses.set(tenantId, {
      ...jobStatus,
      unsyncedCoupons: [...jobStatus.unsyncedCoupons, ...missingCoupons],
      processedCount: jobStatus.processedCount + platformData.length,
      finishedAt: !nextUrl ? new Date().toISOString() : jobStatus.finishedAt,
    });
    if (!nextUrl) {
      this.stopJob(job, 'Reached end of pages.');
    } else {
      // Queue the next job
      const latestJobRef = this.tenantJobs.get(tenantId);
      this.tenantJobs.set(tenantId, { ...latestJobRef, page: nextUrl });
    }
  }

  /**
   * Dequeues the current job if it exists
   */
  private stopJob(job: ReconciliationJob, reason: string) {
    const { id, tenantId } = job;
    this.logger.log(`Stopping job ${id} for tenant ${tenantId}: ${reason}`);
    if (this.tenantJobs.has(tenantId)) {
      this.logger.log(`Deleting job ${job.id} for tenant ${tenantId}`);
      this.tenantJobs.delete(tenantId);
    }
    if (this.tenantJobStatuses.has(tenantId)) {
      const status = this.tenantJobStatuses.get(tenantId);
      if (status.finishedAt) {
        this.logger.log(
          `Job ${id} for tenant ${tenantId} was already stopped.`,
        );
      } else {
        this.tenantJobStatuses.set(tenantId, {
          ...status,
          finishedAt: new Date().toISOString(),
        });
        this.logger.log(`Job ${id} for tenant ${tenantId} stopped.`);
      }
    }
  }

  private deleteJobAndStatus(tenantId: string) {
    this.tenantJobs.delete(tenantId);
    this.tenantJobStatuses.delete(tenantId);
  }

  /**
   * Helper to check if the interval is running already
   */
  private isRunningJobs() {
    let existingInterval: unknown;
    try {
      existingInterval = this.schedulerRegistry.getInterval(
        CouponReconciliationService.RECONCILIATION_INTERVAL,
      );
    } catch (e) {
      existingInterval = undefined;
    }

    return !!existingInterval;
  }
}
