import {
  Entity,
  PrimaryGeneratedColumn,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Column,
} from 'typeorm';
import { Coupon } from '../coupon/coupon.entity';

export enum PlatformDataKey {
  SHOPIFY_PRICERULE_ID = 'shopify_pricerule_id',
  SHOPIFY_DISCOUNTCODE_ID = 'shopify_discountcode_id',
}

@Entity()
export class PlatformData {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  key: PlatformDataKey;

  @Column()
  value: string;

  @ManyToOne(type => Coupon, coupon => coupon.platformData)
  coupon: Coupon;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
