import { Injectable } from '@nestjs/common';
import { ShopifyService } from './shopify.service';
import { Tenant } from '@app/tenant/tenant.entity';

/**
 * There are more, including only the ones needed right now.
 */
export interface ListPriceRuleQueryParams {
  limit: number;
  times_used: number;
}

@Injectable()
export class PriceRuleService {
  constructor(private readonly shopifyService: ShopifyService) {}
}
