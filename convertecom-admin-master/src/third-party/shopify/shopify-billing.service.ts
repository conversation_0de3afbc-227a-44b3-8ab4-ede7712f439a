import { Injectable } from '@nestjs/common';
import { ShopifyService } from './shopify.service';
import { RecurringApplicationChargeResponse } from './dto/billing.dto';
import { Tenant } from '@app/tenant/tenant.entity';
import { AxiosResponse } from 'axios';

@Injectable()
export class ShopifyBillingService {
  constructor(private shopifyService: ShopifyService) {}

  async activateRecurringCharges(
    tenant: Tenant,
    recurringChargeId: string,
  ): Promise<AxiosResponse<RecurringApplicationChargeResponse>> {
    return await this.shopifyService.post<RecurringApplicationChargeResponse>(
      tenant,
      `recurring_application_charges/${recurringChargeId}/activate.json`,
    );
  }
}
