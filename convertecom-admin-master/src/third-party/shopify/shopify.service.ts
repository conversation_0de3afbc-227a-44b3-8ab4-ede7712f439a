import { Injectable, Logger } from '@nestjs/common';
import { Tenant } from '@app/tenant/tenant.entity';
import axios, { AxiosPromise, AxiosResponse, Method } from 'axios';
import { ShopifyError } from './dto/shopify-error.dto';

@Injectable()
export class ShopifyService {
  async get<R = any>(
    tenant: Tenant,
    endpoint: string,
    options: { transformResponse?: (res: string) => any } = {},
  ) {
    return this.callShopifyApi<R>(tenant, {
      method: 'GET',
      endpoint,
      data: undefined,
      ...options,
    });
  }

  post<R = any, D = any>(tenant: Tenant, endpoint: string, data?: D) {
    return this.callShopifyApi(tenant, { method: 'POST', endpoint, data });
  }

  private async callShopifyApi<R = any, D = any>(
    tenant: Tenant,
    params: {
      method: Method;
      endpoint: string;
      data?: D;
      transformResponse?: (res: unknown) => unknown;
    },
  ) {
    const { method, endpoint, data, transformResponse } = params;

    try {
      const response = ((await axios({
        method,
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': tenant.apiCredentials.accessToken,
        },
        url: `https://${tenant.externalId}/admin/api/2020-01/${endpoint}`,
        data: method !== 'GET' ? data : undefined,
        transformResponse,
      })) as unknown) as AxiosResponse<R>;

      if (this.shopifyResponseHasErrors(response)) {
        Logger.error(response.data.errors);
        throw new Error('Failed to call the Shopify API');
      }

      return response;
    } catch (e) {
      Logger.error(e);
      throw new Error('Failed to call the Shopify API');
    }
  }

  private shopifyResponseHasErrors(
    response: AxiosResponse<any>,
  ): response is AxiosResponse<ShopifyError> {
    return response.status >= 400;
  }
}
