import { DiscountCode } from '../../../order/dto/internal-shopify-webhook.dto';

export interface DiscountCodeDto {
  code: string;
  amount: string;
  type: string;
}

export interface NoteAttributeDto {
  name: string,
  value: string,
}

export interface OrderDto {
  id: number;
  cart_token: string;
  created_at: string;
  updated_at: string;
  discount_codes: DiscountCodeDto[];
  note_attributes: NoteAttributeDto[];
  total_discounts: number;
  total_line_items_price: number;
  total_price: number;
  subtotal_price: number;
  customer: any;
}

/**
 * This DTO only has the fields we care about right now, feel free to add more
 * https://shopify.dev/api/admin-rest/2022-04/resources/order#get-orders?status=any
 */
export interface OrderResponseDto {
  orders: OrderDto[];
}
