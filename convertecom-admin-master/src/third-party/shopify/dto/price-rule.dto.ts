export interface PriceRule {
  id: number;
  value_type: string;
  value: string;
  customer_selection: string;
  target_type: string;
  target_selection: string;
  allocation_method: string;
  allocation_limit: null;
  once_per_customer: false;
  usage_limit: null;
  starts_at: string;
  ends_at: string;
  created_at: string;
  updated_at: string;
  entitled_product_ids: Array<unknown>;
  entitled_variant_ids: Array<unknown>;
  entitled_collection_ids: Array<unknown>;
  entitled_country_ids: Array<unknown>;
  prerequisite_product_ids: Array<unknown>;
  prerequisite_variant_ids: Array<unknown>;
  prerequisite_collection_ids: Array<unknown>;
  prerequisite_saved_search_ids: Array<unknown>;
  prerequisite_customer_ids: Array<unknown>;
  prerequisite_subtotal_range: null;
  prerequisite_quantity_range: null;
  prerequisite_shipping_price_range: null;
  prerequisite_to_entitlement_quantity_ratio: {
    prerequisite_quantity: null;
    entitled_quantity: null;
  };
  title: string;
  admin_graphql_api_id: string;
}

export interface PriceRuleResponseDto {
  price_rules: PriceRule[];
}
