export interface RecurringApplicationCharge {
  id: number;
  name: string;
  api_client_id: number;
  price: string;
  status: string;
  return_url: string;
  billing_on: string;
  created_at: string;
  updated_at: string;
  test?: boolean;
  activated_on: string;
  cancelled_on?: string;
  trial_days: number;
  trial_ends_on: string;
  decorated_return_url: string;
  balance_used?: number;
}

export interface RecurringApplicationChargeResponse {
  recurring_application_charge: RecurringApplicationCharge;
  errors?: any;
}

