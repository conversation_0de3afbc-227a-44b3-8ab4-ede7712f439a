import {
  Column,
  CreateDateColumn,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class AuditLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('varchar')
  eventType: string;

  @Column('uuid')
  targetId: string;

  @Column('json')
  metadata: object;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
