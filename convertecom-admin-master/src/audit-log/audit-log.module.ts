import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuditLog } from './audit-log.entity';
import { Tenant } from '@app/tenant/tenant.entity';
import { AuditLogService } from './audit-log.service';

@Module({
  imports: [TypeOrmModule.forFeature([AuditLog, Tenant])],
  providers: [AuditLogService],
  exports: [AuditLogService, TypeOrmModule],
})
export class AuditLogModule {}
