import { Injectable, Logger } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { AuditLog } from './audit-log.entity';

@Injectable()
export class AuditLogService {
  constructor(private readonly entityManager: EntityManager) { }
  /**
   * A tenant logs in from Shopify
   *
   * TODO: Move this to convertecom
   *
   * @param tenantId
   * @param entityManager
   */
  async logTenantAccess(tenantId: string, entityManager: EntityManager) {
    return entityManager.getRepository(AuditLog).save({
      eventType: 'tenant:access',
      targetId: tenantId,
    });
  }

  /**
   * A user logs in from the admin UI
   * @param userId
   * @param entityManager
   */
  async logUserAccess(userId: string, entityManager: EntityManager) {
    return entityManager.getRepository(AuditLog).save({
      eventType: 'user:access',
      targetId: userId,
    });
  }

  /**
   * ConvertEcom is activated
   * @param tenantId
   * @param entityManager
   */
  async logActivate(tenantId: string, entityManager: EntityManager) {
    return entityManager.getRepository(AuditLog).save({
      eventType: 'tenant:activate',
      targetId: tenantId,
    });
  }

  /**
   * ConvertEcom is deactivated
   * @param tenantId
   * @param entityManager
   */
  async logDeactivate(tenantId: string, entityManager: EntityManager) {
    return entityManager.getRepository(AuditLog).save({
      eventType: 'tenant:deactivate',
      targetId: tenantId,
    });
  }

  async latestActivationAction(tenantId: string, isActive: boolean) {
    return this.entityManager.getRepository(AuditLog).find({
      where: {
        eventType: isActive ? 'tenant:activate' : 'tenant:deactivate',
        targetId: tenantId,
      },
      order: {
        createdAt: 'DESC',
      },
      take: 1,
    });
  }
}
