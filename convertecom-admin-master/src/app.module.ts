import {
  CacheModule,
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { parse } from 'pg-connection-string';
import { AuthModule } from './auth/auth.module';
import { UserModule } from './user/user.module';
import { CommentModule } from './comment/comment.module';
import { TenantModule } from './tenant/tenant.module';
import { CouponModule } from './coupon/coupon.module';
import { AuthMiddleware } from './auth/auth.middleware';
import { ApiSyncQueueModule } from './api-sync-queue/api-sync-queue.module';
import { OptinModule } from './optin/optin.module';
import { StatisticModule } from './statistic/statistic.module';
import { BillingModule } from './billing/billing.module';
import { APP_GUARD } from '@nestjs/core';
import { RolesGuard } from './auth/guards/roles.guard';
import { UserController } from './user/user.controller';
import { CouponController } from './coupon/coupon.controller';
import { TenantController } from './tenant/tenant.controller';
import { StatisticController } from './statistic/statistic.controller';
import { OptinController } from './optin/optin.controller';
import { BillingController } from './billing/controllers/billing.controller';
import { ImpressionModule } from './impression/impression.module';
import { EmailModule } from './email/email.module';
import { BillingSettingsController } from './billing/controllers/billing-settings.controller';
import { CreditController } from './billing/controllers/credit.controller';
import { EmailController } from './email/email.controller';
import { IntegrationModule } from '@app/integration/integration.module';
import { IntegrationController } from '@app/integration/controllers/integration/integration.controller';
import { IntegrationPlatformName } from '@app/integration/configuration/integrations';
import { IntegrationPlatformController } from '@app/integration/controllers/integration-platform/integration-platform.controller';
import { ScheduleModule } from '@nestjs/schedule';
import { CouponReconciliationController } from './coupon/coupon-reconciliation.controller';
import { OrderReconciliationController } from './order/order-reconciliation.controller';
import { OrderModule } from './order/order.module';
import { TenantBillingController } from './billing/controllers/tenant-billing.controller';
import { AppController } from './app.controller';

// Import all entities explicitly
import { User } from './user/user.entity';
import { Tenant } from './tenant/tenant.entity';
import { Comment } from './comment/comment.entity';
import { Coupon } from './coupon/coupon.entity';
import { Customer } from './customer/customer.entity';
import { Order } from './order/order.entity';
import { Optin } from './optin/optin.entity';
import { ApiSyncQueue } from './api-sync-queue/api-sync-queue.entity';
import { PlatformData } from './platform-data/platform-data.entity';
import { Billing } from './billing/billing.entity';
import { BillingLog } from './billing/billing-log.entity';
import { BillingSettings } from './billing/billing-settings.entity';
import { CreditLog } from './billing/credit-log.entity';
import { GdprLog } from './gdpr-log/gdpr-log.entity';
import { Impression } from './impression/impression.entity';
import { Integration } from '@app/integration/entities/integration.entity';
import { IntegrationPlatform } from '@app/integration/entities/integration-platform.entity';
import { AuditLog } from './audit-log/audit-log.entity';

// Always use environment variables, never fall back to ormconfig.json
let connectionConfig;

// If DATABASE_URL is provided, parse it
if (process.env.DATABASE_URL) {
  const connection = parse(process.env.DATABASE_URL);
  console.log('Using DATABASE_URL:', connection.host);
  connectionConfig = {
    type: process.env.TYPEORM_CONNECTION,
    host: connection.host,
    port: connection.port,
    username: connection.user,
    password: connection.password,
    database: connection.database,
    entities: [
      User,
      Tenant,
      Comment,
      Coupon,
      Customer,
      Order,
      Optin,
      ApiSyncQueue,
      PlatformData,
      Billing,
      BillingLog,
      BillingSettings,
      CreditLog,
      GdprLog,
      Impression,
      Integration,
      IntegrationPlatform,
      AuditLog,
    ],
    synchronize: process.env.TYPEORM_SYNCHRONIZE === 'true',
    migrationsRun: process.env.TYPEORM_MIGRATIONS_RUN === 'true',
    migrations: [process.env.TYPEORM_MIGRATIONS],
    cli: {
      migrationsDir: process.env.TYPEORM_MIGRATIONS_DIR,
    },
    extra: {
      ssl:
        typeof process.env.TYPEORM_SSL === 'undefined' ||
          process.env.TYPEORM_SSL === 'true'
          ? { rejectUnauthorized: false }
          : false,
    },
    logging: process.env.TYPEORM_LOGGING === 'true',
  };
}
// Otherwise use individual TYPEORM_* environment variables
else if (process.env.TYPEORM_HOST) {
  console.log('Using TYPEORM_* environment variables');
  connectionConfig = {
    type: process.env.TYPEORM_CONNECTION,
    host: process.env.TYPEORM_HOST,
    port: parseInt(process.env.TYPEORM_PORT, 10),
    username: process.env.TYPEORM_USERNAME,
    password: process.env.TYPEORM_PASSWORD,
    database: process.env.TYPEORM_DATABASE,
    entities: [
      User,
      Tenant,
      Comment,
      Coupon,
      Customer,
      Order,
      Optin,
      ApiSyncQueue,
      PlatformData,
      Billing,
      BillingLog,
      BillingSettings,
      CreditLog,
      GdprLog,
      Impression,
      Integration,
      IntegrationPlatform,
      AuditLog,
    ],
    synchronize: process.env.TYPEORM_SYNCHRONIZE === 'true',
    migrationsRun: process.env.TYPEORM_MIGRATIONS_RUN === 'true',
    migrations: [process.env.TYPEORM_MIGRATIONS],
    cli: {
      migrationsDir: process.env.TYPEORM_MIGRATIONS_DIR,
    },
    extra: {
      ssl:
        typeof process.env.TYPEORM_SSL === 'undefined' ||
          process.env.TYPEORM_SSL === 'true'
          ? { rejectUnauthorized: false }
          : false,
    },
    logging: process.env.TYPEORM_LOGGING === 'true',
  };
}
// Fallback with error message
else {
  console.error('ERROR: No database configuration found in environment variables!');
  console.error('Please set DATABASE_URL or individual TYPEORM_* variables in .env file');
  console.error('Available env vars:', Object.keys(process.env).filter(key => key.includes('DATABASE') || key.includes('TYPEORM')));
  // Create a minimal config to avoid TypeORM errors
  connectionConfig = {
    type: 'postgres',
    host: 'localhost',
    port: 5432,
    username: 'missing',
    password: 'missing',
    database: 'missing',
    entities: [
      User,
      Tenant,
      Comment,
      Coupon,
      Customer,
      Order,
      Optin,
      ApiSyncQueue,
      PlatformData,
      Billing,
      BillingLog,
      BillingSettings,
      CreditLog,
      GdprLog,
      Impression,
      Integration,
      IntegrationPlatform,
      AuditLog,
    ],
    synchronize: false,
    logging: false,
  };
}

@Module({
  imports: [
    TypeOrmModule.forRoot(connectionConfig),
    AuthModule,
    CommentModule,
    TenantModule,
    UserModule,
    CouponModule,
    ApiSyncQueueModule,
    OptinModule,
    StatisticModule,
    BillingModule,
    ImpressionModule,
    EmailModule,
    CacheModule.register(),
    IntegrationModule,
    ScheduleModule.forRoot(),
    OrderModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .exclude({
        path: 'billing/setup/shopify/redirect',
        method: RequestMethod.GET,
      })
      .forRoutes(
        UserController,
        CouponController,
        TenantController,
        StatisticController,
        OptinController,
        BillingController,
        BillingSettingsController,
        CreditController,
        EmailController,
        IntegrationController,
        IntegrationPlatformController,
        CouponReconciliationController,
        OrderReconciliationController,
        TenantBillingController,
      );
  }
}
