{"name": "convertecom-admin-server", "version": "0.0.1", "description": "Admin backend for ConvertEcom", "author": "", "license": "MIT", "scripts": {"build": "tsc -p tsconfig.build.json", "format": "prettier --write \"src/**/*.ts\"", "start": "ts-node -r tsconfig-paths/register src/main.ts", "start:dev": "nodemon", "start:debug": "nodemon --config nodemon-debug.json", "start:prod": "node -r ./tsconfig-paths-bootstrap.js dist/src/main.js", "lint": "tslint -p tsconfig.json -c tslint.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/.bin/typeorm", "migrations": "./node_modules/.bin/ts-node -r tsconfig-paths/register ./node_modules/.bin/typeorm migration:run", "migrations:old": "./node_modules/.bin/ts-node -r tsconfig-paths/register ./node_modules/.bin/typeorm migration:run --config ./ormconfig.json", "migrations:revert": "./node_modules/.bin/ts-node -r tsconfig-paths/register ./node_modules/.bin/typeorm migration:revert", "migrations:generate": "./node_modules/.bin/ts-node ./node_modules/.bin/typeorm migration:generate -n", "migrations:create": "./node_modules/.bin/ts-node ./node_modules/.bin/typeorm migration:create", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@honeycombio/opentelemetry-node": "^0.3.2", "@nestjs/common": "^6.10.11", "@nestjs/core": "^6.10.11", "@nestjs/jwt": "^6.0.0", "@nestjs/passport": "^6.0.0", "@nestjs/platform-express": "^6.10", "@nestjs/schedule": "1", "@nestjs/testing": "^6.0.0", "@nestjs/typeorm": "^6.3.4", "@opentelemetry/auto-instrumentations-node": "^0.36.3", "@sentry/node": "5.6.2", "aws-s3": "^2.0.5", "aws-sdk": "^2.576.0", "axios": "^0.19.0", "bcrypt": "^5.1.0", "cache-manager": "^3.4.0", "class-transformer": "^0.2.0", "class-validator": "^0.9.1", "dompurify": "^2.0.12", "dotenv": "^7.0.0", "helmet": "^3.18.0", "jest": "^23.6.0", "jsdom": "^16.2.2", "json2csv": "^4.5.3", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.11", "lossless-json": "^1.0.5", "mailchimp-api-v3": "^1.13.1", "moment": "^2.24.0", "moment-timezone": "^0.5.26", "nanoid": "^3.1.23", "nest-raven": "^5.0.0", "nodemon": "^1.18.9", "passport": "^0.4.0", "passport-http-bearer": "^1.0.1", "passport-jwt": "^4.0.0", "pg": "^8.9.0", "pg-connection-string": "^2.1.0", "prettier": "^1.15.3", "reflect-metadata": "^0.1.12", "request-ip": "^2.1.3", "rimraf": "^2.6.2", "rxjs": "^6.3.3", "ts-node": "^7.0.1", "tsconfig-paths": "^3.7.0", "typeorm": "^0.2.16"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@app/integration/(.*)$": "<rootDir>/integration/$1", "^@app/auth/(.*)$": "<rootDir>/auth/$1", "^@app/tenant/(.*)$": "<rootDir>/tenant/$1"}}, "devDependencies": {"@types/cache-manager": "^2.10.3", "@types/cron": "^1.7.3", "@types/dompurify": "^2.0.2", "@types/express": "^5.0.1", "@types/hapi__shot": "6.0.0", "@types/jest": "^23.3.13", "@types/json2csv": "^4.5.0", "@types/lossless-json": "^1.0.1", "@types/moment": "^2.13.0", "@types/moment-timezone": "^0.5.12", "@types/multer": "^1.4.12", "@types/node": "^22.14.1", "@types/supertest": "^2.0.7", "supertest": "^3.4.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "tslint": "5.12.1", "typescript": "4.9"}, "engines": {"node": "20.x"}}