const merge = require('webpack-merge');
const common = require('./webpack.common');
const Dotenv = require('dotenv-webpack');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

const bundleAnalyzer = process.env.ANALYZE ? [new BundleAnalyzerPlugin()] : [];

module.exports = merge(common, {
    plugins: [
        new Dotenv({
            path: './react-config.prod',
            save: true,
        }),
      ...bundleAnalyzer,
    ]
});
