{"defaultSeverity": "error", "extends": ["tslint:recommended", "tslint-react"], "jsRules": {"no-unused-expression": true}, "rules": {"quotemark": [true, "single", "jsx-double", "avoid-escape"], "member-access": [false], "ordered-imports": [false], "max-line-length": [true, 150], "member-ordering": [false], "interface-name": [false], "arrow-parens": false, "object-literal-sort-keys": false, "jsx-no-multiline-js": false, "object-literal-key-quotes": false, "no-console": false, "jsx-wrap-multiline": ["error", {"declaration": false, "assignment": false, "prop": "ignore"}]}, "rulesDirectory": []}