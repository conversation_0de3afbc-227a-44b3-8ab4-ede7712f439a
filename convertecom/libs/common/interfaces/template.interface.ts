export interface TemplateValue {
  text: string;
  color?: string;
  backgroundColor?: string;
  colorRgba?: { r: number; g: number; b: number; a?: number };
}

export interface Template {
  mainHeader: TemplateValue;
  mainSubHeader: TemplateValue;
  optIn: TemplateValue;
  optInButton: TemplateValue;
  optOutLink: TemplateValue;
  optOutExtra: TemplateValue;
  optInResultHeader: TemplateValue;
  optInResult: TemplateValue;
  optInResultLink: TemplateValue;
  cart: TemplateValue;
  cartButton: TemplateValue;
  cartAmount: TemplateValue;
  cartExpired: TemplateValue;
  cartExpiredButton: TemplateValue;
  cartInvalid: TemplateValue;
  cartInvalidButton: TemplateValue;
  cartPopupIconGradient: TemplateValue;
  popupDelay: number;
  optOutTtl?: number;
  floating?: boolean;
}
