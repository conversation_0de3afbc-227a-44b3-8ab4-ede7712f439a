# Zing Frontend App and API

## Installation

```bash
$ yarn install
```

## Running the app

```bash
# development
$ yarn client:dev # Start frontend
$ yarn start:dev # Start backend
```

## Tenant Lifecycle
### App Installed
When a tenant adds the app in Shopify the first time, Shopify sends a signed GET request to /oauth. If the shop
does not exist it is created, and the user logs in.

### App Uninstalled
When the app is uninstalled, Shopify sends a request to `/webhook/shopify/uninstall` which deactivates
the tenant. A tenant can be reactivated by reinstalling the app.

## Integrations

### Recart Messenger Plugin integration for <partner_name>
> Important: the Widget does NOT work insisde an iframe, it's a security policy defined by Facebook

>It's important to handle different widget types since, we are integrating with multiple apps.
 * widget types:
    ```
    atc
    discount_widget
    atc_popup
    wheelio
    optimonk
    justuno
    <partner_name>
    zipify
    ```
* states
    ```
     STATE_NOT_LOADED
     STATE_INITIAL
     STATE_SUBSCRIBED
     STATE_ERROR
     ```
* event structure, example for `state_change`
   ```javascript
   {
     event: "state_change",
     widget: {
       id: "_rmw-29679c-<partner_name>",
       type: "<partner_name>",
       state: "STATE_INITIAL"
     }
     state: "STATE_INITIAL"
   }
   ```
* event structure, example for `subscribe_clicked`
    ```json
    {
      event: "subscribe_clicked"
      isTrusted: true,
      target: button.subscription-selector,
      widget: {
        id: "_rmw-343d86-<partner_name>",
        type: "<partner_name>",
        state: "STATE_SUBSCRIBED",
        isCheckboxChecked: true
      }
    }
    ```
 > Note: no need for loader scripts, plugin presents on all Shopify stores which are using Recart
 
 First you have to insert the following html to the place, where you want our widget to render:
 ```html
 <div class="recart-messenger-widget" data-source="<partner_name>" data-smart-display="on"></div>
 ```
> Important: By using data-smart-display attribute you can force smart login feature.
''
 Then add the following code to your js:
```js
window.onRecartMessengerPluginLoaded = function () {
  _rmp.initWidget(['.subscription-selector'])

  let retryCount = 0
  _rmp.on('state_change', function (e) {
    if (e.widget && e.widget.type === '<partner_name>') {
      if (e.state === 'STATE_NOT_LOADED') {
        if (retryCount < 3) {
          retryCount++
          return
        } else {
          // TODO: widget will be hidden, you can show e-mail subscription to user
          // NOTE: do not add any css to the widget, it it self-contained and handle its visibility
        }
      } else if (e.state === 'STATE_INITIAL') {
        // TODO: checkbox is visible, you can display the popup
        console.log({state: 'STATE_INITIAL'})
      } else if (e.state === 'STATE_ERROR') {
        console.log({state: 'STATE_ERROR'})
        // TODO: popup must be hidden
      }
    }
  })

  _rmp.on('subscribe_clicked', function (e) {
    if (e.widget && e.widget.type === '<partner_name>') {
      if (e.widget.isCheckboxChecked) {
        // TODO: checkbox checked, user is going to be subscribed
      } else {
        // TODO: checkbox is unchecked, user will not be subscribed (shake animation/visual notice to user to click the checkbox)
      }
    }
  })

  _rmp.on('fbsdk_not_loaded', function (e) {
    // This error gets fired when we was not able to download Facebook SDK, this event is global, at this point no widget got rendered 
    // TODO: in this case widget won't get rendered, you should implement an error handling to fall back to e-mail supsription
  })

  _rmp.on('internal_error', fucntion (e) {
    // This event is fired when there was an error to set up widgets, this event is global, at this point no widget got rendered
    // TODO: in this case widget won't get rendered, you should implement an error handling to fall back to e-mail supsription
  })
}
```
>> __Note__: In order to not to break the code in older browsers, I suggest you to use transpilers for the code.

