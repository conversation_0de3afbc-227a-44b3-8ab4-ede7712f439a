import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { EntityManager, Repository } from "typeorm";
import { ApiSyncQueue, ApiSyncQueueType } from "./api-sync-queue.entity";
import { Coupon } from "../coupon/coupon.entity";
import { Tenant } from "../tenant/tenant.entity";
import { Optin } from "../optin/optin.entity";

@Injectable()
export class ApiSyncQueueService {
  constructor(
    @InjectRepository(ApiSyncQueue)
    private readonly apiSyncQueueRepository: Repository<ApiSyncQueue>,
  ) {}

  async addCouponJob(coupon: Coupon, tenant: Tenant, entityManager?: EntityManager): Promise<ApiSyncQueue> {
    if (entityManager) {
      return await entityManager.getRepository(ApiSyncQueue).save({coupon, tenant});
    }
    return await this.apiSyncQueueRepository.save({ coupon, tenant });
  }

  async addTenantJob(tenant: Tenant, entityManager?: EntityManager): Promise<ApiSyncQueue> {
    if (entityManager) {
      return await entityManager.getRepository(ApiSyncQueue).save({tenant, type: ApiSyncQueueType.IMPORT_ALL_ORDERS});
    }
    return await this.apiSyncQueueRepository.save({
      tenant,
      type: ApiSyncQueueType.IMPORT_ALL_ORDERS,
    });
  }

  async removeCouponJob(tenant: Tenant) {
    return await this.apiSyncQueueRepository
      .createQueryBuilder()
      .delete()
      .where('"tenantId" = :tenantId', { tenantId: tenant.id })
      .execute();
  }

  async addCustomerJob(optin: Optin, tenant: Tenant): Promise<ApiSyncQueue> {
    return await this.apiSyncQueueRepository.save({
      optin,
      tenant,
      type: ApiSyncQueueType.OPTIN,
    });
  }
}
