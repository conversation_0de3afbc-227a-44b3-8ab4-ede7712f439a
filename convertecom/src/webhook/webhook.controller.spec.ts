import { Test, TestingModule } from '@nestjs/testing';
import { WebhookController } from './webhook.controller';
import { getMockCouponProvider } from '../coupon/mocks/repository';
import { getMockTenantProvider } from '../tenant/mocks/repository';
import { CouponService } from '../coupon/coupon.service';
import { TenantService } from '../tenant/tenant.service';
import { WebhookService } from './webhook.service';
import { getMockOrderCreatedPayload } from './mock/webhook-payload';

describe('Webhook Controller', () => {
  let controller: WebhookController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CouponService,
        TenantService,
        WebhookService,
        getMockCouponProvider(),
        getMockTenantProvider(),
      ],
      controllers: [WebhookController],
    }).compile();

    controller = module.get<WebhookController>(WebhookController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('shopifyOrderCreated()', () => {
    it('should be defined', () => {
      expect(controller.shopifyOrderCreated).toBeDefined();
    });

    it('should return ???', async () => {});
  });

  describe('shopifyOrderPaid()', () => {
    it('should be defined', () => {
      expect(controller.shopifyOrderPaid).toBeDefined();
    });

    it('should return an error if there is no webhook token header', async () => {});

    it('should return ???', async () => {});
  });

  describe('shopifyGdprRedactCustomer()', () => {
    it('should be defined', () => {
      expect(controller.shopifyGdprRedactCustomer).toBeDefined();
    });

    it('should return ???', async () => {
      const result = await controller.shopifyGdprRedactCustomer();
      expect(result).toBeTruthy();
    });
  });

  describe('shopifyGdprRedactShop()', () => {
    it('should be defined', () => {
      expect(controller.shopifyGdprRedactShop).toBeDefined();
    });

    it('should return ???', async () => {
      const result = await controller.shopifyGdprRedactShop();
      expect(result).toBeTruthy();
    });
  });

  describe('shopifyGdprRequest()', () => {
    it('should be defined', () => {
      expect(controller.shopifyGdprRedactShop).toBeDefined();
    });

    it('should return ???', async () => {
      const result = await controller.shopifyGdprRequest();
      expect(result).toBeTruthy();
    });
  });
});
