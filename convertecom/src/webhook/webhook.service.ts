import { Injectable, Logger } from '@nestjs/common';
import { sumBy } from 'lodash';
import { In, Repository } from 'typeorm';

import { ApiSyncQueueService } from '../api-sync-queue/api-sync-queue.service';
import { Coupon } from '../coupon/coupon.entity';
import { CouponService } from '../coupon/coupon.service';
import { Order } from '../order/order.entity';
import { OrderService } from '../order/order.service';
import { Tenant } from '../tenant/tenant.entity';
import { TenantService } from '../tenant/tenant.service';
import { DiscountCode, ShopifyWebhook } from './dto/shopify-webhook.dto';
import { shopifyWebhookPayloadToOrderDto } from './mappers';
import { InjectRepository } from '@nestjs/typeorm';
import { GdprLog } from '../gdpr-log/gdpr-log.entity';

@Injectable()
export class WebhookService {
  private readonly log = new Logger(WebhookService.name, true);
  constructor(
    @InjectRepository(GdprLog)
    private readonly gdprLogRepository: Repository<GdprLog>,
    private readonly couponService: CouponService,
    private readonly apiSyncQueueService: ApiSyncQueueService,
    private readonly orderService: OrderService,
    private readonly tenantService: TenantService,
  ) { }

  async findRedeemedCoupons(webhookPayload: ShopifyWebhook, tenant: Tenant) {
    let redeemedCoupons = [];
    const discountCodes = webhookPayload.discount_codes || [];
    const codes = discountCodes.map(o => o.code);

    if (codes.length > 0) {
      redeemedCoupons = await this.couponService.findAll({
        code: In(codes),
        tenant,
      });
    }

    return redeemedCoupons;
  }

  // Use the sum from Shopify's payload, not from what our coupon says
  // (mainly for default fallback coupons, which don't have a dollar amount in our DB)
  getConvertEcomDiscountSum(
    redeemedCoupons: Coupon[],
    webhookPayload: ShopifyWebhook,
  ): number {
    const redeemedCodes = redeemedCoupons.map(o => o.code);
    return sumBy(
      webhookPayload.discount_codes,
      (discountCode: DiscountCode) => {
        return redeemedCodes.includes(discountCode.code)
          ? Number(discountCode.amount)
          : 0;
      },
    );
  }

  async handleEventOrderCreated(
    webhookPayload: ShopifyWebhook,
    tenant: Tenant,
  ) {
    const redeemedCoupons = await this.findRedeemedCoupons(
      webhookPayload,
      tenant,
    );
    const orderDto = shopifyWebhookPayloadToOrderDto(webhookPayload);

    orderDto.convertEcomDiscounts = this.getConvertEcomDiscountSum(
      redeemedCoupons,
      webhookPayload,
    );

    const redeemerOrder = await this.orderService.updateOrCreate({
      ...orderDto,
      redeemedCoupons,
      tenant,
    });

    Logger.debug(
      `New order ${redeemerOrder.externalOrderId} redeemed ${redeemedCoupons.length
      } coupons`,
    );

    return redeemerOrder;
  }

  async handleEventOrderPaid(
    webhookPayload: ShopifyWebhook,
    tenant: Tenant,
  ): Promise<Order> {
    const redeemedCoupons = await this.findRedeemedCoupons(
      webhookPayload,
      tenant,
    );
    const orderDto = shopifyWebhookPayloadToOrderDto(webhookPayload);

    orderDto.convertEcomDiscounts = this.getConvertEcomDiscountSum(
      redeemedCoupons,
      webhookPayload,
    );

    const originatorOrder = await this.orderService.updateOrCreate({
      ...orderDto,
      redeemedCoupons,
      tenant,
    });

    if (!this.orderService.shouldGenerateCoupon(originatorOrder)) {
      return;
    }

    try {
      const coupon = await this.couponService.create(
        { originatorOrder },
        tenant,
      );
      await this.apiSyncQueueService.addCouponJob(coupon, tenant);
      Logger.debug(
        `Paid order ${originatorOrder.externalOrderId} created coupon ${coupon.id
        }`,
      );
      return originatorOrder;
    } catch (e) {
      if (e.code === '23505') {
        // Unique constraint failure, which probably means this order has already generated a coupon, so stop here.
        Logger.debug(
          `Paid order ${originatorOrder.externalOrderId
          } failed to generate a new coupon because it is already an originator.`,
        );
      } else {
        // Other error. Rethrow.
        throw e;
      }
    }
  }

  async handleEventGdprRequest(tenant: Tenant, payload: any) {
    return await this.gdprLogRepository.save({
      tenant,
      rawData: payload,
      action: 'customer_data_request',
    });
  }

  async handleEventGdprRedactCustomer(tenant: Tenant, payload: any) {
    return await this.gdprLogRepository.save({
      tenant,
      rawData: payload,
      action: 'customer_redact',
    });
  }

  async handleEventGdprRedactShop(tenant: Tenant, payload: any) {
    return await this.gdprLogRepository.save({
      tenant,
      rawData: payload,
      action: 'shop_redact',
    });
  }
}
