import { find, get } from 'lodash';
import { ShopifyWebhook } from '../dto/shopify-webhook.dto';
import { OrderDto } from '../../order/dto/order.dto';

export function shopifyWebhookPayloadToOrderDto(
  payload: ShopifyWebhook,
): OrderDto {
  const optin = find(payload.note_attributes, { name: 'zingUserId' });
  const customerId = get(payload, 'customer.id');

  return {
    externalOrderId: payload.id.toString(),
    externalCustomerId: customerId ? customerId.toString() : null,
    optinId: optin ? optin.value : null,
    totalPrice: payload.total_price || 0,
    subtotalPrice: payload.subtotal_price || 0,
    totalTax: payload.total_tax || 0,
    totalDiscounts: payload.total_discounts || 0,
    externalCreatedAt: payload.created_at,
    externalPaymentAt: payload.updated_at,
  };
}
