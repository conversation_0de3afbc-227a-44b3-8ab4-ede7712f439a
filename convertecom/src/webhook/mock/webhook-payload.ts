import { random } from 'lodash';
import { ShopifyWebhook } from '../dto/shopify-webhook.dto';

// TODO confirm payload with real data
export function getMockOrderCreatedPayload(): ShopifyWebhook {
  return {
    id: random(1000, 9999) + '',
    token: 'abc123',
    cart_token: 'abcdef123456',
    created_at: new Date(),
    updated_at: new Date(),
    line_items: [], // TODO
    name: 'foo bar',
    discount_codes: [{ code: 'abc123', amount: '1.00', type: 'fixed-amount' }],
    total_discounts: 5.0,
    total_line_items_price: 100.0, //TODO
    total_price: 100.0, // TODO
    total_tax: 5.0,
    subtotal_price: 100.0,
    customer: { id: 'foobar1234567890' },
    note_attributes: [{ name: 'zingUserId', value: '12345' }],
  };
}

// TODO confirm payload with real data
export function getMockOrderPaidPayload(): ShopifyWebhook {
  return {
    id: random(1000, 9999) + '',
    token: 'abc123',
    cart_token: 'abcdef123456',
    created_at: new Date(),
    updated_at: new Date(),
    line_items: [], // TODO
    name: 'foo bar',
    discount_codes: [], // TODO
    total_discounts: 5.0,
    total_line_items_price: 100.0, //TODO
    total_price: 100.0, // TODO
    total_tax: 5.0,
    subtotal_price: 100.0,
    customer: { id: 'foobar1234567890' },
    note_attributes: [{ name: 'zingUserId', value: '12345' }],
  };
}

export function getMockGdprRequestPayload() {
  return {}; // TODO
}

export function getMockGdprRedactCustomerPayload() {
  return {}; // TODO
}

export function getMockGdprRedactShopPayload() {
  return {}; // TODO
}
