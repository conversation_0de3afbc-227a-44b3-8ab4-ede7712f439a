import { Test, TestingModule } from '@nestjs/testing';
import { WebhookService } from './webhook.service';
import { getMockCouponProvider } from '../coupon/mocks/repository';
import { getMockTenantProvider } from '../tenant/mocks/repository';
import { CouponService } from '../coupon/coupon.service';
import { TenantService } from '../tenant/tenant.service';
import { getMockShopifyTenant } from '../tenant/mocks/entity';
import {
  getMockOrderPaidPayload,
  getMockOrderCreatedPayload,
} from './mock/webhook-payload';

describe('WebhookService', () => {
  let service: WebhookService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CouponService,
        TenantService,
        WebhookService,
        getMockCouponProvider(),
        getMockTenantProvider(),
      ],
    }).compile();

    service = module.get<WebhookService>(WebhookService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('handleEventOrderCreated()', () => {
    it('should be defined', () => {
      expect(service.handleEventOrderCreated).toBeDefined();
    });

    it('should return ???', async () => {
      const payload = getMockOrderCreatedPayload();
      const tenant = getMockShopifyTenant();

      const result = await service.handleEventOrderCreated(payload, tenant);

      expect(result).toBeTruthy();
      // TODO verify the other fields in the response once i know what they are (Coupon[] ?)
    });
  });

  describe('handleEventOrderPaid()', () => {
    it('should be defined', () => {
      expect(service.handleEventOrderPaid).toBeDefined();
    });

    it('should return a new pending coupon', async () => {
      const payload = getMockOrderPaidPayload();
      const tenant = getMockShopifyTenant();

      const result = await service.handleEventOrderPaid(payload, tenant);

      expect(result).toBeTruthy();
      expect(result.reservedId).toBeNull();
      expect(result.reservedUntil).toBeNull();
    });

    // Note that we'll test whether the percentage is correct within the coupon service. This is merely checking that the percentage and amount are being calculated on the webhook.
    it(`should return a coupon with a correctly calculated non-zero amount and percentage`, async () => {
      const payload = getMockOrderPaidPayload();
      const tenant = getMockShopifyTenant();

      const result = await service.handleEventOrderPaid(payload, tenant);

      expect(result).toBeTruthy();
      expect(result.couponAmount).toBeGreaterThan(0);
      expect(result.percentage).toBeGreaterThan(0);
      expect(result.couponAmount).toBeCloseTo(
        result.percentage * result.orderAmount,
      );
    });
  });

  describe('handleEventGdprRequest()', () => {
    it('should be defined', () => {
      expect(service.handleEventGdprRequest).toBeDefined();
    });

    it('should return ???', async () => {
      const result = await service.handleEventGdprRequest();
      expect(result).toBeTruthy();
    });
  });

  describe('handleEventGdprRedactCustomer()', () => {
    it('should be defined', () => {
      expect(service.handleEventGdprRedactCustomer).toBeDefined();
    });

    it('should return ???', async () => {
      const result = await service.handleEventGdprRedactCustomer();
      expect(result).toBeTruthy();
    });
  });

  describe('handleEventGdprRedactShop()', () => {
    it('should be defined', () => {
      expect(service.handleEventGdprRedactShop).toBeDefined();
    });

    it('should return ???', async () => {
      const result = await service.handleEventGdprRedactShop();
      expect(result).toBeTruthy();
    });
  });
});
