import { Module } from '@nestjs/common';
import { TenantModule } from '../tenant/tenant.module';
import { WebhookService } from './webhook.service';
import { WebhookController } from './webhook.controller';
import { CouponModule } from '../coupon/coupon.module';
import { OrderModule } from '../order/order.module';
import { ApiSyncQueueModule } from '../api-sync-queue/api-sync-queue.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GdprLog } from '../gdpr-log/gdpr-log.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([GdprLog]),
    ApiSyncQueueModule,
    CouponModule,
    OrderModule,
    TenantModule
  ],
  controllers: [WebhookController],
  providers: [WebhookService],
})
export class WebhookModule {}
