import {
  Controller,
  Body,
  Post,
  UnauthorizedException,
  Req,
  Request,
  BadRequestException,
  Logger,
  Param,
  Headers,
} from '@nestjs/common';
import { ShopifyWebhook } from './dto/shopify-webhook.dto';
import { TenantService } from '../tenant/tenant.service';
import { Tenant } from '../tenant/tenant.entity';

import * as crypto from 'crypto';
import { WebhookService } from './webhook.service';
import { ShopifyWebhookBody } from './decorators/shopify-webhook-body.decorator';
import { ApiSyncQueueService } from '../api-sync-queue/api-sync-queue.service';

@Controller('webhook')
export class WebhookController {
  private readonly log = new Logger(WebhookController.name, true);
  constructor(
    private readonly tenantService: TenantService,
    private readonly webhookService: WebhookService,
    private readonly apiSyncQueueService: ApiSyncQueueService,
  ) {}

  // TODO maybe put this in a guard/middleware?
  private async getTenantFromShopifyRequest(req: any): Promise<Tenant> {
    // TODO have to use req:any because otherwise it doesn't know what req.rawBody is
    const requestToken = req.headers['x-shopify-hmac-sha256'];
    // TODO should cache tenant
    const tenant = await this.tenantService.findOneByExternalId(
      req.headers['x-shopify-shop-domain'],
    );

    if (!tenant) {
      throw new BadRequestException('Tenant not found');
    }

    // Public app integration will use shared secret, private app will use webhookSecret
    const secret =
      tenant.apiCredentials.webhookSecret || process.env.SHOPIFY_API_SECRET;

    const signature = crypto
      .createHmac('sha256', secret)
      .update(req.rawBody)
      .digest('base64');

    if (signature !== requestToken) {
      throw new UnauthorizedException('Invalid webhook token');
    }

    return tenant;
  }

  @Post('shopify/order-paid')
  async shopifyOrderPaid(
    @ShopifyWebhookBody() webhookPayload: ShopifyWebhook,
    @Req() req: Request,
  ) {
    const tenant = await this.getTenantFromShopifyRequest(req);
    await this.webhookService.handleEventOrderPaid(webhookPayload, tenant);
    return { status: true };
  }

  @Post('internal/:tenantId/reconcile-order')
  async internalReconcileOrder(
    @Param('tenantId') tenantId: string,
    @ShopifyWebhookBody() webhookPayload: ShopifyWebhook,
    @Headers('x-zing-service-token') zingServiceToken: string,
  ) {
    this.log.log(`Processing internal webhook for order ${webhookPayload.id} for tenant ${tenantId}`);
    if (zingServiceToken !== process.env.ZING_SERVICE_TOKEN) {
      this.log.error(`Unauthorized access to order ${webhookPayload.id} for tenant ${tenantId}`);
      throw new UnauthorizedException();
    }
    const tenant = await this.tenantService.findOne(tenantId);
    await this.webhookService.handleEventOrderPaid(webhookPayload, tenant);
    return { status: true };
  }

  @Post('shopify/order-created')
  async shopifyOrderCreated(
    @ShopifyWebhookBody() webhookPayload: ShopifyWebhook,
    @Req() req: Request,
  ) {
    const tenant = await this.getTenantFromShopifyRequest(req);
    await this.webhookService.handleEventOrderCreated(webhookPayload, tenant);
    return { status: true };
  }

  @Post('shopify/uninstall')
  async shopifyUninstall(@Req() req: Request) {
    const tenant = await this.getTenantFromShopifyRequest(req);
    // remove any items in the api_sync_queue table associated with specific tenant
    // await this.apiSyncQueueService.removeCouponJob(tenant);
    // remove tenant api and billing credentials from db
    await this.tenantService.deactivate(tenant.id);
    return { status: true };
  }

  // GDPR webhooks are merely logged and handled manually later

  @Post('shopify/gdpr-redact-customer')
  async shopifyGdprRedactCustomer(@Body() payload: any, @Req() req: Request) {
    const tenant = await this.getTenantFromShopifyRequest(req);
    return await this.webhookService.handleEventGdprRedactCustomer(
      tenant,
      payload,
    );
  }

  @Post('shopify/gdpr-redact-shop')
  async shopifyGdprRedactShop(@Body() payload: any, @Req() req: Request) {
    const tenant = await this.getTenantFromShopifyRequest(req);
    return await this.webhookService.handleEventGdprRedactShop(tenant, payload);
  }

  @Post('shopify/gdpr-request')
  async shopifyGdprRequest(@Body() payload: any, @Req() req: Request) {
    const tenant = await this.getTenantFromShopifyRequest(req);
    return await this.webhookService.handleEventGdprRequest(tenant, payload);
  }
}
