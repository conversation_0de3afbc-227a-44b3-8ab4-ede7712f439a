import { Test, TestingModule } from '@nestjs/testing';
import { CouponService } from './coupon.service';
import { getMockCouponProvider } from './mocks/repository';
import { getMockShopifyTenant } from '../tenant/mocks/entity';
import { getMockReservedCoupon } from './mocks/entity';
import { Order } from '../order/order.entity';
import { Coupon } from './coupon.entity';
import { CouponRepository } from './coupon.repository';
import { Tenant } from '../tenant/tenant.entity';
import { getRepositoryToken } from '@nestjs/typeorm';

describe('CouponService', () => {
  let service: CouponService;
  let repository: CouponRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CouponService,
        getMockCouponProvider(),
        {
          provide: getRepositoryToken(CouponRepository),
          useClass: CouponRepository,
        },
      ],
    }).compile();

    service = module.get<CouponService>(CouponService);
    repository = module.get(getRepositoryToken(CouponRepository));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateCode()', () => {
    it('should be defined', () => {
      expect(service.generateCode).toBeDefined();
    });

    it('should generate a code if the tenant has no special rules set', () => {
      const tenant = getMockShopifyTenant();
      const code = service.generateCode(tenant);

      expect(code).toBeTruthy();
    });

    // TODO verify basic tenant configuration rules (ex: length, valid characters)
    it('should generate a code of the correct length if the "length" rule is set', () => {});

    it('should generate a code that only contains valid characters if the "allowedCharacters" rule is set', () => {});
  });

  describe('calculateCouponAmount', () => {
    it('should be defined', () => {
      expect(service.calculateCouponAmount).toBeDefined();
    });

    it('should correctly calculate a coupon', async () => {
      const tenant = getMockShopifyTenant();
      const originatorOrder: Order = { id: 'uuid', subtotalPrice: 10 } as Order;
      const coupon: Coupon = { tenant, originatorOrder } as Coupon;
      const result = await service.calculateCouponAmount(coupon);

      const expectedAmount =
        tenant.couponPercentage * coupon.originatorOrder.subtotalPrice;

      expect(result).toBeCloseTo(expectedAmount);
    });
  });

  describe('create()', () => {
    it('should be defined', () => {
      expect(service.create).toBeDefined();
    });

    /*
    // Commented out because the test was not maintained and the tests are off
    xit('should create and return a new "pending" coupon', async () => {
      const payload = { originatorOrderId: 123456, orderAmount: 10 };
      const tenant = getMockShopifyTenant();
      const result = await service.create(payload, tenant);

      expect(result).toBeTruthy();
      expect(result.reservedId).toBeNull();
      expect(result.reservedUntil).toBeNull();
      expect(result.orderAmount).toEqual(payload.orderAmount);
      expect(result.originatorOrderId).toEqual(payload.originatorOrderId);
    });*/
  });

  describe('findReservedCoupon()', () => {
    it('should be defined', () => {
      expect(service.findReservedCoupon).toBeDefined();
    });
  });

  describe('getOrUpdateReservedCoupon', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should be defined', () => {
      expect(service.getOrUpdateReservedCoupon).toBeDefined();
    });

    it('releases existing coupon if cart subtotal is below coupon minimum amount', async () => {
      const reservedId = 'user';
      const cartAmount = 100;
      const tenant = {
        reservationMinutes: 20,
        id: 'tenant_uuid',
        fallbackCoupon: { percentage: 0.05 },
      } as Tenant;
      const existingCoupon = { minimumCartSubtotal: 120 } as Coupon;

      const existingCouponSpy = jest
        .spyOn(service, 'findReservedCoupon')
        .mockImplementation(() => Promise.resolve(existingCoupon));
      const releaseByUserIdSpy = jest
        .spyOn(service, 'releaseCouponByUserId')
        .mockImplementation((id, t) => Promise.resolve());
      const reserveCouponSpy = jest
        .spyOn(repository, 'reserveCoupon')
        .mockImplementation(() => Promise.resolve({} as Coupon));

      await service.getOrUpdateReservedCoupon(reservedId, cartAmount, tenant);

      expect(existingCouponSpy).toHaveBeenCalledWith(reservedId, tenant);
      expect(releaseByUserIdSpy).toHaveBeenCalledWith(reservedId, tenant);
      expect(reserveCouponSpy).toHaveBeenCalledWith(
        reservedId,
        tenant.reservationMinutes,
        tenant.id,
        cartAmount,
        (tenant.fallbackCoupon.percentage * cartAmount).toFixed(2),
      );

      releaseByUserIdSpy.mockRestore();
      existingCouponSpy.mockRestore();
      reserveCouponSpy.mockRestore();
    });

    it('releases existing coupon if coupon discount is below fallback coupon amount', async () => {
      const reservedId = 'user';
      const cartAmount = 100;
      const tenant = {
        reservationMinutes: 20,
        id: 'tenant_uuid',
        fallbackCoupon: { percentage: 0.05 },
      } as Tenant;
      const existingCoupon = { minimumCartSubtotal: 6, couponAmount: 3 } as Coupon;

      const existingCouponSpy = jest
        .spyOn(service, 'findReservedCoupon')
        .mockImplementation(() => Promise.resolve(existingCoupon));
      const releaseByUserIdSpy = jest
        .spyOn(service, 'releaseCouponByUserId')
        .mockImplementation((id, t) => Promise.resolve());
      const reserveCouponSpy = jest
        .spyOn(repository, 'reserveCoupon')
        .mockImplementation(() => Promise.resolve({} as Coupon));

      await service.getOrUpdateReservedCoupon(reservedId, cartAmount, tenant);

      expect(existingCouponSpy).toHaveBeenCalledWith(reservedId, tenant);
      expect(releaseByUserIdSpy).toHaveBeenCalledWith(reservedId, tenant);
      expect(reserveCouponSpy).toHaveBeenCalledWith(
        reservedId,
        tenant.reservationMinutes,
        tenant.id,
        cartAmount,
        (tenant.fallbackCoupon.percentage * cartAmount).toFixed(2),
      );

      releaseByUserIdSpy.mockRestore();
      existingCouponSpy.mockRestore();
      reserveCouponSpy.mockRestore();
    });

    it('uses an existing coupon if the user\'s cart still supports it', async () => {
      const reservedId = 'user';
      const cartAmount = 100;
      const tenant = {
        reservationMinutes: 20,
        id: 'tenant_uuid',
        fallbackCoupon: { percentage: 0.05 },
      } as Tenant;
      const existingCoupon = { minimumCartSubtotal: 50, couponAmount: 10 } as Coupon;

      const existingCouponSpy = jest
        .spyOn(service, 'findReservedCoupon')
        .mockImplementation(() => Promise.resolve(existingCoupon));
      const releaseByUserIdSpy = jest
        .spyOn(service, 'releaseCouponByUserId')
        .mockImplementation((id, t) => Promise.resolve());
      const reserveCouponSpy = jest
        .spyOn(repository, 'reserveCoupon')
        .mockImplementation(() => Promise.resolve({} as Coupon));

      await service.getOrUpdateReservedCoupon(reservedId, cartAmount, tenant);

      expect(existingCouponSpy).toHaveBeenCalledWith(reservedId, tenant);
      expect(releaseByUserIdSpy).toHaveBeenCalledTimes(0);
      expect(reserveCouponSpy).toHaveBeenCalledTimes(0);

      releaseByUserIdSpy.mockRestore();
      existingCouponSpy.mockRestore();
      reserveCouponSpy.mockRestore();
    });

    it('reserves a coupon if none exist for a user', async () => {
      const reservedId = 'user';
      const cartAmount = 50;
      const tenant = {
        reservationMinutes: 20,
        id: 'tenant_uuid',
        fallbackCoupon: { percentage: 0.05 },
      } as Tenant;

      const existingCouponSpy = jest
        .spyOn(service, 'findReservedCoupon')
        .mockImplementation(() => Promise.resolve(undefined));
      const releaseByUserIdSpy = jest
        .spyOn(service, 'releaseCouponByUserId')
        .mockImplementation((id, t) => Promise.resolve());
      const reserveCouponSpy = jest
        .spyOn(repository, 'reserveCoupon')
        .mockImplementation(() => Promise.resolve({} as Coupon));

      await service.getOrUpdateReservedCoupon(reservedId, cartAmount, tenant);

      expect(existingCouponSpy).toHaveBeenCalledWith(reservedId, tenant);
      expect(releaseByUserIdSpy).toHaveBeenCalledTimes(0);
      expect(reserveCouponSpy).toHaveBeenCalledWith(
        reservedId,
        tenant.reservationMinutes,
        tenant.id,
        cartAmount,
        (tenant.fallbackCoupon.percentage * cartAmount).toFixed(2),
      );

      releaseByUserIdSpy.mockRestore();
      existingCouponSpy.mockRestore();
      reserveCouponSpy.mockRestore();
    });
  });
});
