import { EntityRepository, Repository } from 'typeorm';
import { Coupon } from './coupon.entity';
import { first } from 'lodash';
import { Logger } from '@nestjs/common';

@EntityRepository(Coupon)
export class CouponRepository extends Repository<Coupon> {
  private readonly log = new Logger(CouponRepository.name, true);
  async reserveCoupon(
    reservedId: string | number,
    reservationMinutes: number,
    tenantId: string,
    cartAmount: number,
    minimumCouponAmount: number | string,
    fallbackCouponMaxPercentage: number,
  ): Promise<Coupon> {
    const useSlow = false; // Math.random() < 0.2;
    let coupon: Coupon;
    const start = Date.now();
    if (useSlow) {
      coupon = await this.reserveCouponSlow(
        reservedId,
        reservationMinutes,
        tenantId,
        cartAmount,
        minimumCouponAmount,
      );
    } else {
      coupon = await this.reserveCouponWithBetterRandomness(
        reservedId,
        reservationMinutes,
        tenantId,
        cartAmount,
        minimumCouponAmount,
        fallbackCouponMaxPercentage
      );

      if (!coupon) {
        this.log.error('Unable to find coupon using where random() > .2 for '
        + `tenant ${tenantId}, cartAmount: ${cartAmount}, minimum coupon amount ${minimumCouponAmount}`);

        return coupon;
      }
    }
    this.log.log(
      `Reserved coupon for user ${reservedId}, ${tenantId} using ${
        useSlow ? 'ORDER BY RANDOM()' : 'where random() > .2'
      } in ${(Date.now() - start) / 1000}s`,
    );

    return coupon;
  }

  /**
   * @deprecated too slow
   */
  async reserveCouponSlow(
    reservedId: string | number,
    reservationMinutes: number,
    tenantId: string,
    cartAmount: number,
    minimumCouponAmount: number | string,
  ): Promise<Coupon> {
    const result = await this.query(
      `UPDATE coupon
      SET "reservedId" = $1, "reservedUntil" = NOW() + make_interval(mins=>$2), "updatedAt" = NOW()
      WHERE id = (
          SELECT c.id FROM coupon c
          JOIN platform_data pd ON pd."couponId" = c.id AND pd.key = $3
          WHERE c."tenantId" = $4
          AND c."isFallback" = false
          AND c."isDisabled" = false
          AND (c."reservedUntil" IS NULL OR c."reservedUntil" < NOW())
          AND c."minimumCartSubtotal" <= $5
          AND c."couponAmount" >= $6
          AND (SELECT COUNT(*) FROM coupon_redemption_orders_order x WHERE x."couponId" = c.id) = 0
          ORDER BY RANDOM()
          LIMIT 1
          FOR UPDATE SKIP LOCKED
      )
      RETURNING code, "reservedUntil", "couponAmount";`,
      [
        reservedId,
        reservationMinutes,
        'shopify_discountcode_id',
        tenantId,
        cartAmount,
        minimumCouponAmount,
      ],
    );
    return first(result);
  }

  /**
   * ORDER BY RANDOM() can be very slow because it has to look at every single row. Using where random() > .2
   * will give roughly 20% of the eligible coupons and then take only one. At worst case the customer will
   * get the fallback coupon.
   *
   * @deprecated not random enough
   */
  async reserveCouponWithPerformanceUpdate(
    reservedId: string | number,
    reservationMinutes: number,
    tenantId: string,
    cartAmount: number,
    minimumCouponAmount: number | string,
  ): Promise<Coupon> {
    const result = await this.query(
      `UPDATE coupon
      SET "reservedId" = $1, "reservedUntil" = NOW() + make_interval(mins=>$2), "updatedAt" = NOW()
      WHERE id = (
          SELECT c.id FROM coupon c
          JOIN platform_data pd ON pd."couponId" = c.id AND pd.key = $3
          WHERE c."tenantId" = $4
          AND c."isFallback" = false
          AND c."isDisabled" = false
          AND (c."reservedUntil" IS NULL OR c."reservedUntil" < NOW())
          AND c."minimumCartSubtotal" <= $5
          AND c."couponAmount" >= $6
          AND (SELECT COUNT(*) FROM coupon_redemption_orders_order x WHERE x."couponId" = c.id) = 0
          AND random() >= .2
          LIMIT 1
          FOR UPDATE SKIP LOCKED
      )
      RETURNING code, "reservedUntil", "couponAmount";`,
      [
        reservedId,
        reservationMinutes,
        'shopify_discountcode_id',
        tenantId,
        cartAmount,
        minimumCouponAmount,
      ],
    );
    return first(result);
  }

  /**
   * The performance fix method was faster, but tended to give lower discounts. This method takes the best of both methods in hopes to
   * make a faster, more random distribution of coupons.
   */
  async reserveCouponWithBetterRandomness(
    reservedId: string | number,
    reservationMinutes: number,
    tenantId: string,
    cartAmount: number,
    minimumCouponAmount: number | string,
    fallbackCouponMaxPercentage: number | string,
  ): Promise<Coupon> {
    const result = await this.query(
      `UPDATE coupon
       SET "reservedId" = $1,
           "reservedUntil" = NOW() + make_interval(mins=>$2),
           "updatedAt" = NOW()
       WHERE id = (
           SELECT val.id
           FROM (SELECT c.id
                 FROM coupon c
                          JOIN platform_data pd ON pd."couponId" = c.id AND pd.key = $3
                 WHERE c."tenantId" = $4
                   AND c."isFallback" = false
                   AND c."isDisabled" = false
                   AND (c."reservedUntil" IS NULL OR c."reservedUntil" < NOW())
                   AND c."minimumCartSubtotal" <= $5
                   AND c."couponAmount" >= $6
                   AND c."couponAmount" <= $7
                   AND (SELECT COUNT(*) FROM coupon_redemption_orders_order x WHERE x."couponId" = c.id) = 0
                   AND random() < .2
                 ORDER BY id
                 LIMIT 100) val
           ORDER BY random()
           LIMIT 1 FOR UPDATE SKIP LOCKED
       )
      RETURNING code, "reservedUntil", "couponAmount";`,
      [
        reservedId,
        reservationMinutes,
        'shopify_discountcode_id',
        tenantId,
        cartAmount,
        minimumCouponAmount,
        fallbackCouponMaxPercentage,
      ],
    );
    return first(result);
  }
}
