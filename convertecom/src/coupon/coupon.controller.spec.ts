import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { CouponController } from './coupon.controller';
import { CouponService } from './coupon.service';
import { TenantService } from '../tenant/tenant.service';
import { getMockCouponProvider } from './mocks/repository';
import { getMockTenantProvider } from '../tenant/mocks/repository';
import { getMockReservedCoupon } from './mocks/entity';

describe('Coupon Controller', () => {
  let couponController: CouponController;
  let couponService: CouponService;
  let tenantService: TenantService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CouponService,
        TenantService,
        getMockCouponProvider(),
        getMockTenantProvider(),
      ],
      controllers: [CouponController],
    }).compile();

    couponController = module.get<CouponController>(CouponController);
    couponService = module.get<CouponService>(CouponService);
    tenantService = module.get<TenantService>(TenantService);
  });

  it('should be defined', () => {
    expect(couponController).toBeDefined();
  });

  describe('reserve()', () => {
    it('should be defined', () => {
      expect(couponController.reserve).toBeDefined();
    });

    it('should be rejected if tenant is not found', async () => {
      jest
        .spyOn(tenantService, 'findOneByExternalId')
        .mockImplementation(async () => null);

      await expect(
        couponController.reserve({ storeKey: 1, userId: 'foobar' }),
      ).rejects.toBeInstanceOf(BadRequestException);
    });

    it('should turn an existing coupon if this user has already reserved one', async () => {
      const mock = getMockReservedCoupon();
      const expectedResult = {
        code: mock.code,
        amount: mock.couponAmount,
        reservedUntil: mock.reservedUntil,
      };

      jest
        .spyOn(couponService, 'findReservedCoupon')
        .mockImplementation(async () => mock);

      expect(
        await couponController.reserve({
          storeKey: 1,
          userId: 'alreadyhascoupon',
        }),
      ).toEqual(expectedResult);
    });

    it('should return a coupon if one is available', async () => {
      const mock = {
        code: '12345',
        couponAmount: '1.23',
        reservedUntil: '2019-01-01T00:10:00Z',
      };
      const expectedResult = {
        code: mock.code,
        amount: mock.couponAmount,
        reservedUntil: mock.reservedUntil,
      };

      jest
        .spyOn(couponService, 'findReservedCoupon')
        .mockImplementation(async () => null);
      jest
        .spyOn(couponService, 'getOrUpdateReservedCoupon')
        .mockImplementation(async () => mock);

      expect(
        await couponController.reserve({ storeKey: 1, userId: 'foobar' }),
      ).toEqual(expectedResult);
    });

    // TODO this should be changed to fall back to a default coupon (configurable per tenant)
    it('should return nothing if no coupon is available', async () => {
      const mock = null;
      const expectedResult = mock;

      jest
        .spyOn(couponService, 'findReservedCoupon')
        .mockImplementation(async () => null);
      jest
        .spyOn(couponService, 'getOrUpdateReservedCoupon')
        .mockImplementation(async () => mock);

      expect(
        await couponController.reserve({ storeKey: 1, userId: 'foobar' }),
      ).toEqual(expectedResult);
    });
  });
});
