import { getRepositoryToken } from '@nestjs/typeorm';
import { Coupon } from '../coupon.entity';
import { getMockReservedCoupon } from './entity';

const createMock = jest.fn(
  async (dto: any): Promise<Coupon> => {
    dto.reservedId = null;
    dto.reservedUntil = null;
    return dto;
  },
);

const saveMock = jest.fn(
  async (dto: any): Promise<Coupon> => {
    dto.reservedId = dto.reservedId || null;
    dto.reservedUntil = dto.reservedUntil || null;
    return dto;
  },
);

const findMock = jest.fn(
  async (conditions: any): Promise<Coupon[]> => {
    return [{ ...getMockReservedCoupon(), ...conditions }];
  },
);

const findOneMock = jest.fn(
  async (conditions: any): Promise<Coupon> => {
    return { ...getMockReservedCoupon(), ...conditions };
  },
);

const updateMock = jest.fn(async (conditions: any, values: any) => {
  return [];
});

const queryMock = jest.fn(async (query: string, params: any) => {
  return [];
});

export const MockCouponRepository = jest.fn().mockImplementation(() => {
  return {
    create: createMock,
    save: saveMock,
    find: findMock,
    findOne: findOneMock,
    update: updateMock,
    query: queryMock,
  };
});

export function getMockCouponProvider() {
  return {
    provide: getRepositoryToken(Coupon),
    useValue: new MockCouponRepository(),
  };
}
