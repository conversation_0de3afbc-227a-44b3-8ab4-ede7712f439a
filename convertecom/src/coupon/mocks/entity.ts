import { random } from 'lodash';

export function getMockReservedCoupon() {
  return {
    id: random(1000, 9999),
    originatorOrderId: random(1000, 9999),
    orderAmount: random(1, 50),
    percentage: 0.05,
    reservedId: random(1000, 9999),
    redeemerId: null,
    redeemedAt: null,
    code: '111111',
    couponAmount: 1.11,
    reservedUntil: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    tenant: null,
  };
}
