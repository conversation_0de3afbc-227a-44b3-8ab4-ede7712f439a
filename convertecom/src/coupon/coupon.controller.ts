import {
  Controller,
  Post,
  Body,
  UsePipes,
  ValidationPipe,
  BadRequestException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { CouponService } from './coupon.service';
import { TenantService } from '../tenant/tenant.service';
import { CouponDto } from './dto/coupon.dto';
import { ResponseCoupon } from './interfaces/response-coupon';
import { ReserveCouponDto } from './dto/reserve-coupon.dto';

@Controller('coupon')
export class CouponController {
  private readonly logger = new Logger(CouponController.name, true);
  constructor(
    private readonly couponService: CouponService,
    private readonly tenantService: TenantService,
  ) {}

  @Post('reserve')
  @UsePipes(new ValidationPipe({ transform: true }))
  async reserve(@Body() couponDto: ReserveCouponDto): Promise<ResponseCoupon> {
    const tenant = await this.tenantService.findOneByExternalId(
      couponDto.storeKey,
    );
    if (!tenant) {
      throw new BadRequestException('Tenant not found');
    }
    this.logger.log(`Reserving coupon for user ${couponDto.userId}, tenant ${tenant.id}, cart amount ${couponDto.cartAmount}`);

    const coupon = await this.couponService.getOrUpdateReservedCoupon(
      couponDto.userId,
      couponDto.cartAmount,
      tenant,
    );
    if (coupon) {
      this.logger.log(`Reserved coupon for user ${couponDto.userId}, coupon ${coupon.code}, amount ${coupon.couponAmount}`);
      return { code: coupon.code, reservedUntil: coupon.reservedUntil, couponAmount: coupon.couponAmount };
    }

    // Fall back to the default coupon if there are no real ones available
    const fallbackCoupon = tenant.fallbackCoupon;
    if (fallbackCoupon) {
      this.logger.log(`Reserved fallback coupon for user ${couponDto.userId}, coupon ${fallbackCoupon.code}`);
      return { code: fallbackCoupon.code, reservedUntil: null, couponAmount: 0, percentage: fallbackCoupon.percentage };
    }

    return null;
  }

  @Post('refresh')
  @UsePipes(new ValidationPipe({ transform: true }))
  async refresh(@Body() couponDto: CouponDto): Promise<ResponseCoupon> {
    const tenant = await this.tenantService.findOneByExternalId(
      couponDto.storeKey,
    );
    if (!tenant) {
      throw new BadRequestException('Tenant not found');
    }

    const coupon = await this.couponService.refreshCouponByUserId(
      couponDto.userId,
      tenant,
    );
    if (!coupon) {
      throw new BadRequestException('Invalid coupon');
    }

    return coupon;
  }

  @Post('check')
  @UsePipes(new ValidationPipe({ transform: true }))
  async check(@Body() couponDto: CouponDto): Promise<ResponseCoupon> {
    const tenant = await this.tenantService.findOneByExternalId(
      couponDto.storeKey,
    );
    if (!tenant) {
      throw new BadRequestException('Tenant not found');
    }

    const coupon = await this.couponService.findReservedCoupon(
      couponDto.userId,
      tenant,
      true,
    );
    if (!coupon || (couponDto.code && couponDto.code !== coupon.code)) {
      // See if it's the fallback coupon
      if (couponDto.code) {
        const fallbackCoupon = tenant.fallbackCoupon;
        if (fallbackCoupon && fallbackCoupon.code === couponDto.code) {
          return {
            code: fallbackCoupon.code,
            reservedUntil: null,
            couponAmount: 0,
            minimumCartSubtotal: 0,
            percentage: fallbackCoupon.percentage,
          };
        }
      }

      // Nothing reserved, invalid reservation, or it's not a fallback (or there is no fallback)
      throw new NotFoundException();
    }

    return {
      code: coupon.code,
      reservedUntil: coupon.reservedUntil,
      couponAmount: coupon.couponAmount,
      minimumCartSubtotal: coupon.minimumCartSubtotal,
      maximumCartSubtotal: Math.floor(coupon.couponAmount / tenant.couponPercentage * 100) / 100, // Ensure the customer gets at least the coupon percentage set by the tenant
    };
  }

  @Post('release')
  @UsePipes(new ValidationPipe({ transform: true }))
  async release(@Body() couponDto: CouponDto): Promise<void> {
    const tenant = await this.tenantService.findOneByExternalId(
      couponDto.storeKey,
    );
    if (!tenant) {
      throw new BadRequestException('Tenant not found');
    }

    await this.couponService.releaseCouponByUserId(couponDto.userId, tenant);
  }
}
