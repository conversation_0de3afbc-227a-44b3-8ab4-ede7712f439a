import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne
} from 'typeorm';
import { Tenant } from '../tenant/tenant.entity';

@Entity()
export class GdprLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  action: string;

  @ManyToOne(type => Tenant, tenant => tenant.gdprLogs)
  tenant: Tenant;

  @Column({ type: 'json' })
  rawData: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
