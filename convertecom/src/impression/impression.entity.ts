import { En<PERSON>ty, PrimaryGeneratedColumn, CreateDateColumn, ManyToOne, Column, OneToMany } from 'typeorm';
import { Tenant } from '../tenant/tenant.entity';
import { Optin } from '../optin/optin.entity';

@Entity()
export class Impression {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ nullable: true })
  externalId: string;

  @ManyToOne(type => Tenant, tenant => tenant.impressions)
  tenant: Tenant;

  @OneToMany(type => Optin, optin => optin.impression)
  optins: Optin[];
}
