import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Impression } from './impression.entity';
import { Repository } from 'typeorm';
import { Tenant } from '../tenant/tenant.entity';

@Injectable()
export class ImpressionService {
  constructor(
    @InjectRepository(Impression)
    private readonly impressionRepository: Repository<Impression>,
  ) {}

  async create(tenant: Tenant) {
    return await this.impressionRepository.save({ tenant });
  }
}
