// tslint:disable:max-classes-per-file

import { IsBoolean, IsEnum, IsString } from 'class-validator';

export enum IntegrationPlatformName {
  RECART = 'recart',
}

export enum RecartWidgetSkin {
  LIGHT = 'light',
  DARK = 'dark',
}

export abstract class IntegrationConfiguration {
  // tslint:disable-next-line:variable-name
  __platform: IntegrationPlatformName;

  toString() {
    return `IntegrationConfiguration${this.__platform}`;
  }
}

export class RecartConfiguration extends IntegrationConfiguration {
  @IsEnum(IntegrationPlatformName)
  // tslint:disable-next-line:variable-name
  __platform = IntegrationPlatformName.RECART;

  @IsString()
  source: string;

  @IsBoolean()
  smartDisplayEnabled: boolean;

  @IsBoolean()
  forceSendToMessenger: boolean;

  @IsEnum(RecartWidgetSkin)
  skin: RecartWidgetSkin;
}

/*
// Example
export class SomeConfiguration extends IntegrationConfiguration {
  // Add properties
}
*/

export type IntegrationConfigurations = RecartConfiguration; // | SomeConfiguration
