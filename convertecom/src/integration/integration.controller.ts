import { Controller, Get, Param } from '@nestjs/common';
import { IntegrationService } from './integration.service';

@Controller('')
export class IntegrationController {
  constructor(readonly integrationService: IntegrationService) {}

  @Get('tenant/:externalId/integrations')
  async forTenant(@Param('externalId') externalId: string) {
    return this.integrationService.tenantIntegrations(externalId);
  }
}
