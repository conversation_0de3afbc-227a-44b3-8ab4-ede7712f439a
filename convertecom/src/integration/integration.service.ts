import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Tenant } from '../tenant/tenant.entity';
import { Repository } from 'typeorm';
import {
  IntegrationConfiguration,
  IntegrationPlatformName,
  RecartConfiguration,
} from './configuration/integrations';
import { plainToClass } from 'class-transformer';

@Injectable()
export class IntegrationService {
  constructor(
    @InjectRepository(Tenant) private readonly tenantRepo: Repository<Tenant>,
  ) {}

  async tenantIntegrations(externalId: string) {
    const tenant = await this.tenantRepo.findOneOrFail({
      relations: ['integrations', 'integrations.platform'],
      where: {
        externalId,
        'integrations.platform': { name: 'recart' },
      },
    });

    return (await tenant.integrations).reduce((acc, integration) => {
      const name = integration.platform.name;
      integration.configuration = this.addDefaultConfiguration(
        integration.configuration,
      );
      delete integration.platform;
      delete integration.configuration.__platform;
      acc[name] = integration;
      return acc;
    }, {});
  }

  private addDefaultConfiguration(
    configuration: IntegrationConfiguration,
  ): IntegrationConfiguration {
    switch (configuration.__platform) {
      case IntegrationPlatformName.RECART: {
        return plainToClass(RecartConfiguration, {
          source: 'zing',
          smartDisplayEnabled: true,
          forceSendToMessenger: true,
          skin: 'dark',
          ...configuration,
        });
      }
      default: {
        return configuration;
      }
    }
  }
}
