import { Module } from '@nestjs/common';
import { IntegrationController } from './integration.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Integration } from './integration.entity';
import { Tenant } from '../tenant/tenant.entity';
import { IntegrationService } from './integration.service';

@Module({
  controllers: [IntegrationController],
  imports: [TypeOrmModule.forFeature([Integration, Tenant])],
  providers: [IntegrationService],
})
export class IntegrationModule {}
