import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Integration } from './integration.entity';
import { IntegrationPlatformName } from './configuration/integrations';

@Entity()
export class IntegrationPlatform {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  name: IntegrationPlatformName;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(type => Integration, ti => ti.platform)
  integrations: Promise<Integration[]>;
}
