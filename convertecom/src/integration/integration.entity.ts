import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>inColumn,
  ManyToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { IntegrationPlatform } from './integration-platform.entity';
import {
  IntegrationConfiguration,
  IntegrationPlatformName,
  RecartConfiguration,
} from './configuration/integrations';
import { Type } from 'class-transformer';
import { Tenant } from '../tenant/tenant.entity';

@Entity()
export class Integration {
  @PrimaryColumn('uuid')
  id: string;

  @Column('jsonb')
  @Type(() => IntegrationConfiguration, {
    discriminator: {
      property: '__platform',
      subTypes: [
        { name: IntegrationPlatformName.RECART, value: RecartConfiguration },
      ],
    },
  })
  configuration: IntegrationConfiguration;

  @Column()
  isEnabled: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(type => IntegrationPlatform, platform => platform.integrations)
  @JoinColumn()
  platform: IntegrationPlatform;

  @ManyToOne(type => Tenant, tenant => tenant.integrations, { lazy: true })
  tenant: Tenant;
}
