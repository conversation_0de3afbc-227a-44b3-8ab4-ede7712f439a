import { Module } from '@nestjs/common';
import '@shopify/shopify-api/adapters/node';
import { LATEST_API_VERSION, shopifyApi } from '@shopify/shopify-api';

export function shopifyFactory() {
  const hostName =
    process.env.SHOPIFY_V2_REDIRECT_HOST_NAME || process.env.BASE_URL;
  const hostScheme =
    process.env.SHOPIFY_V2_REDIRECT_HOST_SCHEME === 'http' ? 'http' : 'https';
  return shopifyApi({
    hostScheme,
    hostName: hostName.replace(/https?:\/\//, ''),
    apiKey: process.env.SHOPIFY_API_KEY,
    apiSecretKey: process.env.SHOPIFY_API_SECRET,
    scopes:['read_script_tags','write_script_tags','read_customers','write_customers','read_products', 'read_discounts', 'write_discounts', 'write_products', 'read_orders', 'write_orders','write_price_rules', 'read_price_rules'],
    apiVersion: LATEST_API_VERSION,
    isEmbeddedApp: false,
  });
}

export const SHOPIFY = Symbol('ShopifyService');
export type Shopify = ReturnType<typeof shopifyApi>;

@Module({
  providers: [
    {
      provide: SHOPIFY,
      useFactory: shopifyFactory,
    },
  ],
  exports: [SHOPIFY],
})
export class ShopifyModule { }
