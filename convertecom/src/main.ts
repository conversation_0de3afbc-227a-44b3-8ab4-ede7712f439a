let newrelic: any;
if (process.env.NEW_RELIC_LICENSE_KEY) {
  newrelic = require('newrelic');
}

import { config } from 'dotenv';
config();

import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { AppModule } from './app.module';

import * as Sentry from '@sentry/node';
import { RavenInterceptor } from 'nest-raven';
import * as helmet from 'helmet';
import * as bodyParser from 'body-parser';
import { HttpException } from '@nestjs/common';

async function bootstrap() {
  console.log("are you running?");
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  if (process.env.SENTRY_DSN) {
    Sentry.init({ dsn: process.env.SENTRY_DSN });
    app.use(Sentry.Handlers.requestHandler());
    app.useGlobalInterceptors(new RavenInterceptor({
      filters: [
        { type: HttpException, filter: (exception: HttpException) => 500 > exception.getStatus() }
      ],
    }));
  }

  if (newrelic) {
    app.use((req, res, next) => {
      req.newrelic = newrelic;
      next();
    });
  }

  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "https:", "'unsafe-inline'"],
        styleSrc: ["'self'", "https:", "'unsafe-inline'"],
        imgSrc: ["'self'", "data:", "https:"],
        frameAncestors: ["https://admin.shopify.com", "https://myshopify.com"],
      },
    },
  }));

  app.use((req, res, next) => {
    res.setHeader('Permissions-Policy', 'interest-cohort=()');
    next();
  });

  app.enableCors();

  app.use(
    bodyParser.json({
      limit: '10mb',
      verify: (req: any, res, buf, encoding) => {
        if (buf && buf.length) {
          req.rawBody = buf.toString(encoding || 'utf8');
        }
      },
    }),
  );

  console.log("port at which server runs", process.env.PORT);
  await app.listen(3000);
  console.log("server started successfully");
}

bootstrap();
