import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DeepPartial } from 'typeorm';
import { Order } from './order.entity';

// TODO this should probably be tenant-configurable
const rules = {
  minimumSubtotal: 0,
  allowNonZing: false,
};

@Injectable()
export class OrderService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
  ) {}

  async findOneByExternalId(externalOrderId: string): Promise<Order> {
    return await this.orderRepository.findOne({ externalOrderId });
  }

  async updateOrCreate(data: DeepPartial<Order>): Promise<Order> {
    const existingOrder = await this.findOneByExternalId(data.externalOrderId);
    if (existingOrder) {
      data.id = existingOrder.id;
    }

    return await this.orderRepository.save(data);
  }

  shouldGenerateCoupon(order: Order) {
    // Minimum subtotal
    if (order.subtotalPrice <= rules.minimumSubtotal) {
      return false;
    }

    // Don't generate a new Zing if the order redeemed a non-Zing coupon
    if (!rules.allowNonZing && order.totalDiscounts > order.convertEcomDiscounts) {
      return false;
    }

    return true;
  }
}
