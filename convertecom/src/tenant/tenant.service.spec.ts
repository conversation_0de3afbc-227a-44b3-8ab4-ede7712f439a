import { Test, TestingModule } from '@nestjs/testing';
import { TenantService } from './tenant.service';
import { getMockTenantProvider } from './mocks/repository';

describe('TenantService', () => {
  let service: TenantService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TenantService, getMockTenantProvider()],
    }).compile();

    service = module.get<TenantService>(TenantService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findOneByExternalId()', async () => {});
});
