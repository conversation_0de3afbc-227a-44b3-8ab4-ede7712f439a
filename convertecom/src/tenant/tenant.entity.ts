import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { Exclude, Expose } from 'class-transformer';
import { ApiCredentials } from './interfaces/api-credentials.interface';
import { Coupon } from '../coupon/coupon.entity';
import { Order } from '../order/order.entity';
import { EmailPlatformCredentials } from './interfaces/email-platform-credentials.interface';
import { BillingCredentials } from './interfaces/billing-credentials.interface';
import { Template } from '@lib/common';
import { GdprLog } from '../gdpr-log/gdpr-log.entity';
import { Impression } from '../impression/impression.entity';
import { Integration } from '../integration/integration.entity';
import {
  defaultOnboardingStatus,
  OnboardingStatus,
} from './interfaces/onboarding-status.interface';

@Exclude()
@Entity()
export class Tenant {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Expose()
  @Column('varchar')
  platform: string;

  @Expose()
  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalId: string | number;

  @Column('varchar')
  name: string;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 5,
    default: 0.05,
  })
  couponPercentage: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 5,
    default: 0.8,
  })
  minimumGrossPercentage: number;

  @Column({
    default: 10,
  })
  reservationMinutes: number;

  @Column({
    type: 'json',
    nullable: true,
  })
  apiCredentials: ApiCredentials;

  @Column({
    type: 'json',
    nullable: true,
  })
  emailPlatformCredentials: EmailPlatformCredentials;

  @Column({
    type: 'json',
    nullable: true,
  })
  billingCredentials: BillingCredentials;

  @Column({
    type: 'integer',
    default: 1,
  })
  queueBatchSize: number;

  @Expose()
  @Column({
    type: 'boolean',
    default: false,
  })
  active: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(type => Coupon, coupons => coupons.tenant)
  coupons: Coupon[];

  @OneToMany(type => Order, orders => orders.tenant)
  orders: Order[];

  @OneToMany(type => Impression, impression => impression.tenant)
  impressions: Impression[];

  @OneToOne(type => Coupon, { eager: true })
  @JoinColumn()
  fallbackCoupon: Coupon;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 5,
    nullable: true,
  })
  fallbackCouponMaxPercentage: number;

  @Expose()
  @Column({
    type: 'json',
    nullable: true,
  })
  template: Template;

  @Column({
    type: 'json',
    default: defaultOnboardingStatus,
  })
  onboardingStatus: OnboardingStatus;

  @Column({
    type: 'date',
    nullable: true,
  })
  trialEndsOn: Date;

  @OneToMany(type => GdprLog, gdprLog => gdprLog.tenant)
  gdprLogs: GdprLog[];

  @OneToMany(type => Integration, integration => integration.tenant)
  integrations: Promise<Integration[]>;
}
