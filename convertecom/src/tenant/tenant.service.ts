import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Tenant } from './tenant.entity';
import { EntityManager, Repository } from 'typeorm';

@Injectable()
export class TenantService {
  constructor(
    @InjectRepository(Tenant)
    private readonly tenantRepository: Repository<Tenant>,
  ) { }

  async findOne(id: string): Promise<Tenant> {
    return await this.tenantRepository.findOne(id);
  }

  async findOneByExternalId(id: number | string): Promise<Tenant | undefined> {
    return await this.tenantRepository.findOne({ externalId: id });
  }

  async save(
    data: Partial<Tenant>,
    entityManager?: EntityManager,
  ): Promise<Tenant> {
    if (entityManager) {
      return await entityManager.getRepository(Tenant).save(data);
    }
    return await this.tenantRepository.save(data);
  }

  /**
   * Deactivate or "soft uninstall" a tenant
   * @param id
   */
  async deactivate(id: number | string): Promise<any> {
    return await this.tenantRepository
      .createQueryBuilder()
      .update(Tenant)
      .set({
        apiCredentials: null,
        billingCredentials: null,
        active: false,
        onboardingStatus: {
          isBillingSetup: false,
          arePopupsCustomized: false,
          isCodeSnippetAdded: false,
          isConvertEcomActivated: false,
        },
        emailPlatformCredentials: null,
      })
      .where('id = :id', { id })
      .execute();
  }
}
