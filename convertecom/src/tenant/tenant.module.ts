import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Tenant } from './tenant.entity';
import { TenantService } from './tenant.service';
import { TenantController } from './tenant.controller';
import { ImpressionModule } from '../impression/impression.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Tenant]),
    ImpressionModule,
  ],
  providers: [TenantService],
  exports: [TenantService],
  controllers: [TenantController],
})
export class TenantModule {}
