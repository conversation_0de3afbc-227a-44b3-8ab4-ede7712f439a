import { random } from 'lodash';
import { Tenant } from '../tenant.entity';
import { defaultOnboardingStatus } from '../interfaces/onboarding-status.interface';

export function getMockShopifyTenant(): Tenant {
  return {
    id: random(1000, 9999) + '',
    externalId: 'test',
    name: 'Test Tenant',
    apiCredentials: {},
    couponPercentage: 0.05,
    fallbackCouponMaxPercentage: 0.5,
    minimumGrossPercentage: 0.05,
    reservationMinutes: 10,
    queueBatchSize: 1,
    platform: 'shopify',
    coupons: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    orders: [],
    fallbackCoupon: null,
    emailPlatformCredentials: null,
    billingCredentials: null,
    template: null,
    active: true,
    gdprLogs: [],
    impressions: [],
    integrations: Promise.resolve([]),
    onboardingStatus: defaultOnboardingStatus,
    trialEndsOn: null,
  };
}
