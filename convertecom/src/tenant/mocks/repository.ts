import { getRepositoryToken } from '@nestjs/typeorm';
import { random } from 'lodash';
import { Tenant } from '../tenant.entity';

const findOneTenantMock = jest.fn(async (conditions: any) => {
  return { id: random(1000, 9999) };
});

export const MockTenantRepository = jest.fn().mockImplementation(() => {
  return {
    findOne: findOneTenantMock,
  };
});

export function getMockTenantProvider() {
  return {
    provide: getRepositoryToken(Tenant),
    useValue: new MockTenantRepository(),
  };
}
