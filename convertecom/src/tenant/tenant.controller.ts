import { Controller, Get, Param, UseInterceptors, ClassSerializerInterceptor, Query } from '@nestjs/common';
import { TenantService } from './tenant.service';
import { ImpressionService } from '../impression/impression.service';
import { classToPlain } from 'class-transformer';

@Controller('tenant')
export class TenantController {
  constructor(
    private readonly tenantService: TenantService,
    private readonly impressionService: ImpressionService,
    ) {}

  @UseInterceptors(ClassSerializerInterceptor)
  @Get(':externalId')
  async getByExternalId(
    @Param('externalId') externalId: string,
    @Query('impressionId') impressionId: string,
  ) {
    const tenant = await this.tenantService.findOneByExternalId(externalId);
    if (!impressionId) {
      const impression = await this.impressionService.create(tenant);
      impressionId = impression.id;
    }
    // Wrap Tenant in classToPlain so that exposed properties are respected
    return { ...classToPlain(tenant), impressionId };
  }
}
