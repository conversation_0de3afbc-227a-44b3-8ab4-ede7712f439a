import { Module } from '@nestjs/common';
import { OauthController } from './oauth.controller';
import { OauthService } from './oauth.service';
import { TenantModule } from '../tenant/tenant.module';
import { AuthModule } from '../auth/auth.module';
import { ApiSyncQueueModule } from '../api-sync-queue/api-sync-queue.module';
import { CouponModule } from '../coupon/coupon.module';
import { ShopifyModule } from '../shopify/shopify.module';

@Module({
  imports: [
    AuthModule,
    TenantModule,
    ApiSyncQueueModule,
    CouponModule,
    ShopifyModule,
  ],
  controllers: [OauthController],
  providers: [OauthService],
})
export class OauthModule {}
