import {
  BadRequestException,
  Controller,
  Get,
  Inject,
  InternalServerErrorException,
  Logger,
  Req,
  Res,
} from '@nestjs/common';
import { OauthService } from './oauth.service';
import { Request, Response } from 'express';
import { Shopify, SHOPIFY } from '../shopify/shopify.module';
import { Tenant } from 'src/tenant/tenant.entity';

@Controller('')
export class OauthController {
  private readonly logger = new Logger(OauthController.name);

  constructor(
    private readonly oauthService: OauthService,
    @Inject(SHOPIFY) private readonly shopify: Shopify,
  ) { }

  // @Get('oauth')
  // @UsePipes(new ValidationPipe({ transform: true }))
  // async authorize(
  //   @Query() dto: ShopifyOauthDto,
  //   @Req() req: Request,
  //   @Res() res: Response,
  // ) {
  //   // TODO this should be a guard or something
  //   const message = url.parse(req.url).query.replace(`hmac=${dto.hmac}&`, '');
  //   const digest = crypto
  //     .createHmac('sha256', process.env.SHOPIFY_API_SECRET)
  //     .update(message)
  //     .digest('hex');
  //   if (digest !== dto.hmac) {
  //     throw new BadRequestException('Could not validate hmac');
  //   }
  //
  //   const redirectUrl = await this.oauthService.authorize(dto);
  //   return res.redirect(redirectUrl);
  // }

  /**
   * Authorize using the Shopify JS library. Will redirect back to Shopify. The actual
   * logic will happen in callbackV2
   * @param request
   * @param response
   */
  @Get('/oauth')
  async authorize(@Req() request: Request, @Res() response: Response) {
    try {
      const shop = this.shopify.utils.sanitizeShop(request.query.shop as string, true);
      this.logger.log(
        `/oauth - authorizing ${shop} with callback path ${process.env.SHOPIFY_V2_CALLBACK_PATH
        }`,
      );
      await this.shopify.auth.begin({
        shop,
        callbackPath: process.env.SHOPIFY_V2_CALLBACK_PATH,
        isOnline: false,
        rawRequest: request,
        rawResponse: response,
      });
    } catch (e) {
      this.logger.error(`Failed to authorize: ${e.message}`);
      throw new BadRequestException('authorization failed');
    }
  }

  @Get('/oauth/callback')
  async callback(@Req() request: Request, @Res() response: Response) {
    try {
      const callback = await this.shopify.auth.callback({
        rawRequest: request,
        rawResponse: response,
      });
      this.logger.log(
        `/oauth/callback - callback success for ${callback.session.shop}`,
      );

      const tenant = await this.oauthService.authorizeOrCreateTenant(
        callback.session,
      );

      return response.redirect(
        this.oauthService.adminRedirectForTenant(tenant),
      );
      // return response.redirect("https://google.com")
    } catch (e) {
      this.logger.error(
        `/oauth/callback callback failed ${e.message}${e.stack ? `${e.stack}` : ''
        }`,
      );
      throw new InternalServerErrorException('callback failed');
    }
  }
}
