import {
  Injectable,
  BadRequestException,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import axios from 'axios';
import { startCase } from 'lodash';
import { ShopifyOauthDto } from './dto/shopify-oauth.dto';
import { TenantService } from '../tenant/tenant.service';
import { ShopifyOauthAuthorizeDto } from './dto/shopify-oauth-authorize.dto';
import { Tenant } from '../tenant/tenant.entity';
import { AuthService } from '../auth/auth.service';
import { ApiSyncQueueService } from '../api-sync-queue/api-sync-queue.service';
import { CouponService } from '../coupon/coupon.service';
import { Session } from '@shopify/shopify-api';
import { EntityManager } from 'typeorm';

@Injectable()
export class OauthService {
  private readonly logger = new Logger(OauthService.name);
  constructor(
    private readonly authService: AuthService,
    private readonly tenantService: TenantService,
    private readonly apiSyncQueueService: ApiSyncQueueService,
    private readonly couponService: CouponService,
    private readonly entityManager: EntityManager,
  ) { }

  authorizeUser(dto: ShopifyOauthDto, tenant: Tenant): string {
    const nonce = tenant.id;

    // TODO: pull these from the env (this is unused anyways)
    const scopes = ['read_script_tags','write_script_tags','read_customers','write_customers','read_products', 'read_discounts', 'write_discounts', 'write_products', 'read_orders', 'write_orders','write_price_rules', 'read_price_rules'];
    const redirectUri = `${process.env.BASE_URL}/oauth`;

    const url =
      `https://${dto.shop}/admin/oauth/authorize` +
      `?client_id=${process.env.SHOPIFY_API_KEY}` +
      `&scope=${scopes.join(',')}` +
      `&redirect_uri=${redirectUri}` +
      `&state=${nonce}`;

    return url;
  }

  async generateShopifyAuthorizeUrl(dto: ShopifyOauthDto) {
    const shopSlug = dto.shop.split('.')[0];

    // Create new tenant
    const tenant = await this.tenantService.save({
      platform: 'shopify',
      externalId: dto.shop,
      name: startCase(shopSlug),
    });

    // Create default fallback coupon
    const fallbackCoupon = await this.couponService.createFallbackCoupon(
      tenant,
    );
    await this.tenantService.save({ id: tenant.id, fallbackCoupon });

    // Queue the job to sync the fallback coupon
    await this.apiSyncQueueService.addCouponJob(fallbackCoupon, tenant);

    return this.authorizeUser(dto, tenant);
  }

  getTenantAdminAccessToken(tenant: Tenant) {
    return this.authService.createToken({ tenantId: tenant.id });
  }

  adminRedirectForTenant(tenant: Tenant): string {
    const adminAccessToken = this.getTenantAdminAccessToken(tenant);
    const expiresAt = adminAccessToken.expiresIn * 1000 + Date.now();
    return `${process.env.ADMIN_URL}?access_token=${adminAccessToken.accessToken
      }&access_token_expires_at=${expiresAt}`;
  }

  async authorizeOrCreateTenant(session: Session): Promise<Tenant> {
    const tenant = await this.tenantService.findOneByExternalId(session.shop);
    // Tenant exists and is authenticated, i.e. they're set up already
    if (tenant && tenant.apiCredentials && tenant.apiCredentials.accessToken) {
      this.logger.log(
        `authorizing tenant ${tenant.externalId}, id: ${tenant.id}`,
      );
      // Tenant is set up and active. Update the apiCredentials just in case
      return await this.tenantService.save({
        id: tenant.id,
        apiCredentials: {
          ...tenant.apiCredentials,
          accessToken: session.accessToken,
        },
      });
    }
    // Tenant is not authorized, probably because they need to be set up again
    if (tenant && !tenant.apiCredentials) {
      this.logger.log(
        `re-authorizing tenant ${tenant.externalId}, id: ${tenant.id}`,
      );
      // Tenant has a previous install, set up again
      let fallbackCoupon = await this.couponService.getTenantFallbackCoupon(
        tenant,
      );
      return await this.entityManager.transaction(async em => {
        if (!fallbackCoupon) {
          fallbackCoupon = await this.couponService.createFallbackCoupon(
            tenant,
            em,
          );
          await this.apiSyncQueueService.addCouponJob(
            fallbackCoupon,
            tenant,
            em,
          );
        }

        // Queue the job to finalize tenant setup (creates webhooks)
        await this.apiSyncQueueService.addTenantJob(tenant, em);
        return await this.tenantService.save(
          {
            id: tenant.id,
            fallbackCoupon,
            apiCredentials: { accessToken: session.accessToken },
          },
          em,
        );
      });
    }
    // Tenant is signing up for the first time
    this.logger.log(`creating new tenant ${session.shop}`);
    return await this.entityManager.transaction(async em => {
      let newTenant = await this.tenantService.save(
        {
          platform: 'shopify',
          externalId: session.shop,
          name: startCase(session.shop.split('.')[0]),
          apiCredentials: { accessToken: session.accessToken },
        },
        em,
      );
      this.logger.log(
        `created tenant with id ${newTenant.id} for shop ${session.shop}`,
      );
      // Create default fallback coupon
      const newFallbackCoupon = await this.couponService.createFallbackCoupon(
        newTenant,
        em,
      );
      newTenant = await this.tenantService.save(
        {
          id: newTenant.id,
          fallbackCoupon: newFallbackCoupon,
        },
        em,
      );
      // Queue the job to sync the fallback coupon
      await this.apiSyncQueueService.addCouponJob(
        newFallbackCoupon,
        newTenant,
        em,
      );

      // Queue the job to finalize tenant setup (creates webhooks)
      await this.apiSyncQueueService.addTenantJob(newTenant, em);
      return newTenant;
    });
  }
  async authorize(dto: ShopifyOauthDto) {
    const tenant = await this.tenantService.findOneByExternalId(dto.shop);

    if (!tenant) {
      // Initial installation. Create a tenant.
      return this.generateShopifyAuthorizeUrl(dto);
    }

    if (tenant && !dto.state && !tenant.apiCredentials) {
      // Tenant uninstalled, and is trying to install the app again
      // Tenant is already created in db, but the credentials have been removed
      return this.authorizeUser(dto, tenant);
    }

    if (
      !dto.state &&
      tenant.apiCredentials &&
      tenant.apiCredentials.accessToken
    ) {
      // The tenant is already set up and the Shopify admin is just trying to get to the admin portal.
      return this.adminRedirectForTenant(tenant);
    }

    if (tenant.id !== dto.state) {
      throw new BadRequestException('Invalid tenant');
    }

    const accessToken = await this.getAccessToken(dto.code, tenant);
    tenant.apiCredentials = { accessToken };
    await this.tenantService.save(tenant);

    // Queue the job to finalize tenant setup (creates webhooks)
    await this.apiSyncQueueService.addTenantJob(tenant);

    return await this.adminRedirectForTenant(tenant);
  }

  private async getAccessToken(
    authCode: string,
    tenant: Tenant,
  ): Promise<string> {
    if (tenant.platform !== 'shopify') {
      throw new BadRequestException('Only shopify is currently supported');
    }

    const url = `https://${tenant.externalId}/admin/oauth/access_token`;
    const payload = {
      client_id: process.env.SHOPIFY_API_KEY,
      client_secret: process.env.SHOPIFY_API_SECRET,
      code: authCode,
    };

    try {
      const response = await axios.post(url, payload);
      return response.data.access_token;
    } catch (e) {
      Logger.error(e);
      throw new InternalServerErrorException(
        'Failed to receive access token from Shopify',
      );
    }
  }
}
