import { Injectable } from '@nestjs/common';
import { Optin } from './optin.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DeepPartial } from 'typeorm';

@Injectable()
export class OptinService {
  constructor(
    @InjectRepository(Optin)
    private readonly optinRepository: Repository<Optin>,
  ) {}

  async create(data: DeepPartial<Optin>): Promise<Optin> {
    return await this.optinRepository.save(data);
  }

  async findOrCreate(data: DeepPartial<Optin>): Promise<Optin> {
    const existing = await this.optinRepository.findOne({ where: { tenant: data.tenant, email: data.email } });

    if (existing) {
      return existing;
    }
    
    return await this.create(data);
  }
}
