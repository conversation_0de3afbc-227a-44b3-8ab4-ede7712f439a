import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Tenant } from '../tenant/tenant.entity';
import { Order } from '../order/order.entity';
import { Impression } from '../impression/impression.entity';

@Entity()
export class Optin {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  email: string;

  @ManyToOne(type => Tenant)
  tenant: Tenant;

  @ManyToOne(type => Impression, impression =>  impression.optins)
  impression: Impression;

  @OneToMany(type => Order, order => order.optin)
  orders: Order[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
