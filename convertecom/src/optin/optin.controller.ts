import { <PERSON>, Post, Body, BadRequestException } from '@nestjs/common';
import { CreateOptinDto } from './dto/create-optin.dto';
import { OptinService } from './optin.service';
import { TenantService } from '../tenant/tenant.service';
import { ApiSyncQueueService } from '../api-sync-queue/api-sync-queue.service';

@Controller('optin')
export class OptinController {
  constructor(
    private readonly optinService: OptinService,
    private readonly tenantService: TenantService,
    private readonly apiSyncQueueService: ApiSyncQueueService,
  ) {}

  @Post()
  async addOptin(@Body() createOptinDto: CreateOptinDto) {
    const tenant = await this.tenantService.findOneByExternalId(
      createOptinDto.storeKey,
    );
    if (!tenant) {
      throw new BadRequestException();
    }

    const optin = await this.optinService.findOrCreate({
      email: createOptinDto.email,
      tenant,
      impression: createOptinDto.impressionId
        ? { id: createOptinDto.impressionId }
        : null,
    });

    const fallbackCoupon = tenant.fallbackCoupon.code;

    // create job in queue to create customer in shopify admin
    try {
      await this.apiSyncQueueService.addCustomerJob(optin, tenant);
    } catch (err) {
      console.log(err);
    }

    delete optin.tenant;

    return {
      ...optin,
      fallbackCoupon,
    };
  }
}
