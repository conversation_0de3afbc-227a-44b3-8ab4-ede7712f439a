import { Module } from '@nestjs/common';
import { OptinController } from './optin.controller';
import { OptinService } from './optin.service';
import { ApiSyncQueueModule } from '../api-sync-queue/api-sync-queue.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Optin } from './optin.entity';
import { TenantModule } from '../tenant/tenant.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Optin]),
    TenantModule,
    ApiSyncQueueModule,
  ],
  controllers: [OptinController],
  providers: [OptinService],
})
export class OptinModule {}
