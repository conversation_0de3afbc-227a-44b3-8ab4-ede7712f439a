const HtmlWebPackPlugin = require("html-webpack-plugin");

module.exports = {
    entry: {
        'zing-bundle': __dirname + '/client/index.tsx',
        'preview-bundle': __dirname + '/client/preview.tsx',
    },
    output: {
        path: __dirname + '/dist/client',
        filename: '[name].js'
    },
    resolve: {
        extensions: ['.ts', '.tsx', '.js']
    },
    module: {
        rules: [
            {
                test: /\.(js|jsx)$/,
                exclude: /node_modules/,
                use: {
                  loader: "babel-loader"
                }
            },
            {
                test: /\.tsx?$/,
                loader: "ts-loader",
                options: {
                    configFile: 'tsconfig.react.json'
                }
            },
            {
                test: /\.html$/,
                use: [
                  {
                    loader: "html-loader"
                  }
                ]
            },
            {
                test: /\.scss$/,
                use: [
                    "style-loader",
                    "css-loader",
                    "sass-loader"
                ]
            },
            {
                test: /\.svg$/,
                use: ['@svgr/webpack'],
            }
        ]
    },
    plugins: [
        new HtmlWebPackPlugin({
            template: "./client/index.html",
            filename: "./index.html",
            excludeChunks: ['preview-bundle'],
        }),
      new HtmlWebPackPlugin({
          filename: "./preview.html",
          chunks: ['preview-bundle'],
      })
    ]
}
