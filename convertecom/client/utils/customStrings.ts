import axios from 'axios';
import get = require('lodash/get');
import { Template } from '@lib/common';

// export const customStrings = (
//   shopKey: string,
//   impressionId: string,
//   callback: (strings: CustomStringMap, active: boolean, impressionId?: string) => void,
// ) => {
//   axios
//     .get(process.env.API_URL + '/tenant/' + shopKey, {
//       headers: {
//         'Content-Type': 'application/json',
//         'Cache-Control': 'no-cache',
//       },
//       params: {
//         impressionId,
//       },
//     })
//     .then(response => response.data)
//     .then((response: StringPayload) => {
//       callback(response.template, response.active, response.impressionId);
//     });
// };

export const renderString = (strings: Template, key: string) => {
  return get(strings, [key, 'text'], '');
};

export const createStringStyle = (strings: Template, key: string) => {
  const cusString = get(strings, key, null);
  if (cusString) {
    return {
      color: cusString.color,
      backgroundColor: cusString.backgroundColor,
    };
  } else {
    return {};
  }
};

const hexToRgb = (hex: string) => {
  const defaultBackground = {
    r: 0,
    g: 0,
    b: 0,
  };
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : defaultBackground;
};

export const createBackground = (strings: Template, key: string) => {
  const cusString = get(strings, [key, 'color'], null); // default black background
  if (key === 'cartBackground') {
    if (cusString) {
      return {
        backgroundColor: cusString,
      };
    } else {
      return {
        backgroundColor: '#565656',
      };
    }
  }
  const colorRgba = get(strings, [key, 'colorRgba']);
  if (colorRgba) {
    const { r, g, b, a } = colorRgba;
    return {
      backgroundColor: `rgba(${r}, ${g}, ${b}, ${a})`,
    };
  }
  if (cusString) {
    const { r, g, b } = hexToRgb(cusString); // rrggbbaa not supported on all browsers
    return {
      backgroundColor: `rgba(${r}, ${g}, ${b}, 0.6)`,
    };
  }
  return {
    backgroundColor: `rgba(0,0,0,0.6)`,
  };
};
