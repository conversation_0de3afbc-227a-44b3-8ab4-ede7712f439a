import axios from 'axios';
import { ShopifyCartDto } from '../dto/shopify.dto';

export async function update(userId: string) {
  return axios({
    method: 'POST',
    url: '/cart/update.js',
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
    },
    data: {
      attributes: { zingUserId: userId },
    },
  });
}

export function getShopifyCart() {
  return axios
    .get<ShopifyCartDto>('/cart.js')
    .then(response => response.data);
}
