import axios from 'axios';
import { TenantConfigDto } from '../dto/tenant-config.dto';

export async function fetchTenant(externalId: string, impressionId: string) {
  const { data } = await axios
    .get<TenantConfigDto>(process.env.API_URL + '/tenant/' + externalId, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
      },
      params: {
        impressionId,
      },
    });
  return data;
}
