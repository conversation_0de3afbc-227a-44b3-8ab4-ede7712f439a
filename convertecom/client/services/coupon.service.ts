import axios from 'axios';

export async function applyDiscountCodeForShopify(code: string) {
  try {
    await axios.get(`/discount/${code}`);
    resetShopifyPaymentButtons();
  } catch (e) {
    console.error(e);
  }
}

function resetShopifyPaymentButtons() {
  try {
    resetPaymentButtons();
    resetDynamicCheckout();
  } catch (e) {
    console.error(e);
  }
}

/**
 * This is the "Buy it now" button on a product page. If a discount is applied after the page is loaded and this button is pressed, the code
 * does not transfer to the checkout page because the code is initialized when the button is set up. To reset the state, we have to clone the
 * element, delete the original, and then append the clone back in and initialize the payment buttons.
 */
function resetPaymentButtons() {
  const selector = '[data-shopify="payment-button"]';
  const elements = document.querySelectorAll<HTMLDivElement>(selector);
  if (
    elements.length === 0 ||
    typeof (window as any).Shopify.PaymentButton.init !== 'function'
  ) {
    return;
  }

  elements.forEach(el => {
    const parent = el.parentNode;
    const clone = el.cloneNode(false);
    parent.removeChild(el);
    parent.appendChild(clone);
  });

  (window as any).Shopify.PaymentButton.init();
}

/**
 * These are the specific payment provider buttons on the /cart and /checkout pages. If a discount is applied after the page is loaded and this
 * button is pressed, the code does not transfer to the payment provider. Some payment providers may not accept discount codes. This is a Shopify
 * issue and nothing we can control. The buttons are initialized using a MutationObserver so we only need to clear the container to have them
 * re-initialized.
 */
function resetDynamicCheckout() {
  const selector = '[data-shopify="dynamic-checkout-cart"]';
  const elements = document.querySelectorAll<HTMLDivElement>(selector);
  if (elements.length === 0) {
    return;
  }

  elements.forEach(el => {
    // This causes a flash of content, but it's unavoidable.
    el.innerText = '';
  });
}
