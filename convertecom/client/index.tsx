import * as React from 'react';
import * as ReactDOM from 'react-dom';
import ConvertEcomOptIn from './components/optin/ConvertEcomOptIn';
import ConvertEcomCheckout from './components/checkout/ConvertEcomCheckout';
import { OptInContextProvider } from './contexts/optin.context';
import { IntegrationsContextProvider } from './contexts/integrations.context';
import { TenantConfig, ConvertEcomContextProvider } from './contexts/convertecom.context';

export interface Subscription {
  unsubscribe(): void;
}

export interface SubscriptionCallback {
  (data: any): void;
}
export interface EventBus {
  subscribe(event: string, callback: SubscriptionCallback): Subscription;
  publish(event: string, data: any): void;
}
export function eventBus() {
  const eventSubscriptions = new Map<string, Set<symbol>>();
  const subscriptionCallbacks = new Map<symbol, (data: any) => void>();
  return {
    subscribe(event: string, callback: SubscriptionCallback) {
      const id = Symbol(event);
      if (!eventSubscriptions.has(event)) {
        eventSubscriptions.set(event, new Set());
      }
      eventSubscriptions.get(event).add(id);
      subscriptionCallbacks.set(id, callback);

      return {
        unsubscribe() {
          subscriptionCallbacks.delete(id);
          if (eventSubscriptions.has(event)) {
            eventSubscriptions.get(event).delete(id);
          }
        },
      };
    },
    publish(event: string, data: any) {
      setTimeout(() => {
        const subscribers = eventSubscriptions.get(event);
        if (subscribers) {
          subscribers.forEach(id => {
            try {
              subscriptionCallbacks.get(id)(data);
            } catch (e) {
              console.error(`Failed to send to subscription ${e}`);
            }
          });
        }
      }, 0);
    },
  };
}

const convertEcomEventBus = eventBus();

/*
  When a user changes the amount of items on the /cart page the content is reloaded
  by ajax, and we lose our instance. If it's mounted, add a handler to constantly remount it
  if necessary
 */
let interval: number;

function mountCheckout() {
  if (
    !window ||
    (window.location &&
      window.location.pathname !== '/cart' &&
      window.location.hostname !== 'localhost')
  ) {
    return;
  }
  const subscription = convertEcomEventBus.subscribe(
    'tenant/config/updated',
    (tenantConfig: TenantConfig) => {
      subscription.unsubscribe();
      if (tenantConfig.active) {
        if (tenantConfig.template.floating) {
          mountFloatingCheckout();
        } else {
          mountStaticCheckout();
        }
      }
    },
  );
}

function mountStaticCheckout() {
  const checkoutMount = document.getElementById('convertecom-reservation-container');

  if (checkoutMount && !checkoutMount.getAttribute('data-convertecom')) {
    if (typeof interval !== 'undefined') {
      window.clearInterval(interval);
    }
    checkoutMount.setAttribute('data-convertecom', 'true');
    ReactDOM.render(
      <ConvertEcomContextProvider>
        <ConvertEcomCheckout />
      </ConvertEcomContextProvider>,
      checkoutMount,
    );
    interval = window.setInterval(mountCheckout, 1000);
  }
}

function mountFloatingCheckout() {
  const checkoutMount = document.createElement('div');
  checkoutMount.id = 'convertecom-reservation-container-floating';
  document.body.append(checkoutMount);
  ReactDOM.render(
    <ConvertEcomContextProvider>
      <ConvertEcomCheckout />
    </ConvertEcomContextProvider>,
    checkoutMount,
  );
}

mountCheckout();

const convertEcomMount = document.createElement('div');
convertEcomMount.id = 'convertecom';
document.body.append(convertEcomMount);
ReactDOM.render(
  <ConvertEcomContextProvider eventBus={convertEcomEventBus}>
    <OptInContextProvider>
      <IntegrationsContextProvider>
        <ConvertEcomOptIn />
      </IntegrationsContextProvider>
    </OptInContextProvider>
  </ConvertEcomContextProvider>,
  convertEcomMount,
);
