// ConvertEcom Design System

// Colors
$primary: #4361ee; // Vibrant blue as primary color
$primary-light: #4895ef; // Lighter blue for hover states
$primary-dark: #3f37c9; // Darker blue for active states

$secondary: #f72585; // Vibrant pink as accent color
$secondary-light: #ff4d6d; // Lighter pink
$secondary-dark: #b5179e; // Darker pink

$success: #4cc9f0; // Teal for success states
$warning: #fca311; // Orange for warning states
$error: #e63946; // Red for error states

$neutral-100: #f8f9fa; // Lightest gray
$neutral-200: #e9ecef;
$neutral-300: #dee2e6;
$neutral-400: #ced4da;
$neutral-500: #adb5bd;
$neutral-600: #6c757d;
$neutral-700: #495057;
$neutral-800: #343a40;
$neutral-900: #212529; // Darkest gray

$primary-contrast: #ffffff;
$dark-contrast: #212529;

// Typography
$font-family-base: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
$font-size-base: 16px;
$font-size-sm: 14px;
$font-size-lg: 18px;
$font-size-xl: 22px;
$font-size-xxl: 28px;

$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 700;

// Spacing
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// Borders
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-pill: 9999px;

// Shadows
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
$shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 15px 25px rgba(0, 0, 0, 0.15), 0 5px 10px rgba(0, 0, 0, 0.05);

// Transitions
$transition-timer: 200ms;
$transition: all $transition-timer ease-in-out;
$transition-bounce: all 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

.convertecom-no-wrap {
  white-space: nowrap;
}

// Animation keyframes
@keyframes overlay-fade {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translate(-50%, 20px);
  }

  100% {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

// Main overlay
#convertecom-overlay {
  position: fixed;
  display: none;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(33, 37, 41, 0.85);
  backdrop-filter: blur(4px);
  opacity: 0;
  z-index: 9000;
}

// Button styles
.convertecom-btn {
  background: $primary;
  color: $primary-contrast;
  transition: $transition;
  border: none;
  font-family: $font-family-base;
  font-weight: $font-weight-medium;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
  }

  &:hover {
    background: $primary-light;
    box-shadow: $shadow-sm;
  }

  &:active::after {
    animation: ripple 1s ease-out;
  }

  &.form-btn {
    flex-basis: 30%;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }

  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }

  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

.convertecom-btn--submit {
  border-radius: $border-radius-md;
  font-size: $font-size-lg;
  text-transform: uppercase;
  font-weight: $font-weight-bold;
  padding: $spacing-sm $spacing-md;
  width: auto;
  box-shadow: $shadow-sm;
  letter-spacing: 0.5px;

  @media (max-width: 500px) {
    font-size: $font-size-base;
  }
}

.convertecom-btn--submit---fix {
  font-size: $font-size-lg;
  text-transform: uppercase;
  font-weight: $font-weight-bold;
  text-decoration: underline;
  padding: $spacing-sm;
  color: $secondary;
}

.convertecom-btn,
.convertecom-money-bag:not([disabled]) {
  cursor: pointer;
}

.convertecom-is-active {
  display: block !important;
  animation-name: overlay-fade;
  animation-duration: 0.5s;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
}

.convertecom-inner-container {
  display: none;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 90vw;
  margin: 0 auto;
  padding: $spacing-xl;
  text-align: center !important;
  transition: $transition;
  background: linear-gradient(135deg, $neutral-800, $neutral-900);
  border-radius: $border-radius-lg;
  box-shadow: $shadow-lg;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: $primary-contrast;
  font-family: $font-family-base;

  &.convertecom-is-active {
    animation: slide-up 0.5s ease forwards;
  }

  @media (min-width: 768px) {
    width: auto;
    min-width: 480px;
    max-width: 600px;
    padding: $spacing-xl $spacing-xxl;
  }
}

.convertecom-inner-container * {
  line-height: 1.4;
  font-family: $font-family-base;
}

// Typography styles
.convertecom-heading {
  font-size: $font-size-xxl;
  text-align: center;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-md;
  color: $primary-contrast;
  letter-spacing: -0.5px;
  line-height: 1.2;

  @media (min-width: 768px) {
    font-size: 32px;
  }
}

.convertecom-subheader {
  max-width: 420px;
  margin: 0 auto $spacing-lg;
}

.convertecom-subheading {
  font-size: $font-size-xl;
  color: $primary-light;
  font-weight: $font-weight-medium;
  line-height: 1.3;
  margin-bottom: $spacing-lg;
}

.convertecom-emphasize {
  text-transform: uppercase;
  text-align: center;
  font-weight: $font-weight-bold;
  letter-spacing: 0.5px;

  &.spacing {
    margin-bottom: $spacing-md;
  }
}

.convertecom-inner-container .convertecom-text-faded {
  color: $neutral-400;
  font-size: $font-size-sm;
}

.convertecom-inner-container p,
.convertecom-inner-container h2,
.convertecom-inner-container h3 {
  color: $primary-contrast;
  margin-bottom: $spacing-md;
}

.convertecom-inner-container .convertecom-subheading {
  margin-bottom: $spacing-lg;
}

.convertecom-inner-container h2 {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
}

// Form elements
.convertecom-inner-container button[type='submit'] {
  border: 0;
  border-radius: 0 $border-radius-md $border-radius-md 0;
  font-weight: $font-weight-bold;
  height: 48px;
  text-decoration: none;
  padding: 0 $spacing-md;
  white-space: nowrap;
  background: $primary;
  color: $primary-contrast;
  transition: $transition;

  &:hover {
    background: $primary-dark;
  }
}

.convertecom-inner-container a {
  text-decoration: none;
  color: $primary-light;
  transition: $transition;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 1px;
    bottom: -2px;
    left: 0;
    background-color: $primary-light;
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s;
  }

  &:hover {
    cursor: pointer;
    color: $primary-contrast;

    &::after {
      transform: scaleX(1);
      transform-origin: bottom left;
    }
  }
}

// Money bag elements
.convertecom-money-bag-container {
  display: flex;
  justify-content: space-between;
  padding: 0 $spacing-md;
  width: 100%;
  max-width: 500px;
  margin: 0 auto $spacing-xl;

  @media (min-width: 768px) {
    width: 500px;
  }
}

.convertecom-money-bag {
  flex: 1 1 auto;
  background-color: transparent;
  border: 0;
  padding: 0 $spacing-md;
  transition: $transition-bounce;

  &:hover:not([disabled]) {
    transform: translateY(-5px);
  }

  &:active:not([disabled]) {
    transform: translateY(0);
  }

  @media (min-width: 768px) {
    padding: 0 5%;
  }

  img {
    width: 100%;
    height: auto;
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.2));
  }
}

.convertecom-inner-container .convertecom-primary-color {
  color: $primary;
}

// Close button
.convertecom-close-button {
  position: absolute;
  top: -$spacing-lg;
  right: -$spacing-lg;
  border: 0;
  height: 36px;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  background-color: $neutral-700;
  color: $neutral-200;
  font-size: $font-size-lg;
  transition: $transition;
  box-shadow: $shadow-sm;

  &:hover {
    background-color: $neutral-600;
    transform: rotate(90deg);
  }

  @media (max-width: 768px) {
    top: $spacing-sm;
    right: $spacing-sm;
  }
}

.convertecom-close-button__inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  fill: $neutral-200;
}

// Form container
.convertecom-optin-form__container {
  display: flex;
  width: 100%;
  max-width: 420px;
  margin: 0 auto $spacing-lg;
  box-shadow: $shadow-md;
  border-radius: $border-radius-md;
  overflow: hidden;

  @media (min-width: 768px) {
    margin: 0 auto $spacing-xl;
  }
}

#convertecom-optin-form__email {
  background-color: $neutral-100;
  border-radius: $border-radius-md 0 0 $border-radius-md;
  border: 1px solid $neutral-300;
  border-right: none;
  color: $neutral-900;
  font-size: $font-size-base;
  font-family: $font-family-base;
  outline: 0;
  margin: 0;
  padding: 0 $spacing-md;
  text-align: left;
  height: 48px;
  vertical-align: middle;
  width: 100%;
  transition: $transition;

  &:focus {
    background-color: white;
    box-shadow: inset 0 0 0 1px $primary-light;
  }

  @media (min-width: 768px) {
    max-width: 320px;
  }
}

// Floating checkout container
#convertecom-reservation-container-floating {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 8000;

  .convertecom-checkout-notification {
    border-radius: 0;
    box-shadow: $shadow-lg;
  }

  .convertecom-checkout-notification-content {
    align-items: center;
    text-align: left;
    padding: $spacing-md;

    @media (min-width: 768px) {
      text-align: center;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: $spacing-md $spacing-xl;
    }

    button {
      margin-top: $spacing-sm;

      @media (min-width: 768px) {
        margin-top: 0;
        margin-left: $spacing-md;
      }
    }
  }

  .convertecom-checkout-notification__copy {
    text-align: center;

    @media (min-width: 768px) {
      text-align: left;
    }
  }

  .convertecom-checkout-notification__img {
    width: 60px;
    height: 60px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  }

  .convertecom-btn--submit {
    font-size: $font-size-base;
    line-height: 1;
    padding: $spacing-sm $spacing-md;
  }
}

// Confirmation styles
.convertecom-exciting {
  color: $primary-contrast;
  font-size: 36px;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-sm;
  line-height: 1.2;

  @media (min-width: 768px) {
    font-size: 42px;
  }
}

.convertecom-final-money-bag {
  margin-bottom: $spacing-xl;
  max-height: 200px;
  animation: pulse 2s infinite;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));

  @media (min-width: 768px) {
    width: 180px;
  }
}

.convertecom-cta {
  font-size: $font-size-lg;
  color: $primary;
  margin-bottom: $spacing-md;

  @media (min-width: 768px) {
    font-size: $font-size-xl;
    white-space: nowrap;
  }
}

.convertecom-cta-link {
  font-size: $font-size-xl;
  color: $secondary;
  font-weight: $font-weight-bold;
  transition: $transition;

  &:hover {
    color: $secondary-light;
    text-decoration: underline;
  }

  @media (min-width: 768px) {
    font-size: 28px;
  }
}

// Checkout notification styles
.convertecom-checkout-notification {
  display: flex;
  border-radius: $border-radius-lg;
  vertical-align: top;
  padding: $spacing-md;
  text-align: center;
  user-select: none;
  background: linear-gradient(135deg, $neutral-800, $neutral-900);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.convertecom-checkout-notification-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  text-align: left;
  width: 100%;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
  }
}

.convertecom-checkout-notification__copy {
  text-transform: uppercase;
  font-weight: $font-weight-bold;
  color: $primary-contrast;
  letter-spacing: 0.5px;
}

.convertecom-checkout-notification-content p {
  color: $neutral-300;
  margin-bottom: $spacing-xs;
}

.convertecom-checkout-notification-content button {
  margin-top: $spacing-sm;
  align-self: center;

  @media (min-width: 768px) {
    margin-top: 0;
    margin-left: auto;
  }
}

.convertecom-checkout-notification__img {
  flex-shrink: 0;
  margin-right: $spacing-md;
  width: 70px;
  height: 70px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

#convertecom-reservation-container {
  width: 100%;
  display: inline-block;
  margin-bottom: $spacing-sm;
  margin-top: $spacing-md;
  border-radius: $border-radius-lg;
  overflow: hidden;
}

.convertecom-time-remaining {
  font-family: $font-family-base;
  font-weight: $font-weight-bold;
  color: $warning;
  background: rgba(252, 163, 17, 0.1);
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  display: inline-block;
}

@media screen and (min-width: 499px) {
  .convertecom-checkout-notification-content {
    padding-right: 85px;
    align-items: center;
    text-align: center;
  }
}

#convertecom {
  height: 0;

  p {
    color: white;
  }
}

.recart-container--notify {
  animation: shake 0.9s cubic-bezier(0.24, 0.01, 0.17, 0.85) both;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
}

@keyframes shake {

  10%,
  90% {
    transform: translate3d(-4px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(8px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-2px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(6px, 0, 0);
  }
}