import * as ReactDOM from 'react-dom';
import * as React from 'react';
import { Preview } from './components/preview/Preview';
import { IntegrationsContext } from './contexts/integrations.context';
import { RecartContext, RecartState } from './contexts/recart.context';

const zingMount = document.createElement('div');
zingMount.id = 'convertecom-preview';
document.body.append(zingMount);

ReactDOM.render(
  <IntegrationsContext.Provider value={{ isLoaded: true, recart: undefined }}>
    <RecartContext.Provider
      value={{
        state: RecartState.ERROR,
        checkboxChecked: false,
        unrecoverableError: true,
      }}
    >
      <Preview />
    </RecartContext.Provider>
  </IntegrationsContext.Provider>,
  zingMount,
);
