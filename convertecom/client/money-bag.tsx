import * as React from 'react';

export const moneyBag = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    version="1.0"
    viewBox="0 0 228.643 282.007"
  >
    <switch><g><linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="114.321" y1="9.077" x2="114.321" y2="252.93"><stop offset="0" stop-color="#f1f1f1"/><stop offset="1" stop-color="#ff27b6"/></linearGradient><path d="M104.42 96.68c-3.66 6.65-6.61 12.02-9.55 17.39.2.13.39.27.59.4 3.71-4.98 7.42-9.97 11.13-14.95 3.96-5.33 10.99-4.79 13.79 1.05 2.67 5.56 5.37 11.11 8.67 16.46-1.35-6.41-2.71-12.83-4.06-19.24.33-.35.66-.7.99-1.06 3.99 2.11 8.26 3.83 11.93 6.4 23.43 16.4 41.01 38.11 55.6 62.39 8.93 14.86 15.77 30.62 18.02 47.98 1.93 14.85-.73 28.59-10.16 40.82-6.5 8.42-15.39 11.69-25.22 13.32-31.24 5.17-62.67 5.96-94.17 3.28-11.55-.98-23.07-2.75-34.5-4.72-14.46-2.49-22.23-12.83-27.23-25.43-5.77-14.56-4.34-29.49.77-43.75C35.45 156.8 61 124.97 95.9 100.55c2.18-1.54 4.89-2.26 8.52-3.87zm14.38 137.63c1.94-.34 3.6-.62 5.25-.92 11.62-2.11 20.47-10.97 22.18-22.21 1.79-11.71-3.01-21.61-13.35-26.68-3.59-1.76-7.6-2.63-11.37-4.06-1-.38-2.52-1.31-2.53-2.02-.18-7.81-.11-15.62-.11-23.83 5.39 1.82 8.03 5.05 9.49 9.65 1.73 5.5 5.59 7.76 10.09 6.32 4.66-1.49 6.65-6.04 4.48-11.48-3.02-7.58-8.23-13.07-16.37-15.27-2.5-.67-5.03-1.22-7.8-1.89 0-2.58.48-5.03-.16-7.16-.46-1.54-2.39-2.65-3.67-3.95-1.22 1.31-3.06 2.42-3.52 3.96-.68 2.25-.37 4.8-.48 7.25-2.96.66-5.55 1.14-8.09 1.82-9.94 2.7-16.56 10.34-17.46 20.06-1.13 12.24 3.13 20.27 13.47 25.03 3.09 1.42 6.41 2.33 9.52 3.71 1.08.48 2.61 1.68 2.63 2.59.2 8.61.12 17.22.12 25.99-1.08-.12-1.54-.1-1.95-.23-6.57-2.21-8.8-7.76-10.69-13.67-1.66-5.17-5.21-7.22-9.86-5.99-4.33 1.15-6.34 4.64-5.61 9.83 1.04 7.39 4.89 13.26 11.1 16.97 5.08 3.04 10.91 4.85 17.08 7.49 0 2.22-.08 5.3.02 8.38.09 2.53.21 5.28 3.78 5.24 3.56-.04 3.61-2.92 3.76-5.35.21-3.08.05-6.18.05-9.58z" fill="url(#SVGID_1_)"/><linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="113.971" y1="9.077" x2="113.971" y2="252.93"><stop offset="0" stop-color="#f1f1f1"/><stop offset="1" stop-color="#ff27b6"/></linearGradient><path d="M124.66 89.68c1.91-4.75 3.74-9.3 5.57-13.84-.27-.16-.55-.31-.82-.47-2.32 3.31-4.89 6.49-6.89 9.98-1.82 3.18-4.09 4.07-7.62 3.6-7.03-.94-13.95-.46-20.72 3.35-4.85-5.72-9.65-11.37-14.44-17.02-3.91-4.61-7.74-9.3-11.76-13.83-3.3-3.72-3.8-8-.6-11.34 3.98-4.15 8.89-7.44 13.65-10.75.58-.4 3.27.99 3.93 2.15 2.88 5.08 5.37 10.38 8.01 15.6.36-.14.72-.28 1.08-.41-2.13-7.19-4.2-14.4-6.41-21.56-2.39-7.75-2.2-9.19 5.47-11.67 18.15-5.86 36.32-5.37 54.23 1.45 3.57 1.36 4.46 3.21 2.55 7.05-3.16 6.35-5.54 13.1-7.66 20.14 3.19-4.13 6.38-8.25 9.53-12.33 11.94 5.34 14.33 15.49 6.09 24.97-6.31 7.26-12.61 14.53-18.92 21.79-5.52 6.36-5.52 6.36-14.27 3.14z" fill="url(#SVGID_2_)"/><linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="125.334" y1="9.077" x2="125.334" y2="252.93"><stop offset="0" stop-color="#f1f1f1"/><stop offset="1" stop-color="#ff27b6"/></linearGradient><path d="M119.03 222.12v-25.7c7.22.77 12.06 5.23 12.56 11.43.55 6.76-3.82 11.9-12.56 14.27z" fill="url(#SVGID_3_)"/><linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="105.463" y1="9.077" x2="105.463" y2="252.93"><stop offset="0" stop-color="#f1f1f1"/><stop offset="1" stop-color="#ff27b6"/></linearGradient><path d="M110.7 176.91c-7.32-2.04-10.33-5.2-10.47-10.81-.15-6.06 2.59-9.2 10.47-11.59v22.4z" fill="url(#SVGID_4_)"/></g></switch>
  </svg>
);
