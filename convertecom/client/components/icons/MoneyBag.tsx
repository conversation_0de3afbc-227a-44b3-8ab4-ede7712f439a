import * as React from 'react';

interface Props {
  className?: string;
};

export const MoneyBag = ({ className }: Props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    version="1.0"
    viewBox="0 0 228.643 282.007"
    className={className}
  >
    <use xlinkHref="#money-bag-path" fill="url(#money-bag-gradient)" />
    <use xlinkHref="#money-bag-top-path" fill="url(#money-bag-gradient)" />
    <use xlinkHref="#money-bag-dollar-fill-1-path" fill="url(#money-bag-gradient)" />
    <use xlinkHref="#money-bag-dollar-fill-2-path" fill="url(#money-bag-gradient)" />
  </svg>
);

export default MoneyBag;
