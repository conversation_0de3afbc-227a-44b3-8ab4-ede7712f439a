import * as React from 'react';
import { useEffect, useState } from 'react';
import { OptInForm } from '../optin/OptInForm';
import { renderString } from '../../utils/customStrings';
import '../../scss/convertecom-styles.scss';
import '../../scss/convertecom-preview.scss';
import { OptInConfirmation } from '../optin/OptInConfirmation';
import SvgDefs from '../SvgDefs';
import { OptInContainer } from '../optin/OptInContainer';
import { CheckoutContainer } from '../checkout/CheckoutContainer';
import { CheckoutBannerClaim } from '../checkout/CheckoutBannerClaim';
import { CheckoutSelectCoupon } from '../checkout/CheckoutSelectCoupon';
import { CheckoutBannerActive } from '../checkout/CheckoutBannerActive';
import { CheckoutBannerExpired } from '../checkout/CheckoutBannerExpired';
import { CheckoutBannerInvalid } from '../checkout/CheckoutBannerInvalid';
import CustomIcon from '../icons/CustomIcon';
import { Template } from 'libs/common';
// tslint:disable-next-line:no-var-requires

export type SettingsPreviewComponentType =
  | 'mainPopUp'
  | 'optInSuccess'
  | 'cartSeeWhatYouGot'
  | 'cartGiftWaiting'
  | 'cartExpired'
  | 'couponInvalid'
  | 'couponPicker'
  | 'icon';

interface SetTemplateEvent extends MessageEvent {
  data: {
    type: 'updatePreview';
    component: SettingsPreviewComponentType;
    payload: Template;
  };
}

export const Preview = () => {
  const [strings, setStrings] = useState<Template>(undefined);
  const [component, setComponent] = useState<SettingsPreviewComponentType>(
    'mainPopUp',
  );
  const [startTime, setStartTime] = useState(Date.now());
  const [timerRunning, setTimerRunning] = useState(false);
  const [expires, setExpires] = useState('10:00');
  const icon = renderString(strings, 'cartPopupCustomImage');
  const expiresMs = 10 * 60 * 1000;

  function receiveTemplate(event: SetTemplateEvent) {
    if (event.data && event.data.type === 'updatePreview') {
      const allowedOrigins: string[] = (
        process.env.POST_MESSAGE_ALLOWED_ORIGINS || ''
      ).split(',');
      const isAllowedOrigin = allowedOrigins.some(
        origin => event.origin === origin,
      );
      if (!isAllowedOrigin) {
        return;
      }
      setStrings(event.data.payload);
      setComponent(event.data.component);

      if (event.data.component === 'cartGiftWaiting') {
        setStartTime(Date.now());
        setTimerRunning(true);
      }
    }
  }

  useEffect(() => {
    window.addEventListener('message', receiveTemplate);
    return () => {
      window.removeEventListener('message', receiveTemplate);
    };
  }, []);

  useEffect(() => {
    let timer: number;
    if (timerRunning) {
      timer = window.setInterval(() => {
        // Get total seconds
        const remaining = (startTime + expiresMs - Date.now()) / 1000;

        const minutes = Math.floor(remaining / 60);
        const seconds = Math.round(remaining % 60);
        setExpires(`${minutes}:${seconds}`);
      }, 1000);
    } else if (!timerRunning && typeof timer !== 'undefined') {
      clearInterval(timer);
    }

    return () => {
      if (typeof timer !== 'undefined') {
        clearInterval(timer);
      }
    };
  }, [timerRunning]);

  // tslint:disable-next-line:no-empty
  function noop() { }

  function renderComponent() {
    switch (component) {
      case 'mainPopUp':
      case 'optInSuccess': {
        return (
          <OptInContainer
            optInForm={
              <OptInForm
                strings={strings}
                optinEmail={''}
                icon={icon}
                handleEmailChange={noop}
                handleOptIn={noop}
                handleOptOut={noop}
              />
            }
            optInConfirmation={
              <OptInConfirmation
                strings={strings}
                closeOverlay={noop}
                icon={icon}
              />
            }
            strings={strings}
            step={component === 'mainPopUp' ? 1 : 2}
          />
        );
      }
      case 'cartSeeWhatYouGot':
        return (
          <CheckoutContainer
            strings={strings}
            showOverlay={false}
            icon={icon}
            banner={<CheckoutBannerClaim strings={strings} onClaim={noop} />}
          />
        );
      case 'cartGiftWaiting':
        return (
          <CheckoutContainer
            strings={strings}
            showOverlay={false}
            icon={icon}
            banner={
              <CheckoutBannerActive
                strings={strings}
                couponAmount={8.39}
                expires={expires}
              />
            }
          />
        );
      case 'cartExpired':
        return (
          <CheckoutContainer
            strings={strings}
            showOverlay={false}
            icon={icon}
            banner={<CheckoutBannerExpired strings={strings} onRetry={noop} />}
          />
        );
      case 'couponInvalid':
        return (
          <CheckoutContainer
            strings={strings}
            showOverlay={false}
            icon={icon}
            banner={<CheckoutBannerInvalid strings={strings} onRetry={noop} />}
          />
        );
      case 'couponPicker':
        return (
          <CheckoutContainer
            strings={strings}
            showOverlay={true}
            icon={icon}
            banner={null}
          >
            <CheckoutSelectCoupon
              active={true}
              strings={strings}
              onGrabReward={noop}
              icon={icon}
              enabledMoneyBags={true}
            />
          </CheckoutContainer>
        );
      case 'icon':
        return (
          <div className="convertecom-money-bag-container">
            <div className="convertecom-money-bag convertecom-money-bag-preview">
              <CustomIcon
                iconURL={icon}
                className="convertecom-money-bag-preview-image"
              />
            </div>
          </div>
        );
      default: {
        return <div style={{ color: '#fff' }}>No Preview Available</div>;
      }
    }
  }

  return (
    <main
      style={{
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {strings ? (
        <SvgDefs moneyBagGradient={strings.cartPopupIconGradient} />
      ) : null}
      <section style={{ display: 'flex', flexDirection: 'column', flex: '1', alignItems: 'center' }}>
        <h1 style={{ textAlign: 'center' }}>Sample Website Header Here</h1>
        <p style={{ maxWidth: '400px' }}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
          eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad
          minim veniam, quis nostrud exercitation ullamco laboris nisi ut
          aliquip ex ea commodo consequat. Duis aute irure dolor in
          reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
          pariatur. Excepteur sint occaecat cupidatat non proident, sunt in
          culpa qui officia deserunt mollit anim id est laborum.
        </p>
        {strings ? renderComponent() : 'Loading'}
      </section>
      <p className="convertecom-preview-disclaimer">
        <small>
          This is a preview only.
          <br />
          Buttons and links do not work.
          <br />
          Fonts may be different.
        </small>
      </p>
    </main>
  );
};
