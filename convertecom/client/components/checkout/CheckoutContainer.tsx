import * as React from 'react';
import { createBackground } from '../../utils/customStrings';
import { FunctionComponent } from 'react';
import CustomIcon from '../icons/CustomIcon';
import { Template } from '@lib/common';

export interface CheckoutContainerProps {
  strings: Template;
  showOverlay: boolean;
  icon: string;
  banner: React.ReactNode;
  floating?: boolean;
}

export const CheckoutContainer: FunctionComponent<
  CheckoutContainerProps
> = props => (
  <div id="convertEcomCheckoutNotification">
    {props.banner && (
      <div
        className={`convertecom-checkout-notification ${props.floating ? 'convertecom-checkout-notification--floating' : ''
          }`}
        style={createBackground(props.strings, 'cartBackground')}
      >
        <CustomIcon
          iconURL={props.icon}
          className="convertecom-checkout-notification__img"
        />
        {props.banner}
      </div>
    )}
    <div
      id="convertecom-overlay"
      className={props.showOverlay ? 'convertecom-is-active' : ''}
    >
      {props.children}
    </div>
  </div>
);
