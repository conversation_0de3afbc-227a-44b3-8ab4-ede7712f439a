import * as React from 'react';
import {
  createStringStyle,
  renderString,
} from '../../utils/customStrings';
// @ts-ignore
import BalanceText from 'react-balance-text';
import { Template } from '@lib/common';

export interface CheckoutBannerActiveProps {
  strings: Template;
  couponAmount: number;
  expires?: string;
}

export const CheckoutBannerActive = (props: CheckoutBannerActiveProps) => (
  <div className="convertecom-checkout-notification-content">
    <div
      className="convertecom-checkout-notification__copy"
      style={createStringStyle(props.strings, 'cartAmount')}
    >
      <BalanceText>
        {renderString(props.strings, 'cartAmount').replace(
          '%AMOUNT%',
          props.couponAmount.toFixed(2),
        )}
      </BalanceText>
    </div>
    {props.expires ? (
      <div
        className="convertecom-time-remaining"
        style={createStringStyle(props.strings, 'cartTime')}
      >
        <BalanceText>
          {renderString(props.strings, 'cartTime').replace(
            '%TIME%',
            props.expires,
          )}
        </BalanceText>
      </div>
    ) : null}
  </div>
);
