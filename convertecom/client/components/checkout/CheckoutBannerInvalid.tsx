import * as React from 'react';
import { createStringStyle, renderString } from '../../utils/customStrings';
// @ts-ignore
import BalanceText from 'react-balance-text';
import { Template } from '@lib/common';

export interface CheckoutBannerInvalidProps {
  strings: Template;
  onRetry: () => void;
}

export const CheckoutBannerInvalid = (props: CheckoutBannerInvalidProps) => (
  <div className="convertecom-checkout-notification-content">
    <div
      className="convertecom-checkout-notification__copy"
      style={createStringStyle(props.strings, 'cartInvalid')}
    >
      <BalanceText>
        {renderString(props.strings, 'cartInvalid')}
      </BalanceText>
    </div>
    <button
      type="button"
      className="convertecom-btn convertecom-btn--submit"
      onClick={props.onRetry}
      style={createStringStyle(props.strings, 'cartInvalidButton')}
    >
      {renderString(props.strings, 'cartInvalidButton')}
    </button>
  </div>
);
