import * as React from 'react';
// tslint:disable-next-line:no-var-requires
const classnames = require('classnames');
import {
  createBackground,
  createStringStyle,
  renderString,
} from '../../utils/customStrings';
import CustomIcon from '../icons/CustomIcon';
import { Template } from '@lib/common';

export interface CheckoutSelectCouponProps {
  active: boolean;
  strings: Template;
  onGrabReward: () => void;
  icon: string;
  enabledMoneyBags: boolean;
}

export const CheckoutSelectCoupon = (props: CheckoutSelectCouponProps) => (
  <div
    className={classnames(
      'convertecom-inner-container',
      'convertecom-inner-container--step-3',
      props.active ? 'convertecom-is-active' : '',
    )}
    id="convertecom-optin-step3"
    style={createBackground(props.strings, 'optInBackground')}
  >
    <div className="convertecom-money-bag-container">
      <button
        type="button"
        className="convertecom-money-bag"
        onClick={props.onGrabReward}
        disabled={!props.enabledMoneyBags}
      >
        <CustomIcon iconURL={props.icon} />
      </button>
      <button
        type="button"
        className="convertecom-money-bag"
        onClick={props.onGrabReward}
        disabled={!props.enabledMoneyBags}
      >
        <CustomIcon iconURL={props.icon} />
      </button>
      <button
        type="button"
        className="convertecom-money-bag"
        onClick={props.onGrabReward}
        disabled={!props.enabledMoneyBags}
      >
        <CustomIcon iconURL={props.icon} />
      </button>
    </div>
    <h3
      className="convertecom-emphasize"
      style={createStringStyle(props.strings, 'cartPopupPickOne')}
    >
      {renderString(props.strings, 'cartPopupPickOne')}
    </h3>
  </div>
);
