import * as React from 'react';
// @ts-ignore
import BalanceText from 'react-balance-text';
import {
  createStringStyle,
  renderString,
} from '../../utils/customStrings';
import { Template } from '@lib/common';

export interface CheckoutBannerClaimProps {
  strings: Template;
  onClaim: () => void;
}

export const CheckoutBannerClaim = ({
  strings,
  onClaim,
}: CheckoutBannerClaimProps) => (
  <div className="convertecom-checkout-notification-content">
    <div
      className="convertecom-checkout-notification__copy"
      style={createStringStyle(strings, 'cart')}
    >
      <BalanceText>{renderString(strings, 'cart')}</BalanceText>
    </div>
    <button
      type="button"
      className="convertecom-btn convertecom-btn--submit"
      onClick={onClaim}
      style={createStringStyle(strings, 'cartButton')}
      aria-label="Claim discount"
    >
      {renderString(strings, 'cartButton')}
    </button>
  </div>
);
