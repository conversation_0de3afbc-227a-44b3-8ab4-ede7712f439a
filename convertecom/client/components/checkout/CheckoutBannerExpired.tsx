import * as React from 'react';
import { createStringStyle, renderString } from '../../utils/customStrings';
// @ts-ignore
import BalanceText from 'react-balance-text';
import { Template } from '@lib/common';

export interface CheckoutBannerExpiredProps {
  strings: Template;
  onRetry: () => void;
}

export const CheckoutBannerExpired = (props: CheckoutBannerExpiredProps) => (
  <div className="convertecom-checkout-notification-content">
    <div
      className="convertecom-checkout-notification__copy"
      style={createStringStyle(props.strings, 'cartExpired')}
    >
      <BalanceText>
        {renderString(props.strings, 'cartExpired')}
      </BalanceText>
    </div>
    <button
      type="button"
      className="convertecom-btn convertecom-btn--submit"
      onClick={props.onRetry}
      style={createStringStyle(props.strings, 'cartExpiredButton')}
    >
      {renderString(props.strings, 'cartExpiredButton')}
    </button>
  </div>
);
