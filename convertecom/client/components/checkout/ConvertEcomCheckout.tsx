import * as React from 'react';
import { renderString } from '../../utils/customStrings';
import axios from 'axios';
import '../../scss/convertecom-styles.scss';
import { CheckoutBannerClaim } from './CheckoutBannerClaim';
import { CheckoutContainer } from './CheckoutContainer';
import { CheckoutBannerExpired } from './CheckoutBannerExpired';
import { CheckoutBannerActive } from './CheckoutBannerActive';
import { CheckoutBannerInvalid } from './CheckoutBannerInvalid';
import { CheckoutSelectCoupon } from './CheckoutSelectCoupon';
import { CheckoutCouponResult } from './CheckoutCouponResult';
import * as Cart from '../../services/cart';
import { applyDiscountCodeForShopify } from '../../services/coupon.service';
import { TenantConfig, ConvertEcomContext } from '../../contexts/convertecom.context';
import SvgDefs from '../SvgDefs';

enum CouponState {
  UNCLAIMED,
  ACTIVE,
  EXPIRED,
  CHANGED,
}

interface ConvertEcomCheckoutProps {
  userId: string;
  convertEcomDiscountCode: string;
  impressionId: string;
  clearCode: () => void;
  saveCode: (discountCode: string, reservedUntil: string) => void;
  tenantConfig: TenantConfig;
}

interface ConvertEcomCheckoutState {
  cart: number;
  showOverlay: boolean;
  step: number;
  enabledMoneyBags: boolean;
  coupon: CouponState;
  optedIn: boolean;
  icon: string;
  expires?: string;
  couponAmount?: number;
}

class ConvertEcomCheckout extends React.Component<
  ConvertEcomCheckoutProps,
  ConvertEcomCheckoutState
> {
  constructor(props: ConvertEcomCheckoutProps) {
    super(props);
    this.state = {
      cart: 0,
      showOverlay: false,
      enabledMoneyBags: true,
      step: 3,
      coupon: CouponState.UNCLAIMED,
      optedIn: false,
      icon: '',
      couponAmount: 0,
    };
    this.openModal = this.openModal.bind(this);
    this.grabReward = this.grabReward.bind(this);
    this.couponRetrieved = this.couponRetrieved.bind(this);
    this.appendDiscountToUrls = this.appendDiscountToUrls.bind(this);
    this.replaceDiscountUrlParam = this.replaceDiscountUrlParam.bind(this);
    this.processReward = this.processReward.bind(this);
    this.closeModal = this.closeModal.bind(this);
    this.showCartStatus = this.showCartStatus.bind(this);
    this.updateTimer = this.updateTimer.bind(this);
  }

  componentDidMount() {
    // @ts-ignore
    const { userId, convertEcomDiscountCode, impressionId, tenantConfig } = this.props;

    if (userId) {
      if (impressionId) {
        this.setState(s => ({
          icon: renderString(tenantConfig.template, 'cartPopupCustomImage'),
        }));
      }

      Cart.getShopifyCart()
        .then(data => {
          // subtotal should be a number but casting to string to maintain current logic
          const cart: number =
            parseFloat((data.items_subtotal_price as unknown) as string) /
            100 || 0;
          if (convertEcomDiscountCode) {
            fetch(process.env.API_URL + '/coupon/check', {
              method: 'POST',
              cache: 'no-cache',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                storeKey: tenantConfig.shop,
                userId,
                code: convertEcomDiscountCode,
              }),
            })
              .then(response => {
                return response.json();
              })
              .then(response => {
                if (response.statusCode && response.statusCode === 404) {
                  this.props.clearCode();
                } else if (response.couponAmount) {
                  this.showCartStatus(
                    response.reservedUntil,
                    response.minimumCartSubtotal,
                    response.maximumCartSubtotal,
                  );
                  this.setState({
                    couponAmount: parseFloat(response.couponAmount),
                  });
                } else if (response.percentage) {
                  const couponAmount = parseFloat(response.percentage) * cart;
                  this.setState({ couponAmount });
                  this.showCartStatus(
                    response.reservedUntil,
                    response.minimumCartSubtotal,
                    response.maximumCartSubtotal,
                  );
                }
              });
          }
          this.setState({ cart });
        })
        .catch(() => {
          this.setState({ cart: -1 });
        });

      this.setState({ optedIn: true });
    }
  }

  appendDiscountToUrls(discount: string) {
    if (discount) {
      const forms = Array.prototype.slice.call(
        document.querySelectorAll(
          "form[action*='/cart'], form[action*='/checkout']",
        ),
        0,
      );
      const links = Array.prototype.slice.call(
        document.querySelectorAll("a[href*='/checkout']"),
        0,
      );
      forms.forEach((el: any) => {
        el.action = this.replaceDiscountUrlParam(el.action, discount);
      });
      links.forEach((el: any) => {
        el.href = this.replaceDiscountUrlParam(el.href, discount);
      });
    }
  }

  replaceDiscountUrlParam(url: string, discount: string) {
    const pattern = new RegExp('\\b(discount=).*?(&|#|$)');
    if (url.search(pattern) >= 0) {
      return url.replace(pattern, '$1' + discount + '$2');
    }
    url = url.replace(/[?#]$/, '');
    return url + (url.indexOf('?') > 0 ? '&' : '?') + 'discount=' + discount;
  }

  async grabReward() {
    const storeKey = this.props.tenantConfig.shop;
    const userId = this.props.userId;

    // use moneyBags to ensure a user can't click multiple times and cause a race condition
    if (userId && this.state.enabledMoneyBags) {
      this.setState({ enabledMoneyBags: false });
      try {
        const res = await Cart.getShopifyCart();
        const cart: number =
          parseFloat((res.items_subtotal_price as unknown) as string) / 100 ||
          0;
        this.setState({ cart });
        await this.processReward(storeKey, userId, cart);
      } catch (e) {
        console.error(e);
        alert('Something went wrong, please refresh the page and try again!');
        this.setState({ enabledMoneyBags: true });
      }
    }
  }

  async processReward(storeKey: string, userId: string, cartAmount: number) {
    const rawResponse = await axios({
      method: 'POST',
      url: process.env.API_URL + '/coupon/reserve',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
      },
      data: {
        storeKey,
        userId,
        cartAmount,
      },
    });

    if (rawResponse) {
      const response = rawResponse.data;
      this.props.saveCode(response.code, response.reservedUntil);

      await applyDiscountCodeForShopify(response.code);
      await Cart.update(userId);
      this.appendDiscountToUrls(response.code);

      // Check for 0 or 0.00 being returned
      const couponAmount =
        response.couponAmount && parseFloat(response.couponAmount) > 0.01
          ? parseFloat(response.couponAmount)
          : parseFloat(response.percentage) * cartAmount;

      this.showCartStatus(
        response.reservedUntil,
        response.minimumCartSubtotal,
        response.maximumCartSubtotal,
      );
      this.setState({ step: 4, enabledMoneyBags: true, couponAmount });
    }
    this.setState({ enabledMoneyBags: true });
  }

  openModal() {
    this.setState({ showOverlay: true, enabledMoneyBags: true });
  }

  closeModal() {
    this.setState({ showOverlay: false, enabledMoneyBags: true });
  }

  couponRetrieved() {
    this.closeModal();
    this.setState({ step: 3 });
  }

  showCartStatus(
    reservedUntil: number,
    minimumCartSubtotal: number,
    maximumCartSubtotal?: number,
  ) {
    if (!reservedUntil) {
      this.setState({ coupon: CouponState.ACTIVE, enabledMoneyBags: true });
      return;
    }

    const now = new Date();
    const expireDate = new Date(reservedUntil);

    if (now >= expireDate) {
      this.setState({ coupon: CouponState.EXPIRED, enabledMoneyBags: true });
    } else if (minimumCartSubtotal > this.state.cart) {
      this.setState({ coupon: CouponState.CHANGED, enabledMoneyBags: true });
    } else if (maximumCartSubtotal && this.state.cart > maximumCartSubtotal) {
      this.setState({ coupon: CouponState.CHANGED, enabledMoneyBags: true });
    } else {
      this.setState({ coupon: CouponState.ACTIVE, enabledMoneyBags: true });
      this.updateTimer(expireDate.getTime() - now.getTime());
    }
  }

  updateTimer(reservedUntil: number) {
    if (reservedUntil < 1000) {
      this.setState({ coupon: CouponState.EXPIRED, expires: '00:00' });
    } else {
      let diffMs = reservedUntil;
      const ms = diffMs % 1000;
      diffMs = (diffMs - ms) / 1000;
      const secs = diffMs % 60;
      diffMs = (diffMs - secs) / 60;
      const mins = diffMs % 60;
      const hrs = (diffMs - mins) / 60;

      this.setState({
        expires:
          (hrs > 0 ? hrs + ':' : '') +
          (mins < 10 ? '0' + mins : mins) +
          ':' +
          (secs < 10 ? '0' + secs : secs),
      });
      setTimeout(() => this.updateTimer(reservedUntil - 1000), 1000);
    }
  }

  getCheckoutBanner() {
    const { template: strings } = this.props.tenantConfig;
    if (this.state.coupon === CouponState.UNCLAIMED) {
      return <CheckoutBannerClaim strings={strings} onClaim={this.openModal} />;
    } else if (this.state.coupon === CouponState.EXPIRED) {
      return (
        <CheckoutBannerExpired strings={strings} onRetry={this.openModal} />
      );
    } else if (this.state.coupon === CouponState.CHANGED) {
      return (
        <CheckoutBannerInvalid strings={strings} onRetry={this.openModal} />
      );
    } else {
      return (
        <CheckoutBannerActive
          strings={strings}
          couponAmount={this.state.couponAmount}
          expires={this.state.expires}
        />
      );
    }
  }

  render() {
    const {
      showOverlay,
      icon,
      step,
      optedIn,
      expires,
      couponAmount,
    } = this.state;
    const { template: strings, active } = this.props.tenantConfig;
    if (optedIn && active) {
      return (
        <CheckoutContainer
          strings={strings}
          showOverlay={showOverlay}
          icon={icon}
          banner={this.getCheckoutBanner()}
          floating={strings.floating}
        >
          <CheckoutSelectCoupon
            active={step === 3}
            strings={strings}
            onGrabReward={this.grabReward}
            icon={icon}
            enabledMoneyBags={this.state.enabledMoneyBags}
          />
          <CheckoutCouponResult
            active={step === 4}
            strings={strings}
            icon={icon}
            couponAmount={couponAmount}
            onCouponRetrieved={this.couponRetrieved}
            expires={expires}
          />
        </CheckoutContainer>
      );
    } else {
      return '';
    }
  }
}

export default () => (
  <ConvertEcomContext.Consumer>
    {convertEcom =>
      convertEcom.tenantConfig && convertEcom.tenantConfig.active ? (
        <>
          <div
            aria-hidden={true}
            style={{ width: 0, height: 0, overflow: 'hidden' }}
          >
            <SvgDefs
              moneyBagGradient={
                convertEcom.tenantConfig.template.cartPopupIconGradient
              }
            />
          </div>
          <ConvertEcomCheckout
            impressionId={convertEcom.state.impressionId}
            userId={convertEcom.state.userId}
            convertEcomDiscountCode={convertEcom.state.convertEcomDiscountCode}
            clearCode={convertEcom.clearCode}
            saveCode={convertEcom.saveCode}
            tenantConfig={convertEcom.tenantConfig}
          />
        </>
      ) : null
    }
  </ConvertEcomContext.Consumer>
);
