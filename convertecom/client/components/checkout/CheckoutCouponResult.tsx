import * as React from 'react';

// tslint:disable-next-line:no-var-requires
const classnames = require('classnames');
// @ts-ignore
import BalanceText from 'react-balance-text';
import {
  createBackground,
  createStringStyle,
  renderString,
} from '../../utils/customStrings';
import CustomIcon from '../icons/CustomIcon';
import CloseX from '../icons/CloseX';
import { Template } from '@lib/common';

export interface CheckoutCouponResultProps {
  active: boolean;
  strings: Template;
  icon: string;
  couponAmount: number;
  expires?: string;
  onCouponRetrieved: () => void;
}

export const CheckoutCouponResult = (props: CheckoutCouponResultProps) => (
  <div
    className={classnames(
      'convertecom-inner-container',
      'convertecom-inner-container--step-4',
      props.active ? 'convertecom-is-active' : '',
    )}
    id="convertecom-optin-step4"
    style={createBackground(props.strings, 'optInBackground')}
  >
    <button
      type="button"
      className="convertecom-close-button"
      onClick={props.onCouponRetrieved}
    >
      <CloseX />
    </button>

    <CustomIcon iconURL={props.icon} className="convertecom-final-money-bag" />

    <div
      className="convertecom-exciting"
      style={createStringStyle(props.strings, 'cartPopupHeader')}
    >
      {renderString(props.strings, 'cartPopupHeader')}
    </div>
    <div
      className="convertecom-cta convertecom-primary-color"
      style={createStringStyle(props.strings, 'cartPopupAmount')}
    >
      <BalanceText>
        {renderString(props.strings, 'cartPopupAmount').replace(
          '%AMOUNT%',
          props.couponAmount.toFixed(2),
        )}
      </BalanceText>
    </div>
    {props.expires ? (
      <div style={createStringStyle(props.strings, 'cartPopupTime')}>
        <BalanceText>
          {renderString(props.strings, 'cartPopupTime').replace(
            '%TIME%',
            props.expires,
          )}
        </BalanceText>
      </div>
    ) : (
      ''
    )}
    <p>
      <a
        className="convertecom-cta-link convertecom-emphasize"
        onClick={props.onCouponRetrieved}
        style={createStringStyle(props.strings, 'cartPopupLink')}
      >
        {renderString(props.strings, 'cartPopupLink')}
      </a>
    </p>
  </div>
);
