import * as React from 'react';
import SvgDefs from '../SvgDefs';
import '../../scss/convertecom-styles.scss';
import { OptInForm } from './OptInForm';
import { OptInConfirmation } from './OptInConfirmation';
import { OptInContainer } from './OptInContainer';
import { IntegrationsContext, IntegrationsContextValue } from '../../contexts/integrations.context';
import { OptInContext, OptInContextValue } from '../../contexts/optin.context';
import { OptInState, TenantConfig, ConvertEcomContext } from '../../contexts/convertecom.context';
import { renderString } from '../../utils/customStrings';

export type ConvertEcomOptInProps = {
  integrations: IntegrationsContextValue;
  optInState: OptInState;
  userId: string;
  impressionId: string;
  optIn: (discountCode: string, userId: string) => void;
  optOut: () => void;
  tenantConfig: TenantConfig;
} & OptInContextValue;

export interface ConvertEcomOptInState {
  step: number;
  optinEmail: string;
  icon: string;
}

class ConvertEcomOptIn extends React.Component<ConvertEcomOptInProps, ConvertEcomOptInState> {
  constructor(props: ConvertEcomOptInProps) {
    super(props);
    this.state = {
      step: 1,
      optinEmail: '',
      icon: '',
    };
    this.handleEmailChange = this.handleEmailChange.bind(this);
    this.handleOptIn = this.handleOptIn.bind(this);
    this.handleOptOut = this.handleOptOut.bind(this);
    this.closeOverlay = this.closeOverlay.bind(this);
    this.openOverlay = this.openOverlay.bind(this);
  }

  componentDidMount() {
    const {
      optInState,
      userId,
      tenantConfig,
    } = this.props;

    if (optInState !== OptInState.REJECTED && !userId) {
      setTimeout(this.props.openOverlay, tenantConfig.template.popupDelay);
    }
    this.setState({
      icon: renderString(tenantConfig.template, 'cartPopupCustomImage'),
    });
  }

  openOverlay() {
    this.props.openOverlay();
  }

  closeOverlay() {
    this.props.closeOverlay();
    if (
      document.getElementById('convertecom-reservation-container') &&
      this.props.userId
    ) {
      window.location.reload();
    }
  }

  handleEmailChange(event: any) {
    this.setState({ optinEmail: event.target.value });
  }

  handleOptIn(event: any) {
    event.preventDefault();
    fetch(process.env.API_URL + '/optin', {
      method: 'POST',
      cache: 'no-cache',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: this.state.optinEmail,
        storeKey: this.props.tenantConfig.shop,
        impressionId: this.props.impressionId,
      }),
    })
      .then(response => response.json())
      .then(response => {
        if (response.error || !response.id) {
          this.closeOverlay();
          console.error('ConvertEcom error: ' + response.error);
          alert('Sorry, an error occurred. Please try again later.');
        } else {
          // Opt-in will set the coupon in the context as well as set appropriate cookies
          this.props.optIn(response.fallbackCoupon, response.id);
          this.setState({ step: 2 });
        }
      })
      .catch(error => {
        this.closeOverlay();
        console.error('ConvertEcom error: ' + error.message);
      });
  }

  handleOptOut() {
    this.props.optOut();
    this.closeOverlay();
  }

  render() {
    const { icon, optinEmail, step } = this.state;
    const { template: strings } = this.props.tenantConfig;
    const { showOverlay } = this.props;
    return this.props.tenantConfig.active ? (
      <>
        {/* Hide the SVGs in the UI */}
        <div aria-hidden={true} style={{ width: 0, height: 0, overflow: 'hidden' }}><SvgDefs moneyBagGradient={strings.cartPopupIconGradient} /></div>
        <div id="convertecom-overlay" className={showOverlay ? 'convertecom-is-active' : ''}>
          <OptInContainer
            optInForm={
              <OptInForm
                handleEmailChange={this.handleEmailChange}
                handleOptIn={this.handleOptIn}
                handleOptOut={this.handleOptOut}
                icon={icon}
                strings={strings}
                optinEmail={optinEmail}
              />
            }
            optInConfirmation={
              <OptInConfirmation
                strings={strings}
                icon={icon}
                closeOverlay={this.closeOverlay}
              />
            }
            strings={strings}
            step={step}
          />
        </div>
      </>
    ) : (
      ''
    );
  }
}

export default () => (
  <ConvertEcomContext.Consumer>
    {convertEcom => (
      <OptInContext.Consumer>
        {optIn => (
          <IntegrationsContext.Consumer>
            {integrations =>
              integrations.isLoaded && convertEcom.tenantConfig && convertEcom.tenantConfig.active ? (
                <ConvertEcomOptIn
                  optInState={convertEcom.state.optInState}
                  userId={convertEcom.state.userId}
                  impressionId={convertEcom.state.impressionId}
                  optIn={convertEcom.optIn}
                  optOut={convertEcom.optOut}
                  integrations={integrations}
                  tenantConfig={convertEcom.tenantConfig}
                  {...optIn}
                />
              ) : null
            }
          </IntegrationsContext.Consumer>
        )}
      </OptInContext.Consumer>
    )}
  </ConvertEcomContext.Consumer>
);
