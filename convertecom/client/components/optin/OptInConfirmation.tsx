import * as React from 'react';
import {
  createStringStyle,
  renderString,
} from '../../utils/customStrings';
import CustomIcon from '../icons/CustomIcon';
import CloseX from '../icons/CloseX';
// @ts-ignore
import BalanceText from 'react-balance-text';
import { Template } from '@lib/common';

export interface OptInConfirmationProps {
  strings: Template;
  closeOverlay: () => void;
  icon: string;
}

export const OptInConfirmation = (props: OptInConfirmationProps) => (
  <>
    <button
      className="convertecom-close-button"
      onClick={props.closeOverlay}
      aria-label="Close"
    >
      <CloseX />
    </button>

    <CustomIcon iconURL={props.icon} className="convertecom-final-money-bag" />

    <div
      className="convertecom-exciting"
      style={createStringStyle(props.strings, 'optInResultHeader')}
    >
      <BalanceText>
        {renderString(props.strings, 'optInResultHeader')}
      </BalanceText>
    </div>

    <div
      className="convertecom-cta"
      style={createStringStyle(props.strings, 'optInResult')}
    >
      <BalanceText>{renderString(props.strings, 'optInResult')}</BalanceText>
    </div>

    <p>
      <a
        className="convertecom-cta-link"
        onClick={props.closeOverlay}
        style={createStringStyle(props.strings, 'optInResultLink')}
        role="button"
        tabIndex={0}
        onKeyPress={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            props.closeOverlay();
          }
        }}
      >
        {renderString(props.strings, 'optInResultLink')}
      </a>
    </p>
  </>
);
