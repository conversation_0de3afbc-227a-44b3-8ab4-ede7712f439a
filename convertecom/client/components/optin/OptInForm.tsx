import * as React from 'react';
import { useContext } from 'react';
import { createStringStyle, renderString } from '../../utils/customStrings';

import CustomIcon from '../icons/CustomIcon';
// @ts-ignore
import BalanceText from 'react-balance-text';
import { RecartContext } from '../../contexts/recart.context';
import { IntegrationsContext } from '../../contexts/integrations.context';
import { Template } from '@lib/common';

export interface OptInFormProps {
  strings: Template;
  optinEmail: string;
  icon: string;
  handleEmailChange: (e: any) => void;
  handleOptIn: (e: any) => void;
  handleOptOut: () => void;
}

export const OptInForm = (props: OptInFormProps) => {
  const [shouldNotify, setShouldNotify] = React.useState(false);
  const { checkboxChecked, unrecoverableError } = useContext(RecartContext);
  const { recart, isLoaded } = useContext(IntegrationsContext);

  function canSubmit() {
    if (recart && recart.isEnabled) {
      return checkboxChecked || unrecoverableError;
    }
    return true;
  }

  function handleSubmit(event: any) {
    event.preventDefault();
    if (canSubmit()) {
      props.handleOptIn(event);
    }
    setShouldNotify(true);
  }

  React.useEffect(() => {
    if (shouldNotify) {
      setTimeout(() => {
        setShouldNotify(false);
      }, 1000);
    }
  }, [shouldNotify]);

  return (
    <>
      <h3
        className="convertecom-heading"
        style={createStringStyle(props.strings, 'mainHeader')}
      >
        <BalanceText>{renderString(props.strings, 'mainHeader')}</BalanceText>
      </h3>

      <div className="convertecom-subheader">
        <div
          className="convertecom-subheading"
          style={createStringStyle(props.strings, 'mainSubHeader')}
        >
          <BalanceText>
            {renderString(props.strings, 'mainSubHeader')}
          </BalanceText>
        </div>
      </div>

      <div className="convertecom-money-bag-container">
        <button className="convertecom-money-bag" disabled={true}>
          <CustomIcon iconURL={props.icon} />
        </button>
        <button className="convertecom-money-bag" disabled={true}>
          <CustomIcon iconURL={props.icon} />
        </button>
        <button className="convertecom-money-bag" disabled={true}>
          <CustomIcon iconURL={props.icon} />
        </button>
      </div>

      <div
        className="convertecom-emphasize spacing"
        style={createStringStyle(props.strings, 'optIn')}
      >
        <strong>
          <BalanceText>{renderString(props.strings, 'optIn')}</BalanceText>
        </strong>
      </div>

      <form
        id="js-convertecom-optin-form"
        onSubmit={handleSubmit}
      >
        <div className="convertecom-optin-form__container">
          <input
            id="convertecom-optin-form__email"
            type="email"
            name="email"
            value={props.optinEmail}
            onChange={props.handleEmailChange}
            required={true}
            aria-label="Email"
            placeholder="Your email address"
          />

          <button
            type="submit"
            className="convertecom-btn convertecom-btn--submit"
            style={createStringStyle(props.strings, 'optInButton')}
          >
            {renderString(props.strings, 'optInButton')}
          </button>
        </div>
      </form>

      {isLoaded && recart && recart.isEnabled ? (
        <div
          className={`recart-container ${shouldNotify ? 'recart-container--notify' : ''}`}
        >
          <div
            className="recart-messenger-widget"
            data-source={recart.configuration.source}
            data-smart-display={
              recart.configuration.smartDisplayEnabled ? 'on' : 'off'
            }
            data-widget-skin={recart.configuration.skin || 'dark'}
          />
        </div>
      ) : null}

      <p
        className="convertecom-text-faded"
        style={createStringStyle(props.strings, 'optOutExtra')}
      >
        <a
          className="js-convertecom-opt-out"
          onClick={props.handleOptOut}
          style={createStringStyle(props.strings, 'optOutLink')}
        >
          {renderString(props.strings, 'optOutLink')}
        </a>
        {' '}{renderString(props.strings, 'optOutExtra')}
      </p>
    </>
  );
};
