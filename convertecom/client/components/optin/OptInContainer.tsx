import * as React from 'react';
import { createBackground } from '../../utils/customStrings';
import { Template } from '@lib/common';
// tslint:disable-next-line:no-var-requires
const classnames = require('classnames');

export interface OptInContainerProps {
  optInForm: React.ReactNode;
  optInConfirmation: React.ReactNode;
  strings: Template;
  step: number;
}

export const OptInContainer = (props: OptInContainerProps) => {
  const { strings, step, optInForm, optInConfirmation } = props;

  return (
    <>
      <div
        className={classnames(
          'convertecom-inner-container',
          'convertecom-inner-container--step-1',
          step === 1 ? 'convertecom-is-active' : '',
        )}
        id="convertecom-optin-step1"
        style={createBackground(strings, 'optInBackground')}
      >
        {optInForm}
      </div>
      <div
        className={classnames(
          'convertecom-inner-container',
          'convertecom-inner-container--step-2',
          step === 2 ? 'convertecom-is-active' : '',
        )}
        id="convertecom-optin-step2"
        style={createBackground(strings, 'optInBackground')}
      >
        {optInConfirmation}
      </div>
    </>
  );
};
