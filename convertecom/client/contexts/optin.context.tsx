import * as React from 'react';

export enum OverlayLockedBy {
  RECART = 'RECART',
  DEFAULT = 'DEFAULT',
}

export interface OptInContextValue {
  openOverlay: (openAs?: OverlayLockedBy) => void;
  closeOverlay: () => void;
  showOverlay: boolean;
  lockedBy: OverlayLockedBy;
  lockOverlay: (by: OverlayLockedBy) => void;
}

export const OptInContext = React.createContext<OptInContextValue>(undefined);

export const OptInContextProvider: React.FunctionComponent = props => {
  const openOverlayTimeout = React.useRef<number>(null);
  const [isOpen, setIsOpen] = React.useState(false);
  const [lockedBy, setLockedBy] = React.useState<OverlayLockedBy>(
    OverlayLockedBy.DEFAULT,
  );
  const openOverlay = (openAs: OverlayLockedBy = OverlayLockedBy.DEFAULT) => {
    if (
      (lockedBy === OverlayLockedBy.DEFAULT || lockedBy === openAs) &&
      openOverlayTimeout !== null
    ) {
      setIsOpen(true);
      // openOverlayTimeout.current = window.setTimeout(() => {
      //   setIsOpen(true);
      //   // TODO: Set this to a user configurable value. ZING-29
      // }, );
    }
  };

  const closeOverlay = () => {
    setIsOpen(false);
  };

  const lockOverlay = (by: OverlayLockedBy) => {
    if (lockedBy === OverlayLockedBy.DEFAULT) {
      setLockedBy(by);
    }
  };

  return (
    <OptInContext.Provider
      value={{
        showOverlay: isOpen,
        openOverlay,
        closeOverlay,
        lockedBy,
        lockOverlay,
      }}
      children={props.children}
    />
  );
};
