import * as React from 'react';
import { FunctionComponent, useContext } from 'react';
import { OptInState, ConvertEcomContext } from './convertecom.context';
import { IntegrationsContext } from './integrations.context';
import { OptInContext, OverlayLockedBy } from './optin.context';

export enum RecartState {
  NOT_LOADED = 'STATE_NOT_LOADED',
  INITIAL = 'STATE_INITIAL',
  ERROR = 'STATE_ERROR',
  SUBSCRIBED = 'STATE_SUBSCRIBED',
}

export interface RecartContextValue {
  state: RecartState;
  checkboxChecked: boolean;
  unrecoverableError: boolean;
}

export const RecartContext = React.createContext<RecartContextValue>(null);

export const RecartContextProvider: FunctionComponent = props => {
  const [state, setState] = React.useState<RecartState>(RecartState.NOT_LOADED);
  const [checkboxChecked, setCheckboxChecked] = React.useState(false);
  const [unrecoverableError, setUnrecoverableError] = React.useState(false);
  const [forceOverlayTimeout, setForceOverlayTimeout] = React.useState(
    undefined,
  );
  const { state: zingState, tenantConfig } = useContext(ConvertEcomContext);
  const { recart, isLoaded } = useContext(IntegrationsContext);
  const { openOverlay, showOverlay, lockOverlay } = useContext(OptInContext);

  function queueOverlay() {
    // TODO: Move timeout up so it can be dynamically controlled. ZING-29
    if (zingState.optInState === OptInState.PENDING) {
      openOverlay(OverlayLockedBy.RECART);
    }
  }

  function onRecartMessengerPluginLoaded() {
    try {
      const plugin = (window as any)._rmp;
      plugin.initWidget(['button[type="submit"].convertecom-btn--submit']);
      let retryCount = 0;

      plugin.on('state_change', (e: any) => {
        /*
          Use a global recart state because local `recart` doesn't update in this method due to it being
          assigned in useEffect. I think this could be re-written as a class component and resolve it.
         */
        const zingRecartConfig = (window as any).zingRecartConfig;
        if (
          zingRecartConfig &&
          e.widget &&
          e.widget.type === zingRecartConfig.configuration.source
        ) {
          setState(e.state);
          switch (e.state) {
            case RecartState.NOT_LOADED: {
              if (retryCount < 3) {
                retryCount++;
                return;
              }
              setUnrecoverableError(true);
              return;
            }
            case RecartState.INITIAL: {
              queueOverlay();
              return;
            }
            case RecartState.ERROR: {
              // Do nothing
              return;
            }
            default:
              // do nothing
              return;
          }
        }
      });

      plugin.on('subscribe_clicked', (e: any) => {
        const zingRecartConfig = (window as any).zingRecartConfig;
        if (
          zingRecartConfig &&
          e.widget &&
          e.widget.type === zingRecartConfig.configuration.source
        ) {
          setCheckboxChecked(e.widget.isCheckboxChecked);
        }
      });

      plugin.on('fbsdk_not_loaded', () => {
        setUnrecoverableError(true);
        if (!showOverlay) {
          queueOverlay();
        }
      });

      plugin.on('internal_error', () => {
        setUnrecoverableError(true);
        if (!showOverlay) {
          queueOverlay();
        }
      });
    } catch (e) {
      setUnrecoverableError(true);
      if (!showOverlay) {
        queueOverlay();
      }
    }
  }

  React.useEffect(() => {
    if (process.env.ENVIRONMENT === 'development') {
      (window as any)._rmp = (() => {
        const listeners: { [key: string]: Array<(e: any) => void> } = {};
        return {
          initWidget(items: any) {
            return;
          },
          on(evt: string, cb: any) {
            if (typeof listeners[evt] === 'undefined') {
              listeners[evt] = [];
            }
            listeners[evt].push(cb);
          },
          emit(evt: string, s: string) {
            if (typeof listeners[evt] === 'undefined') {
              return;
            }
            listeners[evt].forEach(cb => {
              cb({
                state: s,
                widget: {
                  type: 'convertecom',
                  isCheckboxChecked: false,
                },
              });
            });
          },
        };
      })();
    }
  }, []);

  React.useEffect(() => {
    /*
    Run a bunch of checks before setting up Recart open. This could potentially cause issues if the user is using Recart but
    not through Zing itself
     */
    if (
      isLoaded &&
      typeof recart !== 'undefined' &&
      recart.isEnabled &&
      typeof tenantConfig !== 'undefined' &&
      typeof (window as any).onRecartMessengerPluginLoaded !== 'function' &&
      (window as any).onRecartMessengerPluginLoaded !== false
    ) {
      // A proxy is used to return `false` after initWidget is called in onRecartMessengerPluginLoaded
      if (
        typeof (window as any).onRecartMessengerPluginLoaded !== 'function' &&
        (window as any).onRecartMessengerPluginLoaded !== false
      ) {
        (window as any).onRecartMessengerPluginLoaded = onRecartMessengerPluginLoaded;
      }
      lockOverlay(OverlayLockedBy.RECART);
      // Set a timeout so that the popup will display if anything goes wrong or Recart doesn't load
      if (state !== RecartState.INITIAL) {
        const timeout = setTimeout(() => {
          setUnrecoverableError(true);
          setCheckboxChecked(true);
          queueOverlay();
        }, tenantConfig.template.popupDelay);
        setForceOverlayTimeout(timeout);
      }
    }
  }, [isLoaded, recart, state, tenantConfig, state]);

  /*
   This effect may not be called after the effect above that sets the timeout, but it
   should be here just in case. The order that things happen with the recart widget isn't
   necessarily intuitive and this should cover all cases.
   */
  React.useEffect(() => {
    // Reset the forced timeout
    if (state === RecartState.INITIAL && forceOverlayTimeout) {
      setUnrecoverableError(false);
      setCheckboxChecked(false);
      clearTimeout(forceOverlayTimeout);
      setForceOverlayTimeout(undefined);
    }
  }, [state, forceOverlayTimeout]);

  React.useEffect(() => {
    (window as any).zingRecartConfig = recart;
  }, [recart]);

  return (
    <RecartContext.Provider
      value={{ state, checkboxChecked, unrecoverableError }}
    >
      {props.children}
    </RecartContext.Provider>
  );
};
