import * as React from 'react';
import { FunctionComponent, useEffect, useState } from 'react';
import { RecartContextProvider } from './recart.context';
import { getIntegrations } from '../services/integration.service';

export interface RecartMessengerConfig {
  isEnabled: boolean;
  configuration: {
    source: string;
    smartDisplayEnabled: boolean;
    forceSendToMessenger: boolean;
    skin: 'light' | 'dark';
  };
}

export interface IntegrationsContextValue {
  isLoaded: boolean;
  recart?: RecartMessengerConfig;
}

export const IntegrationsContext = React.createContext<
  IntegrationsContextValue
>({ isLoaded: false });

export const IntegrationsContextProvider: FunctionComponent = props => {
  const [integrations, setIntegrations] = useState<IntegrationsContextValue>({
    isLoaded: false,
  });

  useEffect(() => {
    const shop = (window as any).Shopify.shop;
    getIntegrations(shop).then(res => {
      setIntegrations({ isLoaded: true, ...res });
    });
  }, []);

  return (
    <IntegrationsContext.Provider value={integrations}>
      <RecartContextProvider>{props.children}</RecartContextProvider>
    </IntegrationsContext.Provider>
  );
};
