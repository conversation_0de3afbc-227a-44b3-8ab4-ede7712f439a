import * as React from 'react';
import { useCallback, useEffect } from 'react';
import _get = require('lodash/get');
import { applyDiscountCodeForShopify } from '../services/coupon.service';
import { fetchTenant } from '../services/tenant.service';
import { Template } from '@lib/common';
import { EventBus } from '../index';

// tslint:disable-next-line:no-var-requires
const cookie = require('react-cookies');

enum ConvertEcomCookies {
  OPT_IN_DECLINED = 'convertEcomOptInDeclined',
  OPT_IN_DECLINED_EXPIRES = 'convertEcomOptInDeclinedExpires',
  USER_ID = 'convertEcomUserId',
  IMPRESSION_ID = 'convertEcomImpressionId',
  SHOPIFY_DISCOUNT_CODE = 'discount_code',
  DISCOUNT_CODE = 'convertEcomDiscountCode',
  RESERVED_UNTIL = 'convertEcomDiscountReservedUntil',
  FALLBACK_CODE = 'convertEcomFallbackDiscountCode',
}

export enum OptInState {
  REJECTED = 'REJECTED',
  ACCEPTED = 'ACCEPTED',
  PENDING = 'PENDING',
}

export interface ConvertEcomState {
  hydrated: boolean;
  optInState: OptInState;
  userId?: string;
  impressionId?: string;
  discountCode?: string;
  convertEcomDiscountCode?: string;
  fallbackDiscountCode?: string;
  minimumDiscountPercentage?: number;
  reservedUntil?: string;
}

export interface TenantConfig {
  active: boolean;
  shop: string;
  template?: Template;
}

export interface ConvertEcomContextValue {
  state: ConvertEcomState;
  hydrate: () => void;
  saveCode: (discountCode: string, reservedUntil: string) => void;
  optIn: (discountCode: string, userId: string) => void;
  optOut: () => void;
  clearCode: () => void;
  tenantConfig: TenantConfig;
}

const secondsInYear = 60 * 60 * 24 * 365;

function initialState(): ConvertEcomState {
  return {
    hydrated: false,
    optInState: OptInState.PENDING,
  };
}

function getOptInState(userId?: string) {
  if (userId) {
    return OptInState.ACCEPTED;
  }
  const optInDeclinedCookie = cookie.load(ConvertEcomCookies.OPT_IN_DECLINED);
  const optInDeclinedStorage = window.sessionStorage.getItem(
    ConvertEcomCookies.OPT_IN_DECLINED,
  );
  const optInDeclinedExpiresStorage = window.sessionStorage.getItem(
    ConvertEcomCookies.OPT_IN_DECLINED_EXPIRES,
  );

  if (
    typeof optInDeclinedCookie === 'undefined' &&
    (typeof optInDeclinedStorage === 'undefined' || !optInDeclinedStorage)
  ) {
    return OptInState.PENDING;
  }
  if (typeof optInDeclinedCookie !== 'undefined') {
    return optInDeclinedCookie === 'true' || optInDeclinedCookie === true
      ? OptInState.REJECTED
      : OptInState.ACCEPTED;
  }
  if (typeof optInDeclinedStorage !== 'undefined') {
    const expiresAt = parseInt(optInDeclinedExpiresStorage, 10);
    if (!Number.isNaN(expiresAt) && Date.now() > expiresAt) {
      window.sessionStorage.removeItem(ConvertEcomCookies.OPT_IN_DECLINED_EXPIRES);
      return OptInState.PENDING;
    }
    return optInDeclinedStorage === 'true'
      ? OptInState.REJECTED
      : OptInState.ACCEPTED;
  }
}

export const ConvertEcomContext = React.createContext<ConvertEcomContextValue>(null);
export interface ConvertEcomContextProviderProps {
  eventBus?: EventBus;
}
export const ConvertEcomContextProvider: React.FunctionComponent<
  ConvertEcomContextProviderProps
> = ({ eventBus, children }) => {
  const [state, setState] = React.useState<ConvertEcomState>(initialState());
  const [hydrated, setHydrated] = React.useState(false);
  const [tenantConfig, doSetTenantConfig] = React.useState<TenantConfig>(
    undefined,
  );

  const setTenantConfig = useCallback((config: TenantConfig) => {
    doSetTenantConfig(config);
    if (eventBus) {
      eventBus.publish('tenant/config/updated', config);
    }
  }, []);

  const hydrate = () => {
    const userId = cookie.load(ConvertEcomCookies.USER_ID);
    const optInState = getOptInState(userId);
    const impressionId = cookie.load(ConvertEcomCookies.IMPRESSION_ID);
    const shopifyDiscountCode = cookie.load(ConvertEcomCookies.SHOPIFY_DISCOUNT_CODE);
    const convertEcomDiscountCode = cookie.load(ConvertEcomCookies.DISCOUNT_CODE);
    const reservedUntil = cookie.load(ConvertEcomCookies.RESERVED_UNTIL);
    const fallbackDiscountCode = cookie.load(ConvertEcomCookies.FALLBACK_CODE);

    setState(s => ({
      ...s,
      optInState,
      userId,
      impressionId,
      convertEcomDiscountCode,
      reservedUntil,
      fallbackDiscountCode,
      discountCode: shopifyDiscountCode,
      hydrated: true,
    }));
  };

  const saveCode = (discountCode: string, reservedUntil?: string) => {
    // This is also applied in `applyDiscountCodeForShopify`, but whatever
    cookie.save(ConvertEcomCookies.SHOPIFY_DISCOUNT_CODE, discountCode);
    cookie.save(ConvertEcomCookies.DISCOUNT_CODE, discountCode, { maxAge: 60 * 60 });
    if (reservedUntil) {
      cookie.save(ConvertEcomCookies.RESERVED_UNTIL, reservedUntil);
    }
    setState(s => ({
      ...s,
      convertEcomDiscountCode: discountCode,
      discountCode,
      reservedUntil,
    }));
  };

  const clearCode = () => {
    cookie.remove(ConvertEcomCookies.DISCOUNT_CODE);
    cookie.remove(ConvertEcomCookies.RESERVED_UNTIL);
    setState(s => ({
      ...s,
      discountCode: undefined,
      convertEcomDiscountCode: undefined,
      reservedUntil: undefined,
    }));
  };
  const optIn = (discountCode: string, userId: string) => {
    cookie.save(ConvertEcomCookies.USER_ID, userId, { maxAge: secondsInYear });
    cookie.save(ConvertEcomCookies.FALLBACK_CODE, discountCode, {
      maxAge: secondsInYear,
    });
    cookie.save(ConvertEcomCookies.OPT_IN_DECLINED, false);
    window.sessionStorage.setItem(ConvertEcomCookies.OPT_IN_DECLINED, 'false');
    setState(s => ({
      ...s,
      optInState: OptInState.ACCEPTED,
      userId,
      fallbackDiscountCode: discountCode,
    }));
  };

  const saveImpression = (impressionId: string) => {
    cookie.save(ConvertEcomCookies.IMPRESSION_ID, impressionId);
    setState(s => ({
      ...s,
      impressionId,
    }));
  };

  const optOut = () => {
    const { optOutTtl } = tenantConfig.template;
    const optOutSeconds =
      typeof optOutTtl === 'undefined' || optOutTtl < 1 ? undefined : optOutTtl;
    cookie.save(ConvertEcomCookies.OPT_IN_DECLINED, true, { maxAge: optOutSeconds });
    window.sessionStorage.setItem(ConvertEcomCookies.OPT_IN_DECLINED, 'true');
    if (!!optOutSeconds) {
      const expiresAt = optOutSeconds * 1000 + Date.now(); // Turn into milliseconds
      window.sessionStorage.setItem(
        ConvertEcomCookies.OPT_IN_DECLINED_EXPIRES,
        expiresAt.toString(),
      );
    }
    setState(s => ({
      ...s,
      optInState: OptInState.REJECTED,
    }));
  };

  // Load everything into the context's state
  useEffect(() => {
    hydrate();
    setHydrated(true);
  }, []);

  // Load tenant config
  useEffect(() => {
    const shop = _get(window, 'Shopify.shop', undefined);
    if (state.hydrated && shop && typeof tenantConfig === 'undefined') {
      fetchTenant(shop, state.impressionId)
        .then(data => {
          setTenantConfig({
            shop,
            active: data.active,
            template: {
              ...data.template,
              popupDelay:
                typeof data.template.popupDelay === 'undefined'
                  ? 3000
                  : data.template.popupDelay,
            },
          });
          if (!state.impressionId) {
            saveImpression(data.impressionId);
          }
        })
        .catch(e => {
          setTenantConfig({
            shop,
            active: false,
            template: undefined,
          });
        });
    }
  }, [state.hydrated, state.impressionId, setTenantConfig, tenantConfig]);

  // Ensure the code is set in the user's session
  useEffect(() => {
    const { discountCode, fallbackDiscountCode, convertEcomDiscountCode } = state;
    let codeToSet: string;
    if (!discountCode && convertEcomDiscountCode) {
      // Discount code was reset, set it again
      codeToSet = convertEcomDiscountCode;
    } else if (!discountCode && fallbackDiscountCode) {
      // Discount code was reset and we have a fallback, set it
      codeToSet = fallbackDiscountCode;
    } else if (
      discountCode &&
      discountCode === fallbackDiscountCode &&
      convertEcomDiscountCode
    ) {
      // Discount code exists but needs to be set to proper discount code
      codeToSet = convertEcomDiscountCode;
    }
    if (codeToSet) {
      applyDiscountCodeForShopify(codeToSet).then(() => {
        setState(s => ({
          ...s,
          discountCode: codeToSet,
        }));
      });
    }
  }, [state.discountCode, state.fallbackDiscountCode, state.convertEcomDiscountCode]);

  return (
    <ConvertEcomContext.Provider
      value={{
        state,
        hydrate,
        saveCode,
        optIn,
        optOut,
        clearCode,
        tenantConfig,
      }}
    >
      {hydrated ? children : null}
    </ConvertEcomContext.Provider>
  );
};
