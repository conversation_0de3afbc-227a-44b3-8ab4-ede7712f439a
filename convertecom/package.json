{"name": "convertecom", "version": "0.0.1", "description": "", "author": "", "license": "MIT", "scripts": {"build": "tsc -p tsconfig.build.json && node --openssl-legacy-provider ./node_modules/.bin/webpack --config webpack.prod.js --mode production", "format": "prettier --write \"src/**/*.ts\"", "client:dev": "node --openssl-legacy-provider ./node_modules/.bin/webpack-dev-server --open --config webpack.dev.js --mode development", "start": "ts-node -r tsconfig-paths/register src/main.ts", "start:dev": "nodemon", "start:debug": "nodemon --config nodemon-debug.json", "start:prod": "node -r ./tsconfig-paths-bootstrap.js dist/src/main.js", "lint": "tslint -p tsconfig.json -c tslint.json", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "engines": {"node": "20.x"}, "dependencies": {"@nestjs/common": "^6.0.0", "@nestjs/core": "^6.0.0", "@nestjs/jwt": "^6.0.0", "@nestjs/passport": "^6.0.0", "@nestjs/platform-express": "^6.1.1", "@nestjs/serve-static": "^1.0.1", "@nestjs/testing": "^6.2.0", "@nestjs/typeorm": "^6.0.0", "@sentry/node": "5.6.2", "@shopify/shopify-api": "^6.2.0", "axios": "^0.19.0", "bcrypt": "^5.1.0", "class-transformer": "^0.2.0", "class-validator": "^0.9.1", "classnames": "^2.2.6", "css-loader": "^3.2.0", "dotenv": "^7.0.0", "fs": "0.0.1-security", "helmet": "^3.18.0", "lodash": "^4.17.11", "lossless-json": "^1.0.3", "mysql": "^2.17.1", "nest-raven": "^5.0.0", "newrelic": "^9.10.2", "nodemon": "^1.18.9", "passport": "^0.4.0", "passport-http-bearer": "^1.0.1", "passport-jwt": "^4.0.0", "pg": "8.9.0", "pg-connection-string": "^2.1.0", "prettier": "^1.15.3", "pug": "^2.0.3", "react": "^16.11.0", "react-balance-text": "^2.0.1", "react-cookies": "^0.1.1", "react-dom": "^16.11.0", "react-scripts": "^5.0.1", "react-svg": "^10.0.23", "reflect-metadata": "^0.1.12", "rxjs": "^6.3.3", "sass-loader": "^8.0.0", "shopify-api-node": "^3.15.0", "style-loader": "^1.0.0", "ts-loader": "^8.4.0", "ts-node": "^10.9.1", "tsconfig-paths": "^3.7.0", "tslint": "5.12.1", "typeorm": "0.2.17", "typescript": "4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node", "verbose": true}, "devDependencies": {"@babel/core": "^7.7.0", "@babel/preset-env": "^7.7.1", "@babel/preset-react": "^7.7.0", "@svgr/webpack": "^4.3.3", "@types/axios": "^0.14.0", "@types/dotenv": "^6.1.1", "@types/express": "^4.16.0", "@types/istanbul-lib-report": "^1.1.1", "@types/jest": "^23.3.13", "@types/lodash": "^4.14.153", "@types/newrelic": "^5.11.2", "@types/node": "^10.17.4", "@types/react": "^16.9.11", "@types/react-cookies": "^0.1.0", "@types/react-dom": "^16.9.3", "@types/react-svg": "^5.0.0", "@types/supertest": "^2.0.7", "babel-loader": "^8.0.6", "dotenv-webpack": "^1.7.0", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.2.0", "jest": "^23.6.0", "rimraf": "^2.6.2", "sass": "^1.58.3", "supertest": "^3.4.1", "ts-jest": "^23.10.5", "tslint-react": "^4.1.0", "webpack": "^4.41.2", "webpack-bundle-analyzer": "^3.8.0", "webpack-cli": "^3.3.10", "webpack-dev-server": "^3.9.0", "webpack-merge": "^4.2.2"}}