{"name": "convertecom-admin-ui", "version": "0.1.0", "private": true, "dependencies": {"@date-io/core": "^1.3.13", "@date-io/date-fns": "^1.3.13", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@material-ui/pickers": "^3.3.10", "@shopify/app-bridge-react": "^3.7.7", "@tanstack/react-location": "^3.7.4", "@types/babel__core": "^7.1.17", "@types/babel__traverse": "^7.0.8", "@types/downloadjs": "^1.4.1", "@types/hookrouter": "^2.2.1", "@types/jest": "^26.0.24", "@types/jsonwebtoken": "^8.5.1", "@types/lodash": "^4.14.167", "@types/moment": "^2.13.0", "@types/moment-timezone": "^0.5.12", "@types/mui-datatables": "^3.7.9", "@types/node": "^16.11.7", "@types/numeral": "^0.0.26", "@types/react": "^17.0.19", "@types/react-color": "^3.0.1", "@types/react-dom": "^17.0.11", "@types/react-router-dom": "^5.1.7", "@types/victory": "^31.0.20", "apexcharts": "^4.0.0", "aws-s3": "^2.0.5", "axios": "^0.24.0", "buffer": "^6.0.3", "clsx": "^1.1.1", "crypto-browserify": "^3.12.1", "date-fns": "^2.28.0", "downloadjs": "^1.4.7", "express": "^4.17.1", "formdata-polyfill": "^3.0.19", "http-proxy-middleware": "^2.0.1", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "mui-datatables": "^3.7.9", "numeral": "^2.0.6", "prettier": "^2.4.1", "process": "^0.11.10", "prop-types": "^15.7.2", "react": "^17.0.2", "react-apexcharts": "^1.4.0", "react-app-rewired": "^2.2.1", "react-color": "^2.17.3", "react-dom": "^17.0.2", "react-hook-form": "^7.44.3", "react-images-upload": "^1.2.7", "react-json-view": "^1.19.1", "react-jsx-runtime": "^1.0.0-alpha.1", "react-router-dom": "^6.4.3", "react-scripts": "^5.0.1", "stream-browserify": "^3.0.0", "url-search-params-polyfill": "^7.0.0", "util": "^0.12.5", "uuid": "^8.3.2", "victory": "^35.3.0"}, "scripts": {"start": "react-app-rewired start", "start:prod": "node server.js", "build": "react-app-rewired build", "test": "react-scripts test", "eject": "react-scripts eject", "format": "prettier --write \"src/**/*.{ts,tsx}\""}, "eslintConfig": {"extends": "react-app"}, "resolutions": {"react": "^17.0.2", "react-dom": "^17.0.2", "react-dnd": "^11.1.3"}, "engines": {"node": "18.x"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.0", "@babel/plugin-proposal-optional-chaining": "^7.16.0", "@babel/plugin-proposal-private-methods": "^7.16.0", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@babel/preset-typescript": "^7.16.0", "babel-jest": "^26.6.3", "typescript": "^4.7.4"}}