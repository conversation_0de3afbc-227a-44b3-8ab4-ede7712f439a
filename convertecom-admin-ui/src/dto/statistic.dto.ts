export interface DashboardStatisticsDto {
  legacyDiscountSum: number;
  convertEcomDiscountSum: number;
  convertEcomSubtotalSum: number;
  storeSubtotalSum: number;
  recoveredRevenue: number;
  totalOrders: number;
  uniqueCustomers: number;
  totalConvertEcomOrders: number;
  totalVisitors: number;
  uniqueVisitorCustomers: number;
  totalVisitorOptins: number;
  totalVisitorOrders: number;
  visitorConversionRate: number;
  orderConversionRate: number;
  uniqueCustomerConversionRate: number;
  pipelineConversionRate: number;
  roiYieldPercentage: number;
}
