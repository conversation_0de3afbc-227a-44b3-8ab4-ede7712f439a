import { AuditLogDto } from './audit-log.dto';

export interface FeatureFlagsDto {
  billingTrial?: boolean;
  billingTest?: boolean;
  couponsRotateFallback?: boolean;
  statisticsDashboardUpdated?: boolean;
  billingRecoveredRevenue?: boolean;
  billingSummaryHistory?: boolean;
}

export interface TenantDto {
  id: string;
  platform: string;
  externalId: string;
  name: string;
  couponPercentage: string;
  legacyDiscountPercentage: string;
  minimumGrossPercentage: string;
  reservationMinutes: number;
  apiCredentials: any;
  emailPlatformCredentials: {
    platform: string;
    apiKey: string;
    listId: string;
    startDate: string;
  };
  billingCredentials: any;
  billingPercentage: string;
  template: {
    popupDelay: number;
    optOutTtl?: number;
    optInBackground: {
      text: string;
      color: string;
      colorRgba: {
        r: number;
        g: number;
        b: number;
        a: number;
      };
    };
    cartBackground: { text: string; color: string };
    mainHeader: {
      text: string;
      color: string;
    };
    mainSubHeader: {
      text: string;
      color: string;
    };
    optIn: {
      text: string;
      color: string;
    };
    optInButton: {
      text: string;
      color: string;
      backgroundColor: string;
    };
    optOutLink: { text: string; color: string };
    optOutExtra: { text: string; color: string };
    optInResultHeader: { text: string; color: string };
    optInResult: {
      text: string;
      color: string;
    };
    optInResultLink: { text: string; color: string };
    cart: {
      text: string;
      color: string;
    };
    cartButton: {
      text: string;
      color: string;
      backgroundColor: string;
    };
    cartAmount: {
      text: string;
      color: string;
    };
    cartTime: {
      text: string;
      color: string;
    };
    cartExpired: {
      text: string;
      color: string;
    };
    cartExpiredButton: {
      text: string;
      color: string;
      backgroundColor: string;
    };
    cartInvalid: {
      text: string;
      color: string;
    };
    cartInvalidButton: {
      text: string;
      color: string;
      backgroundColor: string;
    };
    cartPopupPickOne: { text: string; color: string };
    cartPopupHeader: { text: string; color: string };
    cartPopupAmount: {
      text: string;
      color: string;
    };
    cartPopupTime: {
      text: string;
      color: string;
    };
    cartPopupLink: { text: string; color: string };
    cartPopupIconGradient: {
      text: string;
      color: string;
      backgroundColor: string;
    };
    cartPopupCustomImage: { text: string };
  };
  queueBatchSize: number;
  active: boolean;
  onboardingStatus: {
    isBillingSetup: boolean;
    arePopupsCustomized: boolean;
    isCodeSnippetAdded: boolean;
    isConvertEcomActivated: boolean;
  };
  featureFlags: FeatureFlagsDto;
  trialEndsOn: string;
  createdAt: string;
  updatedAt: string;
  fallbackCoupon: any;
  fallbackCouponMaxPercentage: number;
  latestActivationAction?: AuditLogDto;
}
