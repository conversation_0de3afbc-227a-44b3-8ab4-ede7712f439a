import { Grid } from '@material-ui/core';
import LoadingProgress from '../LoadingProgress';
import React, { FunctionComponent } from 'react';

export const PageContainer: FunctionComponent = props => (
  <Grid container spacing={4}>
    <LoadingProgress />
    {props.children}
    {/*<Grid item xs={12} md={3}>*/}
    {/*  <Typography component="h6" variant="h6" color="primary" gutterBottom>*/}
    {/*    {props.title ? props.title : null}*/}
    {/*  </Typography>*/}
    {/*  {props.description ? (*/}
    {/*    <Typography component="p" variant="body2">*/}
    {/*      {props.description}*/}
    {/*    </Typography>*/}
    {/*  ) : null}*/}
    {/*  {props.sidebarContent ? props.sidebarContent : null}*/}
    {/*</Grid>*/}

    {/*<Grid item xs={12} md={9}>*/}
    {/*  <LoadingProgress />*/}
    {/*  {props.children}*/}
    {/*</Grid>*/}
  </Grid>
);
