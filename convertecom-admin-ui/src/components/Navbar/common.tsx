import React from 'react';
import { Link } from 'react-router-dom';
import { ListItem, ListItemProps, makeStyles } from '@material-ui/core';

interface Props {
  nested?: boolean;
  className?: string;
}

export type ListItemLinkProps = ListItemProps<typeof Link, Props> & { href?: string };

const useStyles = makeStyles(theme => ({
  nested: {
    paddingLeft: theme.spacing(4),
    paddingTop: theme.spacing(0.75),
    paddingBottom: theme.spacing(0.75),
  },
}));

/**
 * Custom link component for Material-UI ListItem using React Router v6
 * Forwarded ref due to errors from MUI https://material-ui.com/guides/composition/#caveat-with-refs
 * @param props
 * @constructor
 */
export const ListItemLink = (props: ListItemLinkProps) => {
  const classes = useStyles();
  const { nested, button, href, className, ...rest } = props;

  // Create a custom Link component that properly passes the to prop
  const CustomLink = React.forwardRef<HTMLAnchorElement>((linkProps, ref) => {
    // We need to explicitly pass the to prop to the Link component
    return <Link to={href || ''} ref={ref} {...linkProps} />;
  });

  // Set the to prop explicitly on the CustomLink component
  CustomLink.displayName = 'CustomLink';

  return (
    <ListItem
      className={`${nested ? classes.nested : ''} ${className || ''}`}
      button
      component={CustomLink}
      to={href || ''} // Add the to prop here to satisfy TypeScript
      {...rest}
    />
  );
};
