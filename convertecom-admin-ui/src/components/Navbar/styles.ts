import { makeStyles } from '@material-ui/core/styles';

const drawerWidth = 260;

export const useStyles = makeStyles(theme => ({
  toolbar: {
    background: '#FFFFFF',
    color: theme.palette.text.primary,
    padding: theme.spacing(0, 2),
    height: 64,
    boxShadow: 'none',
    borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
  },
  toolbarIcon: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing(0, 1.5),
    height: 64,
    backgroundColor: '#FFFFFF',
    borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
  },
  appBar: {
    zIndex: theme.zIndex.drawer + 1,
    boxShadow: 'none',
    backgroundColor: '#FFFFFF',
    transition: theme.transitions.create(['width', 'margin'], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
  },
  appBarShift: {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(['width', 'margin'], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
  logo: {
    height: '36px',
    marginRight: theme.spacing(2),
    width: 'auto',
  },
  logoContainer: {
    display: 'flex',
    alignItems: 'center',
  },
  menuButton: {
    marginRight: theme.spacing(2),
    color: theme.palette.text.secondary,
  },
  menuButtonHidden: {
    display: 'none',
  },
  title: {
    flexGrow: 1,
    color: theme.palette.text.primary,
    fontWeight: 600,
  },
  drawerPaper: {
    position: 'relative',
    [theme.breakpoints.down('sm')]: {
      position: 'fixed'
    },
    whiteSpace: 'nowrap',
    width: drawerWidth,
    backgroundColor: '#FFFFFF',
    borderRight: '1px solid rgba(0, 0, 0, 0.08)',
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
  drawerPaperClose: {
    overflowX: 'hidden',
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    width: theme.spacing(7),
    [theme.breakpoints.down('sm')]: {
      width: theme.spacing(0),
    },
  },
  navSection: {
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(1),
    padding: theme.spacing(0, 2),
  },
  navSectionTitle: {
    fontSize: '0.75rem',
    fontWeight: 600,
    textTransform: 'uppercase',
    color: theme.palette.text.secondary,
    letterSpacing: '0.05em',
    marginBottom: theme.spacing(1),
  },
  navItem: {
    margin: theme.spacing(0.5, 0),
    padding: theme.spacing(1, 2),
    borderRadius: theme.shape.borderRadius,
    '&.active': {
      backgroundColor: 'rgba(0, 178, 169, 0.08)',
      color: theme.palette.primary.main,
      '& .MuiListItemIcon-root': {
        color: theme.palette.primary.main,
      },
    },
  },
  navItemIcon: {
    minWidth: 36,
    color: theme.palette.text.secondary,
  },
  navItemText: {
    fontSize: '0.9375rem',
    fontWeight: 500,
  },
  navItemNested: {
    paddingLeft: theme.spacing(4),
  },
  profileSection: {
    padding: theme.spacing(2),
    marginTop: 'auto',
    borderTop: '1px solid rgba(0, 0, 0, 0.08)',
  },
  profileButton: {
    width: '100%',
    justifyContent: 'flex-start',
    padding: theme.spacing(1),
    textAlign: 'left',
  },
  profileAvatar: {
    width: 36,
    height: 36,
    marginRight: theme.spacing(1.5),
    backgroundColor: theme.palette.primary.main,
  },
  profileInfo: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  profileName: {
    fontWeight: 600,
    fontSize: '0.9375rem',
    color: theme.palette.text.primary,
  },
  profileEmail: {
    fontSize: '0.8125rem',
    color: theme.palette.text.secondary,
  },
  hide: {
    [theme.breakpoints.down('xs')]: {
      display: 'none'
    }
  },
  searchBar: {
    position: 'relative',
    borderRadius: theme.shape.borderRadius,
    backgroundColor: 'rgba(0, 0, 0, 0.04)',
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.06)',
    },
    marginRight: theme.spacing(2),
    marginLeft: 0,
    width: '100%',
    [theme.breakpoints.up('sm')]: {
      marginLeft: theme.spacing(3),
      width: 'auto',
    },
  },
  searchIcon: {
    padding: theme.spacing(0, 2),
    height: '100%',
    position: 'absolute',
    pointerEvents: 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: theme.palette.text.secondary,
  },
  inputRoot: {
    color: theme.palette.text.primary,
  },
  inputInput: {
    padding: theme.spacing(1, 1, 1, 0),
    paddingLeft: `calc(1em + ${theme.spacing(4)}px)`,
    transition: theme.transitions.create('width'),
    width: '100%',
    [theme.breakpoints.up('md')]: {
      width: '20ch',
    },
  },
  headerActions: {
    display: 'flex',
    alignItems: 'center',
  },
  notificationButton: {
    marginRight: theme.spacing(1),
  },
  badge: {
    backgroundColor: theme.palette.primary.main,
  },
  drawerHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing(0, 1),
  },
  drawerLogo: {
    height: 40,
    marginLeft: theme.spacing(1),
  },
}));
