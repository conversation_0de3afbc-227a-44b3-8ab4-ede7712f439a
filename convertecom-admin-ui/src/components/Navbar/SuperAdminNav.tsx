import React from 'react';
import { Box, Divider, ListItemIcon, ListItemText, Typography } from '@material-ui/core';
import DashboardIcon from '@material-ui/icons/DashboardOutlined';
import AssessmentIcon from '@material-ui/icons/AssessmentOutlined';
import MonetizationOnIcon from '@material-ui/icons/MonetizationOnOutlined';
import StoreIcon from '@material-ui/icons/StoreOutlined';
import PeopleIcon from '@material-ui/icons/PeopleOutlined';
import BusinessIcon from '@material-ui/icons/BusinessOutlined';
import SettingsIcon from '@material-ui/icons/SettingsOutlined';
import SecurityIcon from '@material-ui/icons/SecurityOutlined';
import { ListItemLink } from './common';
import { useStyles } from './styles';
import { useLocation } from 'react-router-dom';

export const SuperAdminNav = () => {
  const classes = useStyles();
  const location = useLocation();
  const currentPath = location.pathname;

  const isActive = (path: string) => {
    if (path === '/') {
      return currentPath === '/';
    }
    return currentPath.startsWith(path);
  };

  return (
    <>
      <ListItemLink
        to={'/'}
        className={`${classes.navItem} ${isActive('/') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <DashboardIcon />
        </ListItemIcon>
        <ListItemText
          primary="Dashboard"
          className={classes.navItemText}
        />
      </ListItemLink>

      <ListItemLink
        to={'/reports'}
        className={`${classes.navItem} ${isActive('/reports') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <AssessmentIcon />
        </ListItemIcon>
        <ListItemText
          primary="Analytics"
          className={classes.navItemText}
        />
      </ListItemLink>

      <Box mt={2} mb={1}>
        <Divider />
      </Box>

      <Box className={classes.navSection}>
        <Typography className={classes.navSectionTitle}>
          Management
        </Typography>
      </Box>

      <ListItemLink
        to={'/tenants'}
        className={`${classes.navItem} ${isActive('/tenants') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <StoreIcon />
        </ListItemIcon>
        <ListItemText
          primary="Merchants"
          className={classes.navItemText}
        />
      </ListItemLink>

      <ListItemLink
        to={'/users'}
        className={`${classes.navItem} ${isActive('/users') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <PeopleIcon />
        </ListItemIcon>
        <ListItemText
          primary="Admins"
          className={classes.navItemText}
        />
      </ListItemLink>

      <ListItemLink
        to={'/organizations'}
        className={`${classes.navItem} ${isActive('/organizations') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <BusinessIcon />
        </ListItemIcon>
        <ListItemText
          primary="Organizations"
          className={classes.navItemText}
        />
      </ListItemLink>

      <Box mt={2} mb={1}>
        <Divider />
      </Box>

      <Box className={classes.navSection}>
        <Typography className={classes.navSectionTitle}>
          System
        </Typography>
      </Box>

      <ListItemLink
        to={'/billing'}
        className={`${classes.navItem} ${isActive('/billing') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <MonetizationOnIcon />
        </ListItemIcon>
        <ListItemText
          primary="Billing"
          className={classes.navItemText}
        />
      </ListItemLink>

      <ListItemLink
        to={'/settings'}
        className={`${classes.navItem} ${isActive('/settings') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <SettingsIcon />
        </ListItemIcon>
        <ListItemText
          primary="Settings"
          className={classes.navItemText}
        />
      </ListItemLink>

      <ListItemLink
        to={'/security'}
        className={`${classes.navItem} ${isActive('/security') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <SecurityIcon />
        </ListItemIcon>
        <ListItemText
          primary="Security"
          className={classes.navItemText}
        />
      </ListItemLink>
    </>
  );
};
