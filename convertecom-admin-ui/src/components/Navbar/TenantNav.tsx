import React from 'react';
import { Box, Divider, ListItemIcon, ListItemText, Typography } from '@material-ui/core';
import DashboardIcon from '@material-ui/icons/DashboardOutlined';
import AssessmentIcon from '@material-ui/icons/AssessmentOutlined';
import MonetizationOnIcon from '@material-ui/icons/MonetizationOnOutlined';
import HelpIcon from '@material-ui/icons/HelpOutlineOutlined';
import SettingsIcon from '@material-ui/icons/SettingsOutlined';
import ShoppingCartIcon from '@material-ui/icons/ShoppingCartOutlined';
import PeopleIcon from '@material-ui/icons/PeopleOutlined';
import StorefrontIcon from '@material-ui/icons/StorefrontOutlined';
import ExtensionIcon from '@material-ui/icons/ExtensionOutlined';
import { useStyles } from './styles';
import { ListItemLink } from './common';
import { useLocation } from 'react-router-dom';

export interface TenantNavProps {
  tenantSetupComplete: boolean;
}

export const TenantNav = ({ tenantSetupComplete }: TenantNavProps) => {
  const classes = useStyles();
  const location = useLocation();
  const currentPath = location.pathname;

  const isActive = (path: string) => {
    if (path === '/') {
      return currentPath === '/';
    }
    return currentPath.startsWith(path);
  };

  return (
    <>
      <ListItemLink
        to={'/'}
        disabled={!tenantSetupComplete}
        className={`${classes.navItem} ${isActive('/') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <DashboardIcon />
        </ListItemIcon>
        <ListItemText
          primary="Dashboard"
          className={classes.navItemText}
        />
      </ListItemLink>

      <ListItemLink
        to={'/reports'}
        disabled={!tenantSetupComplete}
        className={`${classes.navItem} ${isActive('/reports') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <AssessmentIcon />
        </ListItemIcon>
        <ListItemText
          primary="Analytics"
          className={classes.navItemText}
        />
      </ListItemLink>

      <ListItemLink
        to={'/customers'}
        disabled={!tenantSetupComplete}
        className={`${classes.navItem} ${isActive('/customers') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <PeopleIcon />
        </ListItemIcon>
        <ListItemText
          primary="Customers"
          className={classes.navItemText}
        />
      </ListItemLink>

      <ListItemLink
        to={'/orders'}
        disabled={!tenantSetupComplete}
        className={`${classes.navItem} ${isActive('/orders') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <ShoppingCartIcon />
        </ListItemIcon>
        <ListItemText
          primary="Orders"
          className={classes.navItemText}
        />
      </ListItemLink>

      <ListItemLink
        to={'/store'}
        disabled={!tenantSetupComplete}
        className={`${classes.navItem} ${isActive('/store') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <StorefrontIcon />
        </ListItemIcon>
        <ListItemText
          primary="Store"
          className={classes.navItemText}
        />
      </ListItemLink>

      <Box mt={2} mb={1}>
        <Divider />
      </Box>

      <Box className={classes.navSection}>
        <Typography className={classes.navSectionTitle}>
          Account
        </Typography>
      </Box>

      <ListItemLink
        to={'/billing'}
        disabled={!tenantSetupComplete}
        className={`${classes.navItem} ${isActive('/billing') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <MonetizationOnIcon />
        </ListItemIcon>
        <ListItemText
          primary="Billing"
          className={classes.navItemText}
        />
      </ListItemLink>

      <ListItemLink
        to={'/settings'}
        className={`${classes.navItem} ${isActive('/settings') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <SettingsIcon />
        </ListItemIcon>
        <ListItemText
          primary="Settings"
          className={classes.navItemText}
        />
      </ListItemLink>

      <ListItemLink
        to="/settings/integrations"
        className={`${classes.navItem} ${classes.navItemNested} ${isActive('/settings/integrations') ? 'active' : ''}`}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <ExtensionIcon />
        </ListItemIcon>
        <ListItemText
          primary="Integrations"
          className={classes.navItemText}
        />
      </ListItemLink>

      <ListItemLink
        to={'#'}
        onClick={() => window.open('https://convertecom.zendesk.com/hc/en-us')}
        disabled={!tenantSetupComplete}
        className={classes.navItem}
      >
        <ListItemIcon className={classes.navItemIcon}>
          <HelpIcon />
        </ListItemIcon>
        <ListItemText
          primary="Help Center"
          className={classes.navItemText}
        />
      </ListItemLink>
    </>
  );
};
