import {
  <PERSON><PERSON><PERSON><PERSON>,
  Ava<PERSON>,
  Badge,
  Box,
  Button,
  Di<PERSON>r,
  Drawer,
  IconButton,
  InputBase,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Toolbar,
  Tooltip,
  Typography,
} from '@material-ui/core';
import AccountCircle from '@material-ui/icons/AccountCircle';
import ChevronLeftIcon from '@material-ui/icons/ChevronLeft';
import MenuIcon from '@material-ui/icons/Menu';
import NotificationsIcon from '@material-ui/icons/NotificationsOutlined';
import SearchIcon from '@material-ui/icons/Search';
import HelpOutlineIcon from '@material-ui/icons/HelpOutline';
import clsx from 'clsx';
import React from 'react';
import { getUserId, isSuperAdmin, logout } from '../../services/AuthService';
import { useStyles } from './styles';
import { TenantNav } from './TenantNav';
import { SuperAdminNav } from './SuperAdminNav';
import { Link as RouterLink } from 'react-router-dom';

function ListItemLink(props: any) {
  return <ListItem button component={RouterLink} color={'inherit'} {...props} />;
}

export default (props: any) => {
  const classes = useStyles();

  const { tenantSetupComplete } = props;
  const [open, setOpen] = React.useState(
    window.sessionStorage.getItem('drawerClosed') !== 'true',
  );
  const [anchorEl, setAnchorEl] = React.useState(null);

  const isMenuOpen = Boolean(anchorEl);

  const handleDrawerOpen = () => {
    setOpen(true);
    window.sessionStorage.setItem('drawerClosed', 'false');
  };
  const handleDrawerClose = () => {
    setOpen(false);
    window.sessionStorage.setItem('drawerClosed', 'true');
  };
  const handleProfileMenuOpen = (e: any) => {
    setAnchorEl(e.currentTarget);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const userId = getUserId();

  return (
    <>
      <AppBar
        position="absolute"
        elevation={0}
        className={clsx(classes.appBar, open && classes.appBarShift)}
      >
        <Toolbar className={classes.toolbar}>
          <IconButton
            edge="start"
            aria-label="Open drawer"
            onClick={handleDrawerOpen}
            className={clsx(
              classes.menuButton,
              open && classes.menuButtonHidden,
            )}
          >
            <MenuIcon />
          </IconButton>

          <div className={classes.logoContainer}>
            <img src="/logo.png" alt="ConvertEcom" className={classes.logo} />
            <h2>Convert Ecom</h2>
          </div>

          <div className={classes.searchBar}>
            <div className={classes.searchIcon}>
              <SearchIcon />
            </div>
            <InputBase
              placeholder="Search…"
              classes={{
                root: classes.inputRoot,
                input: classes.inputInput,
              }}
              inputProps={{ 'aria-label': 'search' }}
            />
          </div>

          <div className={classes.headerActions}>
            <Tooltip title="Help">
              <IconButton color="inherit" onClick={() => window.open('https://convertecom.zendesk.com/hc/en-us')}>
                <HelpOutlineIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Notifications">
              <IconButton className={classes.notificationButton}>
                <Badge badgeContent={3} color="primary" classes={{ badge: classes.badge }}>
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title="Account">
              <IconButton
                edge="end"
                onClick={handleProfileMenuOpen}
                color="inherit"
              >
                <Avatar className={classes.profileAvatar} alt="User">
                  {userId ? userId.charAt(0).toUpperCase() : 'U'}
                </Avatar>
              </IconButton>
            </Tooltip>
          </div>
        </Toolbar>
      </AppBar>

      <Menu
        anchorEl={anchorEl}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        id={'account-menu'}
        keepMounted
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        open={isMenuOpen}
        onClose={handleMenuClose}
        PaperProps={{
          elevation: 2,
          style: {
            minWidth: 200,
            borderRadius: 8,
            marginTop: 8,
          },
        }}
      >
        {isSuperAdmin() && (
          <MenuItem component={RouterLink} to={`/users/detail/${userId}`}>
            <ListItemIcon>
              <AccountCircle fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Account" />
          </MenuItem>
        )}
        <MenuItem onClick={logout}>
          <ListItemIcon>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              <path d="M16 17L21 12L16 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              <path d="M21 12H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </ListItemIcon>
          <ListItemText primary="Log out" />
        </MenuItem>
      </Menu>

      <Drawer
        variant="permanent"
        classes={{
          paper: clsx(classes.drawerPaper, !open && classes.drawerPaperClose),
        }}
        open={open}
      >
        <div className={classes.toolbarIcon}>
          {open && (
            <div className={classes.drawerHeader}>
              {/* <img src="/logo.png" alt="ConvertEcom" className={classes.drawerLogo} /> */}
              <IconButton onClick={handleDrawerClose}>
                <ChevronLeftIcon />
              </IconButton>
            </div>
          )}
          {!open && (
            <IconButton aria-label="Open drawer" onClick={handleDrawerOpen}>
              <MenuIcon />
            </IconButton>
          )}
        </div>
        <Divider />

        {open && (
          <Box className={classes.navSection}>
            <Typography className={classes.navSectionTitle}>
              Main Navigation
            </Typography>
          </Box>
        )}

        <List>
          {!isSuperAdmin() && (
            <TenantNav tenantSetupComplete={tenantSetupComplete} />
          )}

          {isSuperAdmin() && <SuperAdminNav />}
        </List>

        {open && isSuperAdmin() && (
          <Box className={classes.profileSection}>
            <Button
              className={classes.profileButton}
              component={RouterLink}
              to={`/users/detail/${userId}`}
            >
              <Avatar className={classes.profileAvatar}>
                {userId ? userId.charAt(0).toUpperCase() : 'U'}
              </Avatar>
              <div className={classes.profileInfo}>
                <Typography className={classes.profileName}>
                  Admin User
                </Typography>
                <Typography className={classes.profileEmail}>
                  <EMAIL>
                </Typography>
              </div>
            </Button>
          </Box>
        )}
      </Drawer>
    </>
  );
};
