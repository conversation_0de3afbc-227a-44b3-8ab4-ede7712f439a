import React, { useContext } from 'react';
import { CircularProgress, Theme } from '@material-ui/core';
import { makeStyles, createStyles } from '@material-ui/styles';
import { fade } from '@material-ui/core/styles';
import { LoadingContext } from '../../contexts/loading.context';

export interface LoadingProgressProps {
  backgroundColor?: string;
}

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    progressOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      zIndex: 110,
      display: 'flex',
      width: '100%',
      height: '100%',
      backgroundColor: (props: LoadingProgressProps) => props.backgroundColor || fade(theme.palette.background.paper, 1),
    },
    progressContainer: {
      margin: 'auto',
    },
  }),
);

export default (props: LoadingProgressProps) => {
  const classes = useStyles(props);
  const { isLoading } = useContext(LoadingContext);

  return isLoading ? (
    <div className={classes.progressOverlay}>
      <div className={classes.progressContainer}>
        <CircularProgress />
      </div>
    </div>
  ) : null;
};
