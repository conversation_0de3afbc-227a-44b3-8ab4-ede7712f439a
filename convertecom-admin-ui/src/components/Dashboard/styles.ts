import { makeStyles } from '@material-ui/core/styles';

export const useStyles = makeStyles(theme => ({
  paper: {
    padding: theme.spacing(3),
    display: 'flex',
    overflow: 'hidden',
    flexDirection: 'column',
    borderRadius: 16,
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
    border: '1px solid rgba(0, 0, 0, 0.05)',
    backgroundColor: '#FFFFFF',
    transition: 'all 0.3s ease',
    '&:hover': {
      boxShadow: '0 8px 30px rgba(0, 0, 0, 0.08)',
      transform: 'translateY(-4px)',
    },
  },
  fixedHeight: {
    // height: 500
  },
  divider: {
    margin: theme.spacing(4, 0),
  },
  filterContainer: {
    marginBottom: theme.spacing(1),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  filterLabel: {
    marginRight: theme.spacing(2),
    fontWeight: 500,
    color: theme.palette.text.secondary,
  },
  statCard: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
    overflow: 'hidden',
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
    border: '1px solid rgba(0, 0, 0, 0.05)',
    transition: 'all 0.3s ease',
    '&:hover': {
      boxShadow: '0 8px 30px rgba(0, 0, 0, 0.08)',
      transform: 'translateY(-4px)',
    },
  },
  statCardAmount: {
    display: 'flex',
    alignItems: 'center',
    padding: theme.spacing(3),
    '& > :first-child': {
      marginRight: theme.spacing(2.5),
    },
  },
  statCardIcon: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    height: 60,
    borderRadius: '50%',
    backgroundColor: 'rgba(0, 178, 169, 0.1)',
    color: theme.palette.primary.main,
  },
  statCardContent: {
    flex: 1,
  },
  mainText: {
    fontSize: '0.875rem',
    fontWeight: 600,
    color: theme.palette.text.secondary,
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
    marginBottom: theme.spacing(0.5),
  },
  description: {
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    padding: theme.spacing(2),
    borderTop: `1px solid ${theme.palette.divider}`,
    marginTop: 'auto',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  selectField: {
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.shape.borderRadius,
    minWidth: 180,
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: theme.palette.divider,
      },
      '&:hover fieldset': {
        borderColor: theme.palette.primary.light,
      },
      '&.Mui-focused fieldset': {
        borderColor: theme.palette.primary.main,
      },
    },
  },
  dashboardHeader: {
    marginBottom: theme.spacing(4),
    display: 'flex',
    flexDirection: 'column',
    [theme.breakpoints.up('md')]: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
  },
  dashboardTitle: {
    color: theme.palette.text.primary,
    fontWeight: 700,
    fontSize: '1.75rem',
    marginBottom: theme.spacing(1),
    [theme.breakpoints.up('md')]: {
      marginBottom: 0,
    },
  },
  dashboardSubtitle: {
    color: theme.palette.text.secondary,
    marginBottom: theme.spacing(3),
    maxWidth: 600,
  },
  statValue: {
    fontWeight: 700,
    fontSize: '2rem',
    lineHeight: 1.2,
    color: theme.palette.text.primary,
    marginBottom: theme.spacing(0.5),
  },
  statTrend: {
    display: 'flex',
    alignItems: 'center',
    fontSize: '0.875rem',
    fontWeight: 500,
  },
  statTrendUp: {
    color: theme.palette.success.main,
  },
  statTrendDown: {
    color: theme.palette.error.main,
  },
  trendIcon: {
    fontSize: '1rem',
    marginRight: theme.spacing(0.5),
  },
  dashboardContent: {
    marginTop: theme.spacing(3),
  },
  summaryRow: {
    marginBottom: theme.spacing(4),
  },
  chartContainer: {
    height: 350,
    marginBottom: theme.spacing(4),
  },
  sectionTitle: {
    fontSize: '1.25rem',
    fontWeight: 600,
    marginBottom: theme.spacing(3),
    marginTop: theme.spacing(4),
  },
  dateRangeSelector: {
    display: 'flex',
    alignItems: 'center',
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.shape.borderRadius,
    padding: theme.spacing(0.5, 2),
    border: `1px solid ${theme.palette.divider}`,
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.02)',
    },
  },
  dateRangeText: {
    marginLeft: theme.spacing(1),
    fontWeight: 500,
  },
  tabsContainer: {
    marginBottom: theme.spacing(3),
  },
  tab: {
    minWidth: 120,
    fontWeight: 600,
    fontSize: '0.9375rem',
  },
  tabSelected: {
    color: theme.palette.primary.main,
  },
  tabIndicator: {
    backgroundColor: theme.palette.primary.main,
    height: 3,
    borderRadius: '3px 3px 0 0',
  },
}));
