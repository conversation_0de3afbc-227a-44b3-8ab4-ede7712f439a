import React from 'react';
import { Card, CardContent, Typography, Box, Tooltip, Fade } from '@material-ui/core';
import TrendingUpIcon from '@material-ui/icons/TrendingUp';
import TrendingDownIcon from '@material-ui/icons/TrendingDown';
import { useStyles } from './styles';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  description: string;
  trend?: {
    value: number;
    isUpward: boolean;
  };
}

export default (props: StatCardProps) => {
  const { title, value, icon, description, trend } = props;
  const classes = useStyles();

  return (
    <Tooltip
      title={description}
      placement="top"
      arrow
      TransitionComponent={Fade}
      TransitionProps={{ timeout: 600 }}
    >
      <Card className={classes.statCard}>
        <CardContent className={classes.statCardAmount}>
          <div className={classes.statCardIcon}>
            {icon}
          </div>
          <div className={classes.statCardContent}>
            <Typography className={classes.mainText}>
              {title}
            </Typography>
            <Typography variant="h4" component="h2" className={classes.statValue}>
              {value}
            </Typography>

            {trend && (
              <div className={`${classes.statTrend} ${trend.isUpward ? classes.statTrendUp : classes.statTrendDown}`}>
                {trend.isUpward ? (
                  <TrendingUpIcon className={classes.trendIcon} />
                ) : (
                  <TrendingDownIcon className={classes.trendIcon} />
                )}
                <span>{trend.isUpward ? '+' : ''}{trend.value}%</span>
              </div>
            )}
          </div>
        </CardContent>
        <div className={classes.description}>
          <Typography variant="body2" color="textSecondary">
            {description}
          </Typography>
        </div>
      </Card>
    </Tooltip>
  );
}
