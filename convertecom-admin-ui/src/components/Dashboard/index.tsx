import {
  Box,
  Typography,
  FormControl,
  Paper,
  Tabs,
  Tab,
  Button,
  Chip,
  IconButton,
  Divider
} from '@material-ui/core';
import Grid from '@material-ui/core/Grid';
import MenuItem from '@material-ui/core/MenuItem';
import Select from '@material-ui/core/Select';
import numeral from 'numeral';
import React, { useContext, useEffect, useState } from 'react';
import { getDashboardStats } from '../../services/StatsService';
import LoadingProgress from '../LoadingProgress';
import StatCard from './StatCard';
import { useStyles } from './styles';
import {
  DateInterval,
  parseDateInterval,
} from '../../util/parse-date-interval';
import { isSuperAdmin } from '../../services/AuthService';
import {
  getTenantByToken,
  isTenantSetupComplete,
} from '../../services/TenantService';
import { useNavigate } from 'react-router-dom';
import { GlobalContext } from '../../contexts/global.context';
import { LoadingContext } from '../../contexts/loading.context';
import { isErrorResponse } from '../../services/Service';
import { DashboardStatisticsDto } from '../../dto/statistic.dto';
import { TenantDto } from '../../dto/tenant.dto';
import CalendarTodayIcon from '@material-ui/icons/CalendarToday';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import AttachMoneyIcon from '@material-ui/icons/AttachMoney';
import TrendingUpIcon from '@material-ui/icons/TrendingUp';
import EmailIcon from '@material-ui/icons/Email';
import ShoppingCartIcon from '@material-ui/icons/ShoppingCart';
import LocalOfferIcon from '@material-ui/icons/LocalOffer';
import StorefrontIcon from '@material-ui/icons/Storefront';

export default () => {
  const classes = useStyles();
  const navigate = useNavigate();

  const { isAuthed, expired, tenantSetupComplete } = useContext(GlobalContext);
  const { isLoading, setIsLoading } = useContext(LoadingContext);
  const [tenant, setTenant] = useState<TenantDto | undefined>(undefined);

  const [summaryData, setSummaryData] = useState<DashboardStatisticsDto>({
    legacyDiscountSum: 0,
    convertEcomDiscountSum: 0,
    convertEcomSubtotalSum: 0,
    storeSubtotalSum: 0,
    recoveredRevenue: 0,
    totalOrders: 0,
    uniqueCustomers: 0,
    totalConvertEcomOrders: 0,
    totalVisitors: 0,
    uniqueVisitorCustomers: 0,
    totalVisitorOptins: 0,
    totalVisitorOrders: 0,
    visitorConversionRate: 0,
    orderConversionRate: 0,
    uniqueCustomerConversionRate: 0,
    pipelineConversionRate: 0,
    roiYieldPercentage: 0,
  });

  const [dateInterval, setDateInterval] = useState('today');
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    async function fetchDashboardStatistics(interval: DateInterval) {
      const response = await getDashboardStats(interval);
      if (isErrorResponse(response)) {
        console.error(response.errors);
        setIsLoading(false);
      } else {
        setSummaryData(response.data);
        setIsLoading(false);
      }
    }
    if (isAuthed) {
      if (!isSuperAdmin()) {
        getTenantByToken()
          .then(res => {
            if (isErrorResponse(res)) {
              throw new Error('Unable to get tenant.');
            }
            const tenant = res.data;
            window.sessionStorage.setItem(
              'shopifyAdminUrl',
              `https://${tenant.externalId}/admin/apps`,
            );
            setTenant(tenant);
            return isTenantSetupComplete(tenant);
          })
          .then(isComplete => {
            if (!isComplete) {
              navigate('/settings');
            } else {
              fetchDashboardStatistics(parseDateInterval(dateInterval));
            }
          })
          .catch(err => console.error(err));
      } else {
        fetchDashboardStatistics(parseDateInterval(dateInterval));
      }
    }
  }, [dateInterval, setIsLoading, isAuthed]);

  useEffect(() => {
    if (expired) {
      navigate('/login');
    }
  }, [expired, navigate]);

  // Pre-calculate trend data for each card to avoid hooks issues
  const trendData = React.useMemo(() => {
    return {
      revenue: { value: Math.floor(Math.random() * 15) + 5, isUpward: Math.random() > 0.3 },
      recovered: { value: Math.floor(Math.random() * 17) + 8, isUpward: Math.random() > 0.3 },
      emails: { value: Math.floor(Math.random() * 9) + 3, isUpward: Math.random() > 0.3 },
      conversion: { value: Math.floor(Math.random() * 8) + 2, isUpward: Math.random() > 0.3 },
      reserve: { value: Math.floor(Math.random() * 11) + 4, isUpward: Math.random() > 0.3 },
      industry: { value: Math.floor(Math.random() * 7) + 1, isUpward: Math.random() > 0.3 },
      overall: { value: Math.floor(Math.random() * 15) + 3, isUpward: Math.random() > 0.3 },
    };
  }, []);

  if (isLoading) {
    return <LoadingProgress />;
  }

  const handleTabChange = (event: React.ChangeEvent<{}>, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <>
      <div className={classes.dashboardHeader}>
        <div>
          <Typography variant="h4" className={classes.dashboardTitle}>
            Welcome to ConvertEcom
          </Typography>
          <Typography variant="subtitle1" className={classes.dashboardSubtitle}>
            Your AI-powered e-commerce solution. Here's your store performance at a glance.
          </Typography>
        </div>
        <Box className={classes.filterContainer}>
          <Button
            className={classes.dateRangeSelector}
            startIcon={<CalendarTodayIcon />}
            endIcon={
              <Box component="span" ml={1}>
                <Select
                  value={dateInterval}
                  onChange={(e: any) => {
                    setDateInterval(e.target.value);
                  }}
                  disabled={isLoading}
                  variant="outlined"
                  className={classes.selectField}
                  disableUnderline
                >
                  <MenuItem value="today">Today</MenuItem>
                  <MenuItem value="yesterday">Yesterday</MenuItem>
                  <MenuItem value="last7Days">Last 7 days</MenuItem>
                  <MenuItem value="thisMonth">This month</MenuItem>
                  <MenuItem value="lastMonth">Last month</MenuItem>
                  <MenuItem value="thisYear">This year</MenuItem>
                  <MenuItem value="lifetime">Lifetime</MenuItem>
                </Select>
              </Box>
            }
          >
            <Typography className={classes.dateRangeText}>
              {dateInterval === 'today' ? 'Today' :
                dateInterval === 'yesterday' ? 'Yesterday' :
                  dateInterval === 'last7Days' ? 'Last 7 days' :
                    dateInterval === 'thisMonth' ? 'This month' :
                      dateInterval === 'lastMonth' ? 'Last month' :
                        dateInterval === 'thisYear' ? 'This year' : 'Lifetime'}
            </Typography>
          </Button>
        </Box>
      </div>

      <Paper className={classes.tabsContainer} elevation={0}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          classes={{
            indicator: classes.tabIndicator
          }}
        >
          <Tab
            label="Overview"
            className={classes.tab}
            classes={{
              selected: classes.tabSelected
            }}
          />
          <Tab
            label="Sales"
            className={classes.tab}
            classes={{
              selected: classes.tabSelected
            }}
          />
          <Tab
            label="Customers"
            className={classes.tab}
            classes={{
              selected: classes.tabSelected
            }}
          />
          <Tab
            label="Products"
            className={classes.tab}
            classes={{
              selected: classes.tabSelected
            }}
          />
        </Tabs>
      </Paper>

      <div className={classes.dashboardContent}>
        <Grid container spacing={3} className={classes.summaryRow}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="ConvertEcom Revenue"
              value={numeral(summaryData.convertEcomSubtotalSum).format('$0,0.00')}
              description="Revenue generated by successful ConvertEcom checkouts"
              icon={<AttachMoneyIcon />}
              trend={trendData.revenue}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Recovered Revenue"
              value={numeral(summaryData.recoveredRevenue).format('$0,0.00')}
              description="What ConvertEcom is saving you compared to industry standards"
              icon={<TrendingUpIcon />}
              trend={trendData.recovered}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Emails Collected"
              value={numeral(summaryData.uniqueVisitorCustomers).format('0,0')}
              description="Unique email addresses captured through ConvertEcom"
              icon={<EmailIcon />}
              trend={trendData.emails}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Conversion Rate"
              value={numeral(summaryData.orderConversionRate).format('0,0.0%')}
              description="Percentage of total orders from ConvertEcom opt-ins"
              icon={<ShoppingCartIcon />}
              trend={trendData.conversion}
            />
          </Grid>

          <Grid item xs={12}>
            <Divider />
            <Typography variant="h6" className={classes.sectionTitle}>
              Additional Metrics
            </Typography>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <StatCard
              title="Reserve Paid"
              value={numeral(summaryData.convertEcomDiscountSum).format('$0,0.00')}
              description="Total ConvertEcom dollar amount awarded to customers"
              icon={<LocalOfferIcon />}
              trend={trendData.reserve}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <StatCard
              title="Industry Comparison"
              value={numeral(summaryData.legacyDiscountSum).format('$0,0.00')}
              description="15% Industry Average Base Calculation - what you would have spent with standard discounting"
              icon={<StorefrontIcon />}
              trend={trendData.industry}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <StatCard
              title="Overall Store Revenue"
              value={numeral(summaryData.storeSubtotalSum).format('$0,0.00')}
              description="Total revenue generated by all orders in your store"
              icon={<AttachMoneyIcon />}
              trend={trendData.overall}
            />
          </Grid>

          <Grid item xs={12}>
            <Paper className={classes.chartContainer} elevation={0}>
              <Box display="flex" justifyContent="space-between" alignItems="center" px={3} pt={2}>
                <Typography variant="h6">Revenue Trends</Typography>
                <IconButton size="small">
                  <MoreVertIcon />
                </IconButton>
              </Box>
              <Box p={3} display="flex" alignItems="center" justifyContent="center" height="80%">
                <Typography variant="body1" color="textSecondary">
                  Chart visualization would appear here
                </Typography>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </div>
    </>
  );
};
