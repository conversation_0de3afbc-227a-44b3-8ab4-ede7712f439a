import { ApexOptions } from 'apexcharts';
import Chart from 'react-apexcharts';

export default (props: any) => {
  const { series, yaxis, showMarkers } = props;

  const options = {
    yaxis,
    markers: {
      size: showMarkers ? 6 : null
    },
    chart: {
      stacked: true,
      zoom: {
        enabled: false
      }
    },
    fill: {
      type: 'solid'
    },
    dataLabels: {
      enabled: false
    },
    stroke: {
      curve: 'straight'
    },
  } as ApexOptions;

  return (
    <Chart
      options={options}
      series={series}
      type='area'
      height='300'
    />
  );
}
