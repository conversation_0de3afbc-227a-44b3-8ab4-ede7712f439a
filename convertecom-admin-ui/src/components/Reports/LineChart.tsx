import { ApexOptions } from 'apexcharts';
import React from 'react';
import Chart from 'react-apexcharts';

export default (props: any) => {
  const { series, yaxis, showMarkers } = props;

  const options = {
    yaxis,
    markers: {
      size: showMarkers ? 6 : null
    },
    chart: {
      chart: {
          zoom: {
            enabled: false
          }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        curve: 'smooth'
      },
      grid: {
        row: {
          colors: ['#f3f3f3', 'transparent'],
          opacity: 0.5
        },
      },
    }
  } as ApexOptions;

  return (
    <Chart
      options={options}
      series={series}
      type='line'
      height='300'
    />
  );
}
