import { makeStyles } from '@material-ui/core/styles';

export const useStyles = makeStyles(theme => ({
  paper: {
    padding: theme.spacing(3),
    display: 'flex',
    overflow: 'hidden',
    flexDirection: 'column',
    borderRadius: 16,
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
    border: '1px solid rgba(0, 0, 0, 0.05)',
    backgroundColor: '#FFFFFF',
    transition: 'all 0.3s ease',
    '&:hover': {
      boxShadow: '0 8px 30px rgba(0, 0, 0, 0.08)',
      transform: 'translateY(-4px)',
    },
  },
  fixedHeight: {
    // height: 500
  },
  divider: {
    margin: theme.spacing(4, 0),
  },
  filterContainer: {
    marginBottom: theme.spacing(3),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  filterLabel: {
    marginRight: theme.spacing(2),
    fontWeight: 500,
    color: theme.palette.text.secondary,
  },
  statCardLeft: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing(2),
    fontSize: '48px',
    color: theme.palette.primary.main,
  },
  statCardRight: {
    display: 'flex',
    flexDirection: 'column',
  },
  selectField: {
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.shape.borderRadius,
    minWidth: 180,
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: theme.palette.divider,
      },
      '&:hover fieldset': {
        borderColor: theme.palette.primary.light,
      },
      '&.Mui-focused fieldset': {
        borderColor: theme.palette.primary.main,
      },
    },
  },
  reportsHeader: {
    marginBottom: theme.spacing(4),
  },
  reportsTitle: {
    fontWeight: 700,
    fontSize: '1.75rem',
    marginBottom: theme.spacing(1),
  },
  reportsSubtitle: {
    color: theme.palette.text.secondary,
    maxWidth: 800,
  },
  reportSection: {
    marginBottom: theme.spacing(4),
  },
  reportSectionTitle: {
    fontWeight: 600,
    fontSize: '1.25rem',
    marginBottom: theme.spacing(1),
    color: theme.palette.primary.main,
  },
  reportSectionDescription: {
    color: theme.palette.text.secondary,
    marginBottom: theme.spacing(2),
  },
  tabsContainer: {
    marginBottom: theme.spacing(3),
  },
  tab: {
    minWidth: 120,
    fontWeight: 600,
    fontSize: '0.9375rem',
  },
  tabSelected: {
    color: theme.palette.primary.main,
  },
  tabIndicator: {
    backgroundColor: theme.palette.primary.main,
    height: 3,
    borderRadius: '3px 3px 0 0',
  },
  chartTitle: {
    fontWeight: 600,
    fontSize: '1.125rem',
    marginBottom: theme.spacing(2),
  },
  dateRangeSelector: {
    display: 'flex',
    alignItems: 'center',
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.shape.borderRadius,
    padding: theme.spacing(0.5, 2),
    border: `1px solid ${theme.palette.divider}`,
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.02)',
    },
  },
  dateRangeText: {
    marginLeft: theme.spacing(1),
    fontWeight: 500,
  },
}));
