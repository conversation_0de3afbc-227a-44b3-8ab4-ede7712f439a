import React, { useState, useEffect, useContext } from 'react';
import clsx from 'clsx';
import { useStyles } from './styles';
import { getReportStats } from '../../services/StatsService';
import { parseDateInterval } from '../../util/parse-date-interval';
import LoadingProgress from '../LoadingProgress';
import {
  Grid,
  Typography,
  Paper,
  Box,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Button,
  Divider,
  IconButton
} from '@material-ui/core';
import AreaChart from './AreaChart';
import BarChart from './BarChart';
import { LoadingContext } from '../../contexts/loading.context';
import CalendarTodayIcon from '@material-ui/icons/CalendarToday';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import GetAppIcon from '@material-ui/icons/GetApp';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';

export default () => {
  const classes = useStyles();
  const fixedHeightPaper = clsx(classes.paper, classes.fixedHeight);

  const { isLoading, setIsLoading } = useContext(LoadingContext);

  const [chartData, setChartData] = useState([]);
  const [dateInterval, setDateInterval] = useState('today');
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event: React.ChangeEvent<{}>, newValue: number) => {
    setActiveTab(newValue);
  };

  useEffect(() => {
    setIsLoading(true);

    getReportStats(parseDateInterval(dateInterval))
      .then(result => {
        setChartData(result.data);
        setIsLoading(false);
      })
      .catch(err => {
        console.error(err);
        // TODO show error
      });
  }, [dateInterval, setIsLoading]);

  return (
    <>
      {isLoading && <LoadingProgress />}

      <div className={classes.reportsHeader}>
        <Typography variant="h4" className={classes.reportsTitle}>
          Analytics
        </Typography>
        <Typography variant="subtitle1" className={classes.reportsSubtitle}>
          Track your store's performance metrics and conversion data
        </Typography>
      </div>

      <Paper className={classes.tabsContainer} elevation={0}>
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange}
          classes={{
            indicator: classes.tabIndicator
          }}
        >
          <Tab 
            label="Overview" 
            className={classes.tab} 
            classes={{
              selected: classes.tabSelected
            }}
          />
          <Tab 
            label="Orders" 
            className={classes.tab}
            classes={{
              selected: classes.tabSelected
            }}
          />
          <Tab 
            label="Revenue" 
            className={classes.tab}
            classes={{
              selected: classes.tabSelected
            }}
          />
          <Tab 
            label="Conversion" 
            className={classes.tab}
            classes={{
              selected: classes.tabSelected
            }}
          />
        </Tabs>
      </Paper>

      <Box className={classes.filterContainer}>
        <Typography variant="h6" component="h2">
          {activeTab === 0 ? 'Performance Overview' : 
           activeTab === 1 ? 'Order Analytics' : 
           activeTab === 2 ? 'Revenue Analytics' : 'Conversion Analytics'}
        </Typography>
        
        <Box display="flex" alignItems="center">
          <Button 
            className={classes.dateRangeSelector}
            startIcon={<CalendarTodayIcon />}
          >
            <Typography className={classes.dateRangeText}>
              {dateInterval === 'today' ? 'Today' : 
               dateInterval === 'yesterday' ? 'Yesterday' :
               dateInterval === 'last7Days' ? 'Last 7 days' :
               dateInterval === 'thisMonth' ? 'This month' :
               dateInterval === 'lastMonth' ? 'Last month' :
               dateInterval === 'thisYear' ? 'This year' : 'Lifetime'}
            </Typography>
            <Select
              value={dateInterval}
              onChange={(e: any) => {
                setDateInterval(e.target.value);
              }}
              disabled={isLoading}
              variant="outlined"
              className={classes.selectField}
              disableUnderline
            >
              <MenuItem value="today">Today</MenuItem>
              <MenuItem value="yesterday">Yesterday</MenuItem>
              <MenuItem value="last7Days">Last 7 days</MenuItem>
              <MenuItem value="thisMonth">This month</MenuItem>
              <MenuItem value="lastMonth">Last month</MenuItem>
              <MenuItem value="thisYear">This year</MenuItem>
              <MenuItem value="lifetime">Lifetime</MenuItem>
            </Select>
          </Button>
          
          <IconButton size="small" style={{ marginLeft: 8 }}>
            <GetAppIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Overview Tab */}
      {activeTab === 0 && (
        <>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Paper className={classes.paper}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography className={classes.chartTitle}>
                    Orders Overview
                  </Typography>
                  <IconButton size="small">
                    <MoreVertIcon />
                  </IconButton>
                </Box>
                <Box p={1}>
                  <Typography variant="body2" color="textSecondary" paragraph>
                    Comparison of the number of your orders that had a ConvertEcom discount attached.
                  </Typography>
                  <AreaChart
                    showMarkers={true}
                    yaxis={{ labels: { formatter: (v: string) => parseInt(v) } }}
                    series={[
                      {
                        name: 'ConvertEcom Redeemed',
                        data: chartData.filter((o: any) => o.category === 'zingOrderCount')
                          .map((o: any) => {
                            return { x: o.key, y: o.value };
                          })
                      },
                      {
                        name: 'Gross Orders',
                        data: chartData.filter((o: any) => o.category === 'orderCount')
                          .map((o: any) => {
                            return { x: o.key, y: o.value };
                          })
                      }
                    ]}
                  />
                </Box>
              </Paper>
            </Grid>
          </Grid>

          <Grid container spacing={3} style={{ marginTop: '16px' }}>
            <Grid item xs={12}>
              <Paper className={classes.paper}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography className={classes.chartTitle}>
                    Discount Comparison
                  </Typography>
                  <IconButton size="small">
                    <MoreVertIcon />
                  </IconButton>
                </Box>
                <Box p={1}>
                  <Typography variant="body2" color="textSecondary" paragraph>
                    Comparison of the various discounts in your store. Successful stores seek to reduce non-ConvertEcom discounts over time.
                  </Typography>
                  <AreaChart
                    showMarkers={true}
                    series={[
                      {
                        name: 'ConvertEcom Discounts',
                        data: chartData.filter((o: any) => o.category === 'zingDiscountSum')
                          .map((o: any) => {
                            return { x: o.key, y: o.value };
                          })
                      },
                      {
                        name: 'Legacy Discounts',
                        data: chartData.filter((o: any) => o.category === 'legacyDiscountSum')
                          .map((o: any) => {
                            return { x: o.key, y: o.value };
                          })
                      }
                    ]}
                  />
                </Box>
              </Paper>
            </Grid>
          </Grid>

          <Grid container spacing={3} style={{ marginTop: '16px' }}>
            <Grid item xs={12}>
              <Paper className={classes.paper}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography className={classes.chartTitle}>
                    Recovered Revenue
                  </Typography>
                  <IconButton size="small">
                    <MoreVertIcon />
                  </IconButton>
                </Box>
                <Box p={1}>
                  <Typography variant="body2" color="textSecondary" paragraph>
                    Demonstration of the amount of revenue recovered by using the ConvertEcom platform vs traditional discounts.
                  </Typography>
                  <AreaChart
                    showMarkers={true}
                    series={[
                      {
                        name: 'Recovered Revenue',
                        data: chartData.filter((o: any) => o.category === 'recoveredRevenue')
                          .map((o: any) => {
                            return { x: o.key, y: o.value };
                          })
                      }
                    ]}
                  />
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </>
      )}
      
      {/* Orders Tab */}
      {activeTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper className={classes.paper}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography className={classes.chartTitle}>
                  Order Metrics
                </Typography>
                <IconButton size="small">
                  <MoreVertIcon />
                </IconButton>
              </Box>
              <Box p={1}>
                <Typography variant="body2" color="textSecondary" paragraph>
                  Detailed breakdown of orders with ConvertEcom discounts vs. total orders
                </Typography>
                <AreaChart
                  showMarkers={true}
                  yaxis={{ labels: { formatter: (v: string) => parseInt(v) } }}
                  series={[
                    {
                      name: 'ConvertEcom Orders',
                      data: chartData.filter((o: any) => o.category === 'zingOrderCount')
                        .map((o: any) => {
                          return { x: o.key, y: o.value };
                        })
                    },
                    {
                      name: 'Total Orders',
                      data: chartData.filter((o: any) => o.category === 'orderCount')
                        .map((o: any) => {
                          return { x: o.key, y: o.value };
                        })
                    }
                  ]}
                />
              </Box>
            </Paper>
          </Grid>
        </Grid>
      )}
      
      {/* Revenue Tab */}
      {activeTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper className={classes.paper}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography className={classes.chartTitle}>
                  Revenue Analytics
                </Typography>
                <IconButton size="small">
                  <MoreVertIcon />
                </IconButton>
              </Box>
              <Box p={1}>
                <Typography variant="body2" color="textSecondary" paragraph>
                  Detailed breakdown of revenue metrics and recovered revenue through ConvertEcom
                </Typography>
                <AreaChart
                  showMarkers={true}
                  series={[
                    {
                      name: 'Recovered Revenue',
                      data: chartData.filter((o: any) => o.category === 'recoveredRevenue')
                        .map((o: any) => {
                          return { x: o.key, y: o.value };
                        })
                    }
                  ]}
                />
              </Box>
            </Paper>
          </Grid>
        </Grid>
      )}
      
      {/* Conversion Tab */}
      {activeTab === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper className={classes.paper}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography className={classes.chartTitle}>
                  Conversion Funnel
                </Typography>
                <IconButton size="small">
                  <MoreVertIcon />
                </IconButton>
              </Box>
              <Box p={1}>
                <Typography variant="body2" color="textSecondary" paragraph>
                  A stacked filter view of your store's performance in converting site
                  visitors into orders using the ConvertEcom platform. Over time, you should
                  see Opted-in Orders stabilize as you move away from traditional
                  discounts.
                </Typography>
                <BarChart
                  series={[
                    {
                      name: 'Total Visitors',
                      data: chartData.filter((o: any) => o.category === 'totalVisitors')
                        .map((o: any) => {
                          return { x: o.key, y: o.value };
                        }),
                    },
                    {
                      name: 'Total Opt-Ins',
                      data: chartData.filter((o: any) => o.category === 'totalOptins')
                        .map((o: any) => {
                          return { x: o.key, y: o.value };
                        }),
                    },
                    {
                      name: 'Total Orders',
                      data: chartData.filter((o: any) => o.category === 'totalOrders')
                        .map((o: any) => {
                          return { x: o.key, y: o.value };
                        }),
                    },
                    {
                      name: 'Unique Opt-Ins',
                      data: chartData.filter((o: any) => o.category === 'uniqueOptins')
                        .map((o: any) => {
                          return { x: o.key, y: o.value };
                        }),
                    },
                    {
                      name: 'Unique Customers',
                      data: chartData.filter((o: any) => o.category === 'uniqueCustomers')
                        .map((o: any) => {
                          return { x: o.key, y: o.value };
                        }),
                    },
                    {
                      name: 'Opted-in Orders',
                      data: chartData.filter((o: any) => o.category === 'optedInOrders')
                        .map((o: any) => {
                          return { x: o.key, y: o.value };
                        }),
                    },
                  ]}
                />
              </Box>
            </Paper>
          </Grid>
        </Grid>
      )}
    </>
  );
};
