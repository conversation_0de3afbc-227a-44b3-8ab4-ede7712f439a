import React from 'react';
import { Grid, makeStyles } from '@material-ui/core';

const useStyles = makeStyles(theme => ({
  menuHeader: {
    paddingBottom: theme.spacing(0),
  },
}));

export default (props: any) => {
  const classes = useStyles();

  return (
    <Grid
      container
      direction="row-reverse"
      justify="flex-start"
      alignItems="flex-start"
      className={classes.menuHeader}
      {...props}
    />
  );
};
