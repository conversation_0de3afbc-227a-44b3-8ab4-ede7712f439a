import React from 'react';
import { Typography } from '@material-ui/core';
import LoadingProgress from '../LoadingProgress';

export default (props: any) => {
  const { isLoading } = props;

  const shopifyAdminUrl = window.sessionStorage.getItem('shopifyAdminUrl');

  if (isLoading) {
    return <LoadingProgress />
  }

  return (
    <>
      <Typography component="h1" variant="h2">
        Session Expired
      </Typography>
      {shopifyAdminUrl ?
      (
        <p>Your session has expired. Please log back in via your <a href={shopifyAdminUrl}>Shopify admin panel</a>.</p>
      ) : (
        <p>Your session has expired. Please log back in via your Shopify admin panel.</p>
      )}
    </>
  );
};
