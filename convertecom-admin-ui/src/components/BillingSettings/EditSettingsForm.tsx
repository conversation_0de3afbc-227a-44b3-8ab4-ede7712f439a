import * as React from 'react';
import { useContext, useEffect, useState } from 'react';
import LockIcon from '@material-ui/icons/Lock';
import LockOpenIcon from '@material-ui/icons/LockOpen';
import {
  Button,
  createStyles,
  Grid,
  Paper,
  Theme,
  Typography,
} from '@material-ui/core';
import {
  fetchGlobalSubscriptionSettings,
  updateBillingSettings,
} from 'services/BillingSettingsService';
import { RequestState } from '../../interfaces/request-state.enum';
import LoadingProgress from '../LoadingProgress';
import { FormControl, hfErrors } from '../Form';
import makeStyles from '@material-ui/core/styles/makeStyles';
import { BillingSettings } from './interfaces/billing-settings.interface';
import Box from '@material-ui/core/Box';
import IconButton from '@material-ui/core/IconButton';
import { FieldValues, useForm, SubmitHandler } from 'react-hook-form';
import { isErrorResult } from '../../util/requests';
import { LoadingContext } from '../../contexts/loading.context';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    paper: {
      padding: theme.spacing(1, 2),
    },
    container: {
      padding: theme.spacing(4),
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(2),
      },
    },
    formFooterButton: {
      marginTop: theme.spacing(2),
      marginRight: theme.spacing(1),
    },
  }),
);

export const EditSettingsForm = () => {
  const { isLoading, setIsLoading } = useContext(LoadingContext);
  const classes = useStyles();
  const [billingSettings, setBillingSettings] = useState<
    BillingSettings | undefined
  >(undefined);
  const [requestState, setRequestState] = useState<RequestState>(
    RequestState.NONE,
  );
  const [requestErrors, setRequestErrors] = useState<string[]>([]);
  const [editDisabled, setEditDisabled] = useState(true);
  const { handleSubmit, register, formState: { errors }, setValue } = useForm();

  const onSubmit: SubmitHandler<FieldValues> = async (values) => {
    if (typeof billingSettings === 'undefined') {
      alert('Billing settings were not loaded!');
      return;
    }
    const trialDays = parseInt(values.trialDays, 10);

    if (trialDays === billingSettings.value.trialDays) {
      return;
    }
    setEditDisabled(true);

    const res = await updateBillingSettings(billingSettings.id, {
      value: { trialDays },
    });
    if (isErrorResult(res)) {
      setRequestErrors(res.errors[0].messages);
      setRequestState(RequestState.ERROR);
      setEditDisabled(false);
      return;
    }
    setRequestState(RequestState.NONE);
    setBillingSettings({
      ...billingSettings,
      value: { trialDays },
    });
  }

  useEffect(() => {
    setRequestState(RequestState.LOADING);
    fetchGlobalSubscriptionSettings().then(res => {
      if (isErrorResult(res)) {
        setRequestErrors(res.errors[0].messages);
        setRequestState(RequestState.ERROR);
      } else {
        setBillingSettings(res.result);
        setValue('trialDays', res.result.value.trialDays);
        setRequestState(RequestState.NONE);
      }
      setIsLoading(false);
    });
  }, [setIsLoading, setValue]);

  function toggleEdit() {
    setEditDisabled(!editDisabled);
  }

  function cancelEdit() {
    setValue(
      'trialDays',
      typeof billingSettings === 'undefined' ? 0 : billingSettings.value,
    );
    setEditDisabled(true);
  }

  function validateTrialDays(trialDays: string) {
    if (typeof trialDays === 'undefined' || trialDays === '') {
      return 'Please enter a value.';
    }
    const asNumber = parseInt(trialDays, 10);
    if (asNumber < 0) {
      return 'Enter zero more more days.';
    }
    return true;
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12} md={3}>
        <Typography component="h6" variant="h6" color="primary" gutterBottom>
          Billing Settings
        </Typography>

        <Typography component="p" variant="body2" gutterBottom>
          View and edit billing settings. These settings will be applied to{' '}
          <strong>new tenants</strong> only.
        </Typography>
        <Typography component="p" variant="body2">
          Click the lock icon to edit.
        </Typography>
      </Grid>

      <Grid item xs={12} md={9}>
        <Paper className={classes.paper}>
          {(requestState === RequestState.LOADING || isLoading) && (
            <LoadingProgress />
          )}
          <form
            noValidate
            autoComplete="off"
            onSubmit={handleSubmit(onSubmit)}
            aria-disabled={editDisabled}
          >
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="flex-end"
            >
              <Typography variant="h5" component="h3">
                Global Subscription Settings
              </Typography>
              <IconButton
                aria-label={`${editDisabled ? 'Unlock' : 'Lock'
                  } Global Subscription Settings`}
                type="button"
                onClick={toggleEdit}
              >
                {editDisabled ? <LockIcon /> : <LockOpenIcon />}
              </IconButton>
            </Box>
            {requestState === RequestState.ERROR && (
              <Typography variant="subtitle1" color="error">
                {requestErrors[0]}
              </Typography>
            )}
            <FormControl
              name="trialDays"
              label="Trial Length (days)"
              type="number"
              disabled={editDisabled}
              errors={hfErrors('trialDays', errors)}
              {...register("trialDays", { validate: validateTrialDays })}
            />
            <div style={{ marginBottom: '20px' }}>
              <Button
                variant="contained"
                className={classes.formFooterButton}
                disabled={editDisabled}
                onClick={cancelEdit}
                type="button"
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                color="primary"
                type="submit"
                className={classes.formFooterButton}
                disabled={editDisabled}
              >
                Save changes
              </Button>
            </div>
          </form>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default EditSettingsForm;
