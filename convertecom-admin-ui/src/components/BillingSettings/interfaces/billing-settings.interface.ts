import { BillingSettingScope } from '../enums/billing-setting-scope.enum';
import { BillingSettingType } from '../enums/billing-setting-type.enum';
import { BillingSettingsSubscription } from '../../../services/BillingSettingsService';

export type BillingSettingsValue = BillingSettingsSubscription;

export interface BillingSettings {
  id: string;
  scope: BillingSettingScope;
  type: BillingSettingType;
  value: BillingSettingsValue;
  createdAt: Date;
  updatedAt: Date;
}
