import { Grid, TableCell } from '@material-ui/core';
import * as React from 'react';
import { forwardRef, useState } from 'react';
import DateFnsUtils from '@date-io/date-fns';
import DateFnsAdapter from '@date-io/date-fns';
import {
  DatePicker,
  MuiPickersUtilsProvider,
} from '@material-ui/pickers';
import MUIDataTable, {
  MUIDataTableColumn,
  MUIDataTableOptions,
} from 'mui-datatables';
import { ParsableDate } from '@material-ui/pickers/constants/prop-types';
import { MaterialUiPickersDate } from '@material-ui/pickers/typings/date';

const dateFns = new DateFnsAdapter();

export interface BillingSummaryProps {
  startDate: Date;
  endDate: Date;

  onSelect(type: string, date: Date): void;
}

export interface BillingSummaryDatePickerProps {
  label: string;
  selectedDate: MaterialUiPickersDate;
  onSelectedDateChange: (date: MaterialUiPickersDate) => void;
  ref: React.Ref<HTMLDivElement>;
  minDate?: ParsableDate;
  maxDate?: ParsableDate;
}

const BillingSummaryDatePicker = forwardRef(function BillingSummaryDatePicker(
  {
    label,
    minDate,
    maxDate,
    selectedDate,
    onSelectedDateChange,
  }: BillingSummaryDatePickerProps,
  ref: React.Ref<HTMLDivElement>,
) {
  const [pendingDate, setPendingDate] = useState(selectedDate);
  return (
    <DatePicker
      minDate={minDate}
      maxDate={maxDate}
      disableFuture
      variant="inline"
      format="yyyy-MM"
      margin="normal"
      id="date-picker-inline"
      label={label}
      value={pendingDate}
      onChange={setPendingDate}
      onBlur={() => {}}
      onClose={() => {
        if (pendingDate !== selectedDate) {
          onSelectedDateChange(pendingDate);
        }
      }}
      onFocus={() => {}}
      className=""
      ref={ref}
      innerRef={() => {}}
      rows={1}
      rowsMax={1}
      color="primary"
      size="medium"
      style={{}}
      views={['month']}
    />
  );
});

export function BillingSummarySearch(props: BillingSummaryProps) {
  return (
    <MuiPickersUtilsProvider utils={DateFnsUtils}>
      <Grid container item spacing={2} alignContent="flex-end" justify="center">
        <Grid item>
          <BillingSummaryDatePicker
            label={'Start Date'}
            selectedDate={props.startDate}
            maxDate={props.endDate}
            onSelectedDateChange={date => {
              console.log('changing date to', date);
              if (null === date) {
                return;
              }

              props.onSelect('start', date);
            }}
          />
        </Grid>
        <Grid item>
          <BillingSummaryDatePicker
            label={'End Date'}
            selectedDate={props.endDate}
            minDate={props.startDate}
            onSelectedDateChange={date => {
              if (null === date) {
                return;
              }

              props.onSelect('end', date);
            }}
          />
        </Grid>
      </Grid>
    </MuiPickersUtilsProvider>
  );
}

export interface BillingHistoryData {
  month: string;
  billedAmount: string;
}

export interface BillingHistoryTableProps {
  startDate: Date;
  endDate: Date;
  data: BillingHistoryData[];
  onSearch: (startDate: Date, endDate: Date) => void;
}

export function BillingHistoryTable(props: BillingHistoryTableProps) {
  const billingHistoryOptions: MUIDataTableOptions = {
    download: false,
    filter: false,
    print: false,
    pagination: false,
    search: false,
    sort: true,
    selectableRows: 'none',
    viewColumns: false,
    customToolbar: () => (
      <BillingSummarySearch
        startDate={props.startDate}
        endDate={props.endDate}
        onSelect={(type: string, date: Date) => {
          if (type === 'start') {
            props.onSearch(date, props.endDate);
          } else {
            props.onSearch(props.startDate, date);
          }
        }}
      />
    ),
  };

  const billingHistoryColumns: MUIDataTableColumn[] = [
    {
      label: 'Month',
      name: 'month',
      options: {
        customBodyRender: value =>
          dateFns.format(dateFns.date(value), dateFns.yearMonthFormat),
      },
    },
    {
      label: 'Billed Amount',
      name: 'billedAmount',
      options: { customBodyRender: value => `$${value}` },
    },
    {
      name: 'total',
      options: {
        customHeadRender(val, _other) {
          let total = 0;
          let dataError = false;
          if (props.data.length > 0) {
            total = props.data.reduce((acc, { billedAmount }) => {
              const value = parseFloat(billedAmount);
              if (Number.isNaN(value)) {
                dataError = true;
                return acc;
              }
              return acc + value;
            }, 0);
          }

          return (
            <TableCell>
              {dataError ? (
                'Data error'
              ) : (
                <>
                  <b>Total:</b> ${total.toFixed(2)}
                </>
              )}
            </TableCell>
          );
        },
        sort: false,
      },
    },
  ];
  return (
    <MUIDataTable
      title="Billing History"
      data={props.data}
      columns={billingHistoryColumns}
      options={billingHistoryOptions}
    />
  );
}
