import * as React from 'react';
import { useState } from 'react';
import {
  createStyles,
  Dialog,
  makeStyles,
  Typography,
} from '@material-ui/core';
import DialogTitle from '@material-ui/core/DialogTitle';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import DialogContent from '@material-ui/core/DialogContent';
import Button from '@material-ui/core/Button';
import DialogContentText from '@material-ui/core/DialogContentText';
import { RequestState } from '../../interfaces/request-state.enum';
import { FormControl, hfErrors } from '../Form';
import { useForm, SubmitHandler } from 'react-hook-form';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Switch from '@material-ui/core/Switch';
import FormHelperText from '@material-ui/core/FormHelperText';
import DialogActions from '@material-ui/core/DialogActions';
import { addApplicationCredit } from '../../services/CreditService';
import { isErrorResponse } from '../../services/Service';

export interface CreditDialogProps {
  tenant?: any;
  open: boolean;
  onClose: () => void;
  onCreate: () => void;
}

interface CreditForm {
  amount: string;
  description: string;
  test: boolean;
}

const initialFormState = () => ({
  amount: "29",
  description: '',
  test: false,
});

const useStyles = makeStyles(theme =>
  createStyles({
    closeButton: {
      position: 'absolute',
      right: theme.spacing(1),
      top: theme.spacing(1),
      color: theme.palette.grey[500],
    },
    formFooterButton: {
      marginTop: theme.spacing(2),
      marginRight: theme.spacing(1),
    },
  }),
);

export const CreditDialog = (props: CreditDialogProps) => {
  const { tenant, open, onClose, onCreate } = props;
  const classes = useStyles();
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<CreditForm>({
    defaultValues: initialFormState(),
  });
  const [editDisabled, setEditDisabled] = useState(false);
  const [requestState, setRequestState] = useState<RequestState>(
    RequestState.NONE,
  );
  const [requestErrors, setRequestErrors] = useState<string[]>([]);

  const onSubmit: SubmitHandler<CreditForm> = async (values) => {
    setEditDisabled(true);
    const amount = parseFloat(values.amount);
    const { description, test } = values;

    const confirmation = window.confirm(`Are you sure you want to credit ${tenant.name} $${amount.toFixed(2)}?`);

    if (!confirmation) {
      setEditDisabled(false);
      return;
    }

    const res = await addApplicationCredit({
      amount,
      description,
      test: !!test ? true : null,
      tenantId: tenant.id,
    });

    if (isErrorResponse(res)) {
      try {
        setRequestErrors(
          res.errors && Array.isArray(res.errors)
            ? res.errors[0].messages
            : ['Unable to create credit.'],
        );
      } catch (e) {
        setRequestErrors(['Unable to create credit.']);
      }

      setRequestState(RequestState.ERROR);
      setEditDisabled(false);
      return;
    }
    setRequestState(RequestState.NONE);
    reset(initialFormState());
    onCreate();
  }
  return (
    <Dialog
      open={open}
      onClose={onClose}
      disableEscapeKeyDown={true}
      aria-labelledby="tenant-credit-dialog-title"
    >
      <DialogTitle id="tenant-credit-dialog-title">
        Credit {tenant && tenant.name}{' '}
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <DialogContentText>
          This will issue credits to merchants that can be used towards future
          app purchases in Shopify. <strong>A corresponding deduction based on your
            revenue share is entered in your Partner account by Shopify. For
            example, if you create a credit for $10.00, then a deduction of $8.00
            is applied.</strong>
        </DialogContentText>
        <DialogContentText>
          The total amount of all application credits requested by an app must
          not exceed the total amount the shop owner was charged in the last 30
          days, or the total amount of pending payouts in the app's Partner
          account.
        </DialogContentText>
        <form
          noValidate
          autoComplete="off"
          onSubmit={handleSubmit(onSubmit)}
        >
          {requestState === RequestState.ERROR && (
            <Typography variant="subtitle1" color="error">
              {requestErrors[0]}
            </Typography>
          )}
          <FormControl
            name="amount"
            label="Amount"
            type="number"
            disabled={editDisabled}
            errors={hfErrors('amount', errors)}
            {...register("amount", { min: 1, required: true })}
            startAdornment={<div>$</div>}
          />
          <FormControl
            name="description"
            label="Description"
            type="text"
            disabled={editDisabled}
            errors={hfErrors('description', errors)}
            {...register("description", { required: true })}
          />
          <FormControlLabel
            control={<Switch {...register("test")} />}
            label="Test Credit?"
          />
          <FormHelperText>
            If activated, the tenant will not receive a credit. Used for testing
            only.
          </FormHelperText>
          <DialogActions>
            <Button
              variant="contained"
              className={classes.formFooterButton}
              disabled={editDisabled}
              onClick={onClose}
              type="button"
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              color="primary"
              type="submit"
              className={classes.formFooterButton}
              disabled={editDisabled}
            >
              Give Credit
            </Button>
          </DialogActions>
        </form>
      </DialogContent>
    </Dialog>
  );
};
