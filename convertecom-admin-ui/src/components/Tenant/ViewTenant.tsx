import * as React from 'react';
import {
  FunctionComponent,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { PageContainer } from '../core/PageContainer';
import { isErrorResponse } from '../../services/Service';
import {
  getTenant,
  updateTenantFeatureFlags,
} from '../../services/TenantService';
import { useNavigate } from 'react-router-dom';
import {
  createStyles,
  FormControlLabel,
  Grid,
  List,
  makeStyles,
  Switch,
  Theme,
  Typography,
} from '@material-ui/core';
import MUIDataTable, {
  MUIDataTableColumn,
  MUIDataTableOptions,
} from 'mui-datatables';
import { getCreditLog } from '../../services/CreditService';
import DateFnsAdapter from '@date-io/date-fns';
import Button from '@material-ui/core/Button';
import { CreditDialog } from './CreditDialog';
import { LoadingContext } from '../../contexts/loading.context';
import { PageSection } from '../core/PageSection';
import { FeatureFlagsDto, TenantDto } from '../../dto/tenant.dto';
import ListItem from '@material-ui/core/ListItem';
import { getTenantBillingHistory } from '../../services/BillingService';
import { BillingHistoryData, BillingHistoryTable } from './BillingSummary';

const dateFns = new DateFnsAdapter();

export interface ViewTenantProps {
  id: string;
}

export interface FeatureFlagDescription {
  name: keyof FeatureFlagsDto;
  label: string;
  value: boolean;
  disabled?: boolean;
}

const useTenantInfoStyles = makeStyles(theme =>
  createStyles({
    root: {
      '& > dt': {
        fontWeight: 'bold',
      },
      '& > dd': {
        marginLeft: 0,
      },
    },
  }),
);

export interface TenantIntoProps {
  tenant: TenantDto;
  updateFeatureFlags: (featureFlags: Partial<FeatureFlagsDto>) => void;
  featureFlagsUpdating: boolean;
}

export const TenantInfo = ({
  tenant,
  updateFeatureFlags,
  featureFlagsUpdating,
}: TenantIntoProps) => {
  const classes = useTenantInfoStyles();
  const {
    billingTest,
    billingRecoveredRevenue,
    billingSummaryHistory,
  } = tenant.featureFlags;
  const featureFlags: FeatureFlagDescription[] = [
    { name: 'billingTest', label: 'Create test charges', value: !!billingTest },
    {
      name: 'billingRecoveredRevenue',
      label: 'Show recovered revenue in billing',
      value: !!billingRecoveredRevenue,
    },
    {
      name: 'billingSummaryHistory',
      label: 'Show summary history in billing',
      value: !!billingSummaryHistory,
    },
  ];
  const activationText = tenant.active
    ? tenant.latestActivationAction
      ? `Activated on ${new Date(
        tenant.latestActivationAction.createdAt,
      ).toLocaleDateString()}`
      : 'Active'
    : tenant.latestActivationAction
      ? `Deactivated on ${new Date(
        tenant.latestActivationAction.createdAt,
      ).toLocaleDateString()}`
      : 'Not Active';
  return (
    <div>
      <dl className={classes.root}>
        <dt>Name</dt>
        <dd>{tenant.name}</dd>
        <dt>Platform</dt>
        <dd>{tenant.platform}</dd>
        <dt>Host</dt>
        <dd>{tenant.externalId}</dd>
        <dt>Activation Status</dt>
        <dd>{activationText}</dd>
        <dt>Install Status</dt>
        <dd>Installed on {new Date(tenant.createdAt).toLocaleDateString()}</dd>
      </dl>
      <Typography variant="h6" component="h3">
        Feature Flags
      </Typography>
      <List dense>
        {featureFlags.map(flag => (
          <ListItem key={flag.name}>
            <FormControlLabel
              control={
                <Switch
                  name={flag.name}
                  checked={flag.value}
                  onChange={() =>
                    updateFeatureFlags({
                      [flag.name]: !flag.value,
                    })
                  }
                  disabled={flag.disabled || featureFlagsUpdating}
                />
              }
              label={<Typography>{flag.label}</Typography>}
            />
          </ListItem>
        ))}
      </List>
    </div>
  );
};

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    paper: {
      padding: theme.spacing(1, 2),
    },
  }),
);

const creditColumns: MUIDataTableColumn[] = [
  {
    label: 'Date',
    name: 'createdAt',
    options: { customBodyRender: value => dateFns.date(value).toDateString() },
  },
  {
    label: 'Amount',
    name: 'amount',
    options: { customBodyRender: value => `$${value.toFixed(2)}` },
  },
  {
    label: 'Description',
    name: 'description',
  },
  {
    label: 'Test?',
    name: 'test',
    options: { customBodyRender: value => (!!value ? 'True' : 'False') },
  },
];

export const ViewTenant: FunctionComponent<ViewTenantProps> = props => {
  const { id } = props;
  const navigate = useNavigate();
  const { setIsLoading } = useContext(LoadingContext);
  const [tenant, setTenant] = useState<TenantDto | undefined>(undefined);
  const [credits, setCredits] = useState<any[]>([]);
  const [creditDialogOpen, setCreditDialogOpen] = useState(false);
  const [featureFlagsUpdating, setFeatureFlagsUpdating] = useState(false);
  const [billingStartDate, setBillingStartDate] = useState(
    dateFns.addDays(new Date(), -365),
  );
  const [billingEndDate, setBillingEndDate] = useState(new Date());

  const [billingData, setBillingData] = useState<BillingHistoryData[]>([]);
  const billingDataLoaded = useRef(false);

  (() => {
    if (!billingDataLoaded.current) {
      onBillingSearch(billingStartDate, billingEndDate);
      billingDataLoaded.current = true;
    }
  })();

  function toggleCreditDialog() {
    setCreditDialogOpen(!creditDialogOpen);
  }

  async function handleCreate() {
    toggleCreditDialog();
    const response = await getCreditLog(id);
    if (isErrorResponse(response)) {
      alert('Unable to load charges.');
    } else {
      setCredits(response.data);
    }
  }

  useEffect(() => {
    async function fetchTenant() {
      const response = await getTenant(id);
      if (isErrorResponse(response)) {
        alert('Unable to load tenant.');
        navigate('/tenants');
      } else {
        setTenant(response.data);
      }
    }

    async function fetchCharges() {
      const response = await getCreditLog(id);
      if (isErrorResponse(response)) {
        alert('Unable to load charges.');
      } else {
        setCredits(response.data);
      }
    }

    if (!id) {
      return;
    }
    setIsLoading(true);
    Promise.all([fetchTenant(), fetchCharges()]).finally(() => {
      setIsLoading(false);
    });
  }, [id, setIsLoading, navigate]);

  async function updateFeatureFlags(featureFlags: Partial<FeatureFlagsDto>) {
    if (typeof tenant === 'undefined') {
      return;
    }
    setFeatureFlagsUpdating(true);
    const res = await updateTenantFeatureFlags(tenant.id, featureFlags);
    setFeatureFlagsUpdating(false);
    if (isErrorResponse(res)) {
      alert('Unable to update feature flags');
    } else {
      setTenant({
        ...tenant,
        featureFlags: { ...tenant.featureFlags, ...featureFlags },
      });
    }
  }

  const creditOptions: MUIDataTableOptions = {
    download: false,
    filter: false,
    print: false,
    pagination: false,
    search: false,
    sort: false,
    selectableRows: 'none',
    viewColumns: false,
    customToolbar: () => (
      <>
        <Button
          variant="contained"
          color="primary"
          onClick={toggleCreditDialog}
        >
          Give Credit
        </Button>
      </>
    ),
  };

  function onBillingSearch(startDate: Date, endDate: Date) {
    console.log('setting out promise ', startDate, endDate);
    setBillingStartDate(startDate);
    setBillingEndDate(endDate);
    getTenantBillingHistory(id, startDate, endDate).then(res => {
      if (res.data.monthlyBillingHistory) {
        console.log('setting in promise ', startDate, endDate);
        setBillingData(res.data.monthlyBillingHistory);
      } else {
        alert('Failed to fetch billing data');
      }
    });
  }

  return (
    <PageContainer>
      <PageSection
        title="View Tenant"
        sidebarContent={
          tenant ? (
            <TenantInfo
              tenant={tenant}
              updateFeatureFlags={updateFeatureFlags}
              featureFlagsUpdating={featureFlagsUpdating}
            />
          ) : null
        }
      >
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <BillingHistoryTable
              data={billingData}
              startDate={billingStartDate}
              endDate={billingEndDate}
              onSearch={onBillingSearch}
            />
          </Grid>
          <Grid item xs={12}>
            <MUIDataTable
              title="Credits"
              data={credits}
              columns={creditColumns}
              options={creditOptions}
            />
          </Grid>
        </Grid>

        <CreditDialog
          open={creditDialogOpen}
          onClose={toggleCreditDialog}
          onCreate={handleCreate}
          tenant={tenant}
        />
      </PageSection>
    </PageContainer>
  );
};
