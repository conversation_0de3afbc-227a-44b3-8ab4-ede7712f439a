import React, { useContext, useState } from 'react';
import { IFindAllParams } from '../../services/Service';
import { getTenants } from '../../services/TenantService';
import { CustomBodyRender, tenantTableColumns } from './datatable.config';
import MUIDataTable, { MUIDataTableState } from 'mui-datatables';
import { Button, Grid, Typography } from '@material-ui/core';
import LoadingProgress from '../LoadingProgress';
import { useNavigate } from 'react-router-dom';
import { LoadingContext } from '../../contexts/loading.context';

async function getData(tableState: MUIDataTableState) {
  const params: IFindAllParams = {
    skip: tableState.page * tableState.rowsPerPage,
    take: tableState.rowsPerPage,
  };

  if (tableState.activeColumn !== null) {
    const activeColumn = tableState.columns.find(
      c => c.name === tableState.activeColumn,
    );
    if (activeColumn) {
      params.order = activeColumn.name;
      params.dir = activeColumn.sortDirection;
    }
  }

  const filters = tableState.columns.reduce<{ [key: string]: string }>(
    (acc, { name }, i) => {
      const filterList = tableState.filterList[i];
      if (filterList && filterList.length > 0) {
        acc[name] = filterList.map(String).join(',');
      }
      return acc;
    },
    {},
  );

  const actualParams = {
    ...params,
    ...filters,
  };
  console.log('Actual Params', actualParams);
  return await getTenants(actualParams);
}

export default function ListTenants(props: any) {
  const navigate = useNavigate();
  const { isLoading, setIsLoading } = useContext(LoadingContext);
  const [data, setData] = useState<any[]>([]);
  const [count, setCount] = useState(0);
  const [tableState, setTableState] = useState<MUIDataTableState | undefined>(
    undefined,
  );
  function updateTable(newTableState: MUIDataTableState) {
    setTableState(newTableState);
    setIsLoading(true);
    getData(newTableState).then(({ data, total }) => {
      setIsLoading(false);
      setCount(total);
      setData(data);
    });
  }
  const options: MUIDataTableState & any = {
    serverSide: true,
    onTableChange: function (action: string, tableState: MUIDataTableState) {
      switch (action) {
        case 'changePage':
        case 'changeRowsPerPage':
        case 'sort':
        case 'filterChange':
          updateTable(tableState);
          break;
        default:
          if (action !== 'propsUpdate')
            console.log(`ignoring table action: ${action}`);
      }
    },
    onTableInit: function (_action: string, tableState: MUIDataTableState) {
      updateTable(tableState);
    },
    count: count,
    page: 0,
    rowsPerPageOptions: [1, 10, 25, 50],
    selectableRows: 'none' as const,
    print: false,
    download: false,
    search: false,
    filter: true,
    filterType: 'dropdown',
  };

  const tenantActions: CustomBodyRender = (id, tableMeta, updateValue) => {
    return (
      <>
        <Button size="small" onClick={() => navigate(`/tenants/${id}`)}>
          View
        </Button>
      </>
    );
  };

  return (
    <Grid container spacing={4}>
      <Grid item xs={12} md={3}>
        <Typography component="h6" variant="h6" color="primary" gutterBottom>
          Tenants
        </Typography>
        <Typography component="p" variant="body2">
          Stores that have installed the Zing app.
        </Typography>
      </Grid>

      <Grid item xs={12} md={9}>
        {isLoading && <LoadingProgress />}
        <MUIDataTable
          title={''}
          data={data}
          columns={tenantTableColumns({
            overrides: {
              actions: { customBodyRender: tenantActions },
              active: {
                customBodyRender(value) {
                  return value ? 'True' : 'False';
                },
              },
              createdAt: {
                customBodyRender: val => new Date(val).toLocaleDateString(),
              },
            },
            filterList:
              tableState && tableState.filterList ? tableState.filterList : [],
          })}
          options={options}
        />
      </Grid>
    </Grid>
  );
}
