import React from 'react';
import {
  MUIDataTableColumn,
  MUIDataTableColumnOptions,
  MUIDataTableMeta,
} from 'mui-datatables';

export interface CustomBodyRender {
  (
    value: any,
    tableMeta: MUIDataTableMeta,
    updateValue: (s: any, c: any, p: any) => any,
  ): React.ReactNode;
}

export const columns = [
  'id',
  'platform',
  'name',
  'externalId',
  'active',
  'createdAt',
  'actions',
];

export const columnOpts: {
  [key: string]: MUIDataTableColumn;
} = {
  id: {
    name: 'id',
    label: 'ID',
    options: {
      filter: false,
      sort: true,
      display: 'false',
    },
  },
  platform: {
    name: 'platform',
    label: 'Platform',
    options: {
      filter: true,
      sort: true,
    },
  },
  name: {
    name: 'name',
    label: 'Name',
    options: {
      filter: false,
      sort: true,
    },
  },
  externalId: {
    name: 'externalId',
    label: 'Host',
    options: {
      filter: false,
      sort: true,
    },
  },
  active: {
    name: 'active',
    label: 'Active?',
    options: {
      filter: true,
      sort: true,
      filterOptions: {
        names: ['true', 'false'],
      },
    },
  },
  createdAt: {
    name: 'createdAt',
    label: 'Install Date',
    options: {
      filter: false,
      sort: true,
    },
  },
  actions: {
    name: 'id',
    label: 'Actions',
    options: {
      filter: false,
      sort: false,
      searchable: false,
    },
  },
};

export interface TenantTableOpts {
  filterList: string[][];
  overrides: { [key: string]: MUIDataTableColumnOptions };
}

export const tenantTableColumns = (
  tableOpts: TenantTableOpts,
): MUIDataTableColumn[] => {
  return columns.map((col, i) => {
    const defaultOpts = columnOpts[col];
    const customOpts = tableOpts.overrides[col] || {};
    if (defaultOpts) {
      Object.assign(defaultOpts.options, {
        filterList: tableOpts.filterList[i],
        ...customOpts,
      });
    }
    return defaultOpts;
  });
};
/*   [
  {
    name: 'id',
    label: 'ID',
    options: {
      filter: false,
      sort: true,
      display: 'false',
    },
  },
  {
    name: 'platform',
    label: 'Platform',
    options: {
      filter: true,
      sort: true,
    },
  },
  {
    name: 'name',
    label: 'Name',
    options: {
      filter: false,
      sort: true,
    },
  },
  {
    name: 'externalId',
    label: 'Host',
    options: {
      filter: false,
      sort: true,
    },
  },
  {
    name: 'active',
    label: 'Active',
    options: {
      filter: true,
      sort: true,
      filterList: opts.filterList[4] || [],
    },
  },
  {
    name: 'installDate',
    label: 'Install Date',
    options: {
      filter: false,
      sort: true,
    },
  },
  {
    name: 'actions',
    label: 'Actions',
    options: {
      filter: false,
      sort: false,
      searchable: false,
      customBodyRender: opts.actions.renderer,
    },
  },
];*/
