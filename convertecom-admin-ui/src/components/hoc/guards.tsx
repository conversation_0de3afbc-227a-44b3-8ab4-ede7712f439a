import * as React from 'react';
import { useContext } from 'react';
import { GlobalContextValue } from '../../interfaces/global-props.interface';
import { GlobalContext } from '../../contexts/global.context';
import { isAuthenticated, isSuperAdmin } from '../../services/AuthService';
import { useNavigate, useParams, Navigate } from 'react-router-dom';

const ShouldActivateGuard = (props: {
  test: (values: GlobalContextValue) => boolean;
  WhenTrue: React.ElementType;
  WhenFalse: string | React.ElementType;
  params: Record<string, string>;
}) => {
  const { test, WhenTrue, WhenFalse, params } = props;
  const globalContext = useContext(GlobalContext);
  const shouldActivateRoute = test(globalContext);
  if (shouldActivateRoute) {
    return <WhenTrue {...params} />;
  }
  if (typeof WhenFalse === 'string') {
    return <Navigate to={WhenFalse} replace />;
  }
  return <WhenFalse {...params} />;
};

export const shouldActivate = (
  test: (values: GlobalContextValue) => boolean,
  WhenTrue: React.ElementType,
  WhenFalse: string,
) => {
  return (props: any) => {
    const params = useParams();
    return (
      <ShouldActivateGuard
        test={test}
        WhenTrue={WhenTrue}
        WhenFalse={WhenFalse}
        params={params}
      />
    );
  };
};

export const whenSuperAdmin = (WhenTrue: React.ElementType) => () => {
  const params = useParams();
  return (
    <ShouldActivateGuard
      test={() => isAuthenticated() && isSuperAdmin()}
      WhenTrue={WhenTrue}
      WhenFalse={'/'}
      params={params}
    />
  );
};

export const whenNotSuperAdmin = (WhenTrue: React.ElementType) => () => {
  const params = useParams();
  return (
    <ShouldActivateGuard
      test={() => isAuthenticated() && !isSuperAdmin()}
      WhenTrue={WhenTrue}
      WhenFalse={'/'}
      params={params}
    />
  );
};
