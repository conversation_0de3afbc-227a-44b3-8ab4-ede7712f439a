import * as React from 'react';
import { createStyles, makeStyles, Theme } from '@material-ui/core';
import ErrorOutlineIcon from '@material-ui/icons/ErrorOutline';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    root: {
      backgroundColor: theme.palette.secondary.dark,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: `${theme.spacing(1)}px ${theme.spacing(2)}px`,
      fontWeight: 'bold',
      color: theme.palette.common.white,
    },
    icon: {
      marginRight: `${theme.spacing(2)}px`,
    }
  }),
);

export interface TrialNotificationProps {
  trialDaysRemaining?: number;
}

export const TrialNotification = ({
  trialDaysRemaining,
}: TrialNotificationProps) => {
  const classes = useStyles();
  return (
    trialDaysRemaining ? (
      <aside className={classes.root} role="alert">
        <ErrorOutlineIcon className={classes.icon}/> You have {trialDaysRemaining} day{trialDaysRemaining !== 1 ? 's' : ''} left on your free
        trial
      </aside>
    ) : null
  );
};
