import React from 'react';

import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';

import ViewBillingLogButton from './ViewBillingLogButton';

import { isSuperAdmin } from '../../services/AuthService';

import numeral from 'numeral';

interface TenantInfo {
    id: '',
    tenantId: '',
    date: '',
    amount: '',
    percentage: "",
    subtotalSum: "",
    createdAt: "",
    updatedAt: "",
    tenant: { name: '' },
    billingLog: null
}

const formatAmount = (value: any) => {
    return numeral(value).format('$0,0.00')
}

export default (props: any) => {
    return (
        <Table>
            <TableHead>
                <TableRow>
                    {props.checkbox &&
                        <TableCell style={{ width: "20%" }}>ID</TableCell>
                    }
                    <TableCell align="center">Date</TableCell>
                    {isSuperAdmin() &&
                        <TableCell align="center">Tenant</TableCell>
                    }
                    <TableCell align="center">Billable Subtotal Sum</TableCell>
                    <TableCell align="center">Usage Charge</TableCell>
                    <TableCell align="center">API Log</TableCell>
                </TableRow>
            </TableHead>
            <TableBody>
                {props.data.map((tenant: TenantInfo, index: number) => (
                    <TableRow key={index}>
                        {props.checkbox &&
                            <TableCell>{tenant.id}</TableCell>
                        }
                        <TableCell align="center">{tenant.date}</TableCell>
                        {isSuperAdmin() &&
                            <TableCell align="center">{tenant.tenant.name}</TableCell>
                        }
                        <TableCell align="center">{formatAmount(tenant.subtotalSum)}</TableCell>
                        <TableCell align="center">{formatAmount(tenant.amount)}</TableCell>
                        <TableCell align="center">
                            <ViewBillingLogButton billingLog={tenant.billingLog} />
                        </TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </Table>
    )
}