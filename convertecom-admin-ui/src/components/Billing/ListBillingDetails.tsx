import React, { useContext, useEffect, useRef, useState } from 'react';
import { downloadBillingCsv, getBilling } from '../../services/BillingService';
import { Button, Grid, Typography } from '@material-ui/core';
import { getTenants } from '../../services/TenantService';
import { isSuperAdmin } from '../../services/AuthService';
import LoadingProgress from '../LoadingProgress';
import { createStyles, makeStyles } from '@material-ui/core/styles';
import SettingsIcon from '@material-ui/icons/Settings';

import TableContainer from '@material-ui/core/TableContainer';
import TablePagination from '@material-ui/core/TablePagination';
import Toolbar from '@material-ui/core/Toolbar';
import IconButton from '@material-ui/core/IconButton';
import FilterListIcon from '@material-ui/icons/FilterList';
import Paper from '@material-ui/core/Paper';

import Popper from '@material-ui/core/Popper';
import Grow from '@material-ui/core/Grow';
import MenuItem from '@material-ui/core/MenuItem';
import FormControl from '@material-ui/core/FormControl';
import Select from '@material-ui/core/Select';
import InputLabel from '@material-ui/core/InputLabel';

import FormControlLabel from '@material-ui/core/FormControlLabel';
import Checkbox from '@material-ui/core/Checkbox';
import { DatePicker, MuiPickersUtilsProvider } from '@material-ui/pickers';
import DateFnsUtils from '@date-io/date-fns';

import BillingTable from './BillingDataTable';
import { useNavigate } from 'react-router-dom';
import Box from '@material-ui/core/Box';
import { LoadingContext } from '../../contexts/loading.context';

interface Option {
  id: string | number;
  name: string;
}

const useStyles = makeStyles(() =>
  createStyles({
    filter: {
      flexDirection: 'row-reverse',
    },
    popover: {
      padding: '2rem',
    },
    popper: {
      zIndex: 100,
    },
    formControl: {
      minWidth: 200,
      width: '100%',
    },
  }),
);

async function getTenantOptions(): Promise<Option[]> {
  const { data } = await getTenants({ order: 'name', dir: 'asc' });
  return data.map((o: any) => {
    return { id: o.id, name: o.name };
  });
}

export default (props: any) => {
  const classes = useStyles();
  const navigate = useNavigate();
  const { isLoading, setIsLoading } = useContext(LoadingContext);
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [allTenants, setTenantOptions] = useState([
    {
      id: '',
      name: '',
    },
  ]);
  const [showDownloadError, setShowDownloadError] = useState(false);
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(10);
  const [billingParams, setParams] = React.useState({
    order: 'date',
    dir: 'desc',
    take: rowsPerPage,
  });
  const [open, setOpen] = React.useState(false);
  const [tenant, setTenant] = React.useState('');
  const [checkbox, setCheckbox] = React.useState(false);
  const [selectedDate, setSelectedDate] = React.useState<Date | null>(null);
  const anchorRef = useRef<HTMLButtonElement>(null);
  const tenantOptions = useRef([] as Option[]);

  async function getData(params: any, page = 0) {
    const billingInfo = await getBilling(params);
    setData(billingInfo.data);
    setTotal(billingInfo.total);
    setPage(page);
  }

  useEffect(() => {
    setIsLoading(true);
    getData(billingParams)
      .then(res => setIsLoading(false))
      .catch(err => console.log(err));
    getTenants({ order: 'name', dir: 'asc' }).then(res => {
      setTenantOptions(res.data);
    });
    if (isSuperAdmin()) {
      getTenantOptions()
        .then(res => {
          tenantOptions.current = res;
        })
        .catch(console.error);
    }
  }, [billingParams, setIsLoading]);

  async function handleDownload() {
    try {
      const file = await downloadBillingCsv();
      if (!file) {
        throw new Error('Nothing to download');
      }
      const csvContent = 'data:text/csv;charset=utf-8,' + file;
      window.open(encodeURI(csvContent), '_blank');
    } catch (e) {
      setShowDownloadError(true);
      setTimeout(() => setShowDownloadError(false), 5000);
    }
  }

  const handleChangePage = (event: unknown, newPage: number) => {
    const params = { ...billingParams, skip: newPage * rowsPerPage };
    getData(params, newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    const params = Object.assign(billingParams, { take: event.target.value });
    setParams(params);
    getData(params);
  };

  const handleToggle = () => {
    setOpen(prevOpen => !prevOpen);
  };

  const clearFilter = () => {
    const originalParams = { order: 'date', dir: 'desc', take: rowsPerPage };
    handleToggle();
    setTenant('');
    setSelectedDate(null);
    setParams(originalParams);
    getData(originalParams);
  };

  const handleDateChange = (date: Date | null) => {
    let formatted_date = null;
    if (date) {
      const month =
        (date.getMonth() + 1 < 10 ? '0' : '') + (date.getMonth() + 1);
      const day = (date.getDate() < 10 ? '0' : '') + date.getDate();
      formatted_date = date.getFullYear() + '-' + month + '-' + day;
      const updatedParams = Object.assign(billingParams, {
        date: formatted_date,
      });
      setParams(updatedParams);
      getData(updatedParams);
    }
    handleToggle();
    setSelectedDate(date);
  };
  const handleTenant = (event: React.ChangeEvent<{ value: unknown }>) => {
    handleToggle();
    setTenant(event.target.value as string);
    const updatedParams = Object.assign(billingParams, {
      tenantId: event.target.value,
    });
    if (event.target.value) {
      setParams(updatedParams);
      getData(billingParams);
    } else {
      const { tenantId, ...params } = updatedParams; // clear tenant id from the params
      setParams(params);
      getData(params);
    }
  };

  const toggleCheckbox = () => {
    setCheckbox(!checkbox);
  };

  const FilterPopover = () => {
    return (
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        className={classes.popper}
        transition
        disablePortal
      >
        {({ TransitionProps }) => (
          <Grow
            {...TransitionProps}
            style={{ transformOrigin: 'center bottom' }}
          >
            <Paper className={classes.popover}>
              <Grid container alignItems="center" justify="space-between">
                <Grid>Filter By:</Grid>
                <Grid>
                  <Button
                    color="primary"
                    variant="outlined"
                    type="button"
                    onClick={() => clearFilter()}
                  >
                    Clear
                  </Button>
                </Grid>
              </Grid>
              <div>
                {isSuperAdmin() && (
                  <FormControl className={classes.formControl}>
                    <InputLabel id="demo-simple-select-label">
                      Tenant
                    </InputLabel>
                    <Select
                      labelId="demo-simple-select-label"
                      id="demo-simple-select"
                      value={tenant}
                      onChange={handleTenant}
                    >
                      <MenuItem key="selectAll" value={''}>
                        All
                      </MenuItem>
                      {allTenants.map((tenant, index) => (
                        <MenuItem key={index} value={tenant.id}>
                          {tenant.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
                <MuiPickersUtilsProvider utils={DateFnsUtils}>
                  <Grid container>
                    <DatePicker
                      disableToolbar
                      disableFuture
                      variant="inline"
                      format="yyyy-MM-dd"
                      margin="normal"
                      id="date-picker-inline"
                      label="Date"
                      value={selectedDate}
                      onChange={handleDateChange}
                      onBlur={() => { }}
                      onFocus={() => { }}
                      className=""
                      ref={() => { }}
                      innerRef={() => { }}
                      rows={1}
                      rowsMax={1}
                      style={{ width: '100%' }}
                      color="primary"
                      size="medium"
                    />
                  </Grid>
                </MuiPickersUtilsProvider>
              </div>
              <FormControl>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={checkbox}
                      onChange={toggleCheckbox}
                      color="primary"
                    />
                  }
                  label="Show ID"
                />
              </FormControl>
            </Paper>
          </Grow>
        )}
      </Popper>
    );
  };

  const navigateBillingSettings = () => navigate('/billing/settings');

  return (
    <Grid container spacing={4}>
      <Grid item xs={12} md={3}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography component="h6" variant="h6" color="primary" gutterBottom>
            Billing
          </Typography>

          <Button
            endIcon={<SettingsIcon />}
            onClick={navigateBillingSettings}
            size="small"
          >
            Settings
          </Button>
        </Box>

        <Typography component="p" variant="body2" gutterBottom>
          A log of tenant daily usage charges.
        </Typography>

        <Button
          variant="contained"
          color="primary"
          type="button"
          onClick={() => handleDownload()}
        >
          Download Billing CSV
        </Button>

        <Typography
          variant="body2"
          component="p"
          style={{
            opacity: showDownloadError ? 1 : 0,
            color: 'red',
            transition: 'opacity 0.5s ease',
          }}
        >
          Could not download billing csv. Either there was an error, or there
          are no billing records to download.
        </Typography>
      </Grid>

      <Grid item xs={12} md={9}>
        {isLoading && <LoadingProgress />}
        <TableContainer component={Paper}>
          <Toolbar className={classes.filter}>
            <IconButton
              ref={anchorRef}
              aria-label="filter list"
              onClick={handleToggle}
            >
              <FilterListIcon />
            </IconButton>
            <FilterPopover />
          </Toolbar>
          <BillingTable data={data} checkbox={checkbox} />
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={total}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Grid>
    </Grid>
  );
};
