import React, { useState } from 'react';
import { Button, Dialog, DialogTitle, Box } from '@material-ui/core';
import ReactJson from 'react-json-view';

export default (props: any) => {
  const { billingLog } = props;
  const [open, setOpen] = useState(false);

  if (billingLog) {
    const billingLogJson = JSON.parse(billingLog.rawResponse || '{}');

    return <>
      <Button variant="outlined" color="primary" type="button" onClick={() => setOpen(true)}>View</Button>
      <Dialog maxWidth="md" fullWidth={true} onClose={() => setOpen(false)} open={open}>
        <DialogTitle id="simple-dialog-title">API Raw Response</DialogTitle>
        <Box style={{ padding: '5px' }}>
          <ReactJson src={billingLogJson} />
        </Box>
      </Dialog>
    </>;
  } else {
    return <Button disabled>Pending</Button>
  }
}
