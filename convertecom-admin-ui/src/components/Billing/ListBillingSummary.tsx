import React, { useCallback, useContext, useEffect, useState } from 'react';
import {
  Box,
  Grid,
  List,
  ListItem,
  ListItemText,
  Typography,
} from '@material-ui/core';
import { get } from 'lodash';
import numeral from 'numeral';
import LoadingProgress from '../LoadingProgress';
import BillingCard from './BillingCard';
import { getBillingSummaryForPeriod } from '../../services/BillingService';
import SystemError from '../Errors/SystemError';
import { LoadingContext } from '../../contexts/loading.context';
import { BillingSummaryDto } from '../../dto/billing.dto';
import { APIErrorResponse, isErrorResponse } from '../../services/Service';
import { TenantContext } from '../../contexts/tenant.context';
import { hasFeatureFlag } from '../../services/TenantService';
import { format, parse } from 'date-fns';
import { makeStyles } from '@material-ui/core/styles';

export interface BillingSummaryProps {
  summary: BillingSummaryDto;
  showRecoveredRevenue: boolean;
  isBillingSummary: boolean;
}

function formatHeaderDate(date: string) {
  return format(parse(date, 'yyyy-MM-dd', new Date()), 'MMM d y');
}

const useStyles = makeStyles({
  historyContainer: {
    maxHeight: '70vh',
    overflowY: 'auto',
    padding: '0 0.25rem 0.25rem',
  },
  summaryContainer: {
    position: 'relative',
  },
});

export const BillingSummary = ({
  summary,
  showRecoveredRevenue,
  isBillingSummary,
}: BillingSummaryProps) => {
  const latestPeriod = get(summary, 'billingPeriods[0]', { periodNumber: -1 });
  const isLatestPeriod = summary.periodNumber === latestPeriod.periodNumber;
  return (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        {isBillingSummary && !isLatestPeriod ? (
          <BillingCard
            title="Billing Summary"
            value={`${formatHeaderDate(summary.startDate)} - ${formatHeaderDate(
              summary.endDate,
            )}`}
          />
        ) : (
          <BillingCard
            title="Current Billing Cycle"
            value={summary.billingCycle}
          />
        )}
      </Grid>
      <Grid item xs={4}>
        <BillingCard
          title="Monthly Flat Fee"
          value={numeral(summary.monthlyFee).format('$0,0')}
        />
      </Grid>
      <Grid item xs={4}>
        <BillingCard
          title="Usage Rate"
          value={numeral(summary.usageRate).format('0,0.00%')}
        />
      </Grid>
      <Grid item xs={4}>
        <BillingCard
          title={isLatestPeriod ? 'Pending Usage Charge' : 'Usage Charge'}
          value={numeral(summary.pendingUsage).format('$0,0.00')}
        />
      </Grid>
      {!showRecoveredRevenue ? null : (
        <Grid item xs={4}>
          <BillingCard
            title="Recovered Revenue"
            value={numeral(summary.recoveredRevenue).format('$0,0.00')}
          />
        </Grid>
      )}
    </Grid>
  );
};

export default function ListBillingSummary() {
  const classes = useStyles();
  const { tenant } = useContext(TenantContext);
  const { isLoading, setIsLoading } = useContext(LoadingContext);
  const [loadError, setLoadError] = useState('');
  const [loadErrorCode, setLoadErrorCode] = useState(500);
  const [billingSummary, setBillingSummary] = useState<BillingSummaryDto>({
    billingCycle: '',
    billableSubtotal: 0,
    monthlyFee: 0,
    usageRate: 0,
    pendingUsage: 0,
    recoveredRevenue: undefined,
    startDate: '',
    endDate: '',
    periodNumber: -1,
    billingPeriods: [],
  });

  const showBillingSummaryHistory =
    hasFeatureFlag(
      'billingSummaryHistory',
      tenant ? tenant.featureFlags : {},
    ) &&
    Array.isArray(billingSummary.billingPeriods) &&
    billingSummary.billingPeriods.length > 0;
  const showRecoveredRevenue = hasFeatureFlag(
    'billingRecoveredRevenue',
    tenant ? tenant.featureFlags : {},
  );

  const handleErrorResponse = useCallback(
    (res: APIErrorResponse) => {
      setIsLoading(false);
      setLoadError(
        'Your billing information could not be retrieved. Please contact support if this issue persists.',
      );
      setLoadErrorCode(get(res, 'response.status', 500));
    },
    [setIsLoading],
  );

  async function selectBillingPeriod(periodNumber: number) {
    setIsLoading(true);
    const res = await getBillingSummaryForPeriod(periodNumber);
    if (isErrorResponse(res)) {
      handleErrorResponse(res);
      setIsLoading(false);
      return;
    }
    setBillingSummary(res);
    setIsLoading(false);
  }

  useEffect(() => {
    setIsLoading(true);

    getBillingSummaryForPeriod()
      .then(response => {
        if (isErrorResponse(response)) {
          handleErrorResponse(response);
          return;
        }
        setBillingSummary(response);
        setIsLoading(false);
      })
      .catch(err => {
        handleErrorResponse(err);
      });
  }, [handleErrorResponse, setIsLoading]);

  if (loadError) {
    return <SystemError text={loadError} code={loadErrorCode} />;
  }

  if (billingSummary.periodNumber === -1 && isLoading) {
    return <LoadingProgress />;
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12} md={3}>
        <Typography component="h6" variant="h6" color="primary" gutterBottom>
          Billing
        </Typography>
        {!showBillingSummaryHistory ? (
          <Typography component="p" variant="body2">
            Current billing period.
          </Typography>
        ) : (
          <>
            <Typography component="h4" variant="body1">
              History
            </Typography>
            <Box className={classes.historyContainer}>
              <List aria-label="Billing periods">
                {billingSummary.billingPeriods!.map(period => (
                  <ListItem
                    button
                    key={period.periodNumber}
                    selected={
                      period.periodNumber === billingSummary.periodNumber
                    }
                    onClick={() => selectBillingPeriod(period.periodNumber)}
                    disabled={isLoading}
                  >
                    <ListItemText
                      primary={format(
                        parse(period.startDate, 'yyyy-MM-dd', new Date()),
                        'MMMM y',
                      )}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          </>
        )}
      </Grid>

      <Grid item xs={12} md={9} className={classes.summaryContainer}>
        {isLoading ? (
          <LoadingProgress backgroundColor="transparent" />
        ) : (
          <BillingSummary
            summary={billingSummary}
            showRecoveredRevenue={showRecoveredRevenue}
            isBillingSummary={showBillingSummaryHistory}
          />
        )}
      </Grid>
    </Grid>
  );
}
