import { makeStyles } from '@material-ui/core/styles';

export const useStyles = makeStyles(theme => ({
  formControl: {
    margin: theme.spacing(2, 0),
  },
  formControlWithColor: {
    padding: '2px',
    marginBottom: '8px',
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
  },
  verticalDivider: {
    height: 28,
    margin: 4,
  },
  colorTextInput: {
    width: '100%',
  },
  swatch: {
    padding: '5px',
    background: '#fff',
    borderRadius: '1px',
    boxShadow: '0 0 0 1px rgba(0,0,0,.1)',
    display: 'inline-block',
    cursor: 'pointer',
  },
  popover: {
    position: 'absolute',
    zIndex: 2,
  },
  cover: {
    position: 'absolute',
    top: '15px',
    right: 0,
    bottom: 0,
    left: 0,
  },
  coverAll: {
    position: 'fixed',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    zIndex: 1,
  },
}));
