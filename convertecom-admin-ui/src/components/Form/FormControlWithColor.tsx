import React, { useState } from 'react';
import { Box, Divider, Input, InputLabel } from '@material-ui/core';
import { SketchPicker } from 'react-color';
import { useStyles } from './styles';
import { TemplateValue } from '../../interfaces/template-value.interface';

interface FormControlWithColorProps {
  value?: TemplateValue;
  hasBackgroundAlpha?: boolean;
  hasColorAlpha?: boolean;
  name: string;
  label: string;
  setter: (changes: TemplateValue) => void;
  errors?: any;
  hideInput?: boolean;
  endAdornment?: any;
  startAdornment?: any;
  type?: string;
}

export const FormControlWithColor: React.FC<
  FormControlWithColorProps
> = props => {
  const classes = useStyles();
  const { text = '', color = '', backgroundColor = '', colorRgba = undefined } =
    props.value || {};

  const [colorOpen, setColorOpen] = useState(false);
  const [backgroundColorOpen, setBackgroundColorOpen] = useState(false);

  const onChange = (key: string) => (
    event: React.ChangeEvent<
      HTMLInputElement | { name?: string | undefined; value: unknown }
    >,
  ) => {
    props.setter({ ...props.value, [key]: event.target.value });
  };

  const previewColor = props.hasColorAlpha
    ? colorRgba
      ? `rgba(${colorRgba.r}, ${colorRgba.g}, ${colorRgba.b}, ${colorRgba.a})`
      : color
    : color;

  return (
    <>
      <InputLabel shrink={true} disableAnimation={true} htmlFor={props.name}>
        {props.label}
      </InputLabel>
      <Box className={classes.formControlWithColor}>
        {color && (
          <>
            <div
              className={classes.swatch}
              onClick={() => setColorOpen(!colorOpen)}
            >
              <div
                style={{ backgroundColor: previewColor, height: 20, width: 25 }}
              ></div>
            </div>
            {colorOpen && (
              <>
                <div
                  className={classes.coverAll}
                  onClick={() => setColorOpen(false)}
                />
                <div className={classes.popover}>
                  <div className={classes.cover}>
                    <SketchPicker
                      color={props.hasColorAlpha ? colorRgba || color : color}
                      onChange={c => {
                        props.setter({
                          ...props.value,
                          color: c.hex,
                          colorRgba: c.rgb,
                        });
                      }}
                      disableAlpha={!props.hasColorAlpha}
                    />
                  </div>
                </div>
              </>
            )}
          </>
        )}
        {backgroundColor && (
          <>
            <Divider
              orientation="vertical"
              className={classes.verticalDivider}
            />
            <div
              className={classes.swatch}
              onClick={() => setBackgroundColorOpen(!backgroundColorOpen)}
            >
              <div style={{ backgroundColor, height: 20, width: 25 }}></div>
            </div>
            {backgroundColorOpen && (
              <>
                <div
                  className={classes.coverAll}
                  onClick={() => setBackgroundColorOpen(false)}
                />
                <div className={classes.popover}>
                  <div className={classes.cover}>
                    <SketchPicker
                      color={backgroundColor}
                      onChange={c =>
                        props.setter({ ...props.value, backgroundColor: c.hex })
                      }
                      disableAlpha={!props.hasBackgroundAlpha}
                    />
                  </div>
                </div>
              </>
            )}
          </>
        )}
        {!props.hideInput && (
          <>
            <Divider
              orientation="vertical"
              className={classes.verticalDivider}
            />
            <Input
              id={props.name}
              type={props.type || 'text'}
              value={text}
              onChange={onChange('text')}
              endAdornment={props.endAdornment}
              startAdornment={props.startAdornment}
              className={classes.colorTextInput}
              multiline={true}
            />
          </>
        )}
        {props.children}
      </Box>
    </>
  );
};

export default FormControlWithColor;
