import React from 'react';
import {
  FormControl,
  InputLabel,
  Input,
  FormHelperText,
  InputProps, InputBaseComponentProps,
} from '@material-ui/core';
import { find, get } from 'lodash';
import { useStyles } from './styles';
import { APIError } from '../../services/Service';
import { FieldError, FieldErrors } from 'react-hook-form/dist/types';

function getErrors(errors: APIError[], fieldName: string): string[] {
  const found = find(errors, { field: fieldName });
  return found ? found.messages : [];
}

export function hfErrors(
  field: string,
  errors: FieldErrors<unknown>,
): APIError[] | undefined {
  const fieldErrors: FieldError | undefined = get(errors, field, undefined);
  if (typeof fieldErrors !== 'undefined') {
    return [{ field, messages: [fieldErrors.message as string] }];
  }
  return undefined;
}

export interface FormControlProps {
  errors?: APIError[];
  hint?: string | React.ReactNode;
  label: string;
  inputClass?: string;
  name: string;
  variant?: any;
  inputProps?: InputBaseComponentProps;
}

export default (props: FormControlProps & InputProps) => {
  const classes = useStyles();

  const errors = getErrors(props.errors || [], props.name);
  const disabled =
    typeof props.disabled !== 'undefined' ? props.disabled : false;
  console.log(errors);

  return (
    <FormControl
      className={classes.formControl}
      fullWidth
      required={props.required || false}
      error={errors.length > 0}
      margin="normal"
    >
      <InputLabel shrink={true} disableAnimation={true} htmlFor={props.name}>
        {props.label}
      </InputLabel>
      <Input
        id={props.name}
        name={props.name || ''}
        type={props.type || 'text'}
        value={props.value}
        onChange={props.onChange}
        onBlur={props.onBlur}
        endAdornment={props.endAdornment}
        startAdornment={props.startAdornment}
        className={props.inputClass}
        disabled={disabled}
        inputRef={props.inputRef}
        inputProps={props.inputProps}
      />
      {props.hint && errors.length === 0 && (
        <FormHelperText>{props.hint}</FormHelperText>
      )}
      {errors.map((error: string) => (
        <FormHelperText>{error}</FormHelperText>
      ))}
    </FormControl>
  );
};
