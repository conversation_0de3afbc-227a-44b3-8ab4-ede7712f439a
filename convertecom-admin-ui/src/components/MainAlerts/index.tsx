import React from 'react';
import { useStyles } from './styles';

function pluckMessage(key: string) {
  const message = window.sessionStorage.getItem(key);
  if (message) {
    window.sessionStorage.removeItem(key);
    return message;
  }

  return null;
}

export default () => {
  const classes = useStyles();

  const errorMessage = pluckMessage('mainError');
  const successMessage = pluckMessage('mainSuccess');

  return (
    <>
      {errorMessage && <div className={classes.mainError}>{errorMessage}</div>}
      {successMessage && (
        <div className={classes.mainSuccess}>{successMessage}</div>
      )}
    </>
  );
};
