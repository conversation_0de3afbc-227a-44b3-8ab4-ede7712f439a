import { makeStyles } from '@material-ui/core/styles';

export const useStyles = makeStyles(theme => ({
  mainError: {
    minWidth: '100%',
    backgroundColor: theme.palette.error.dark,
    color: theme.palette.error.contrastText,
    padding: theme.spacing(1),
    marginBottom: theme.spacing(2),
  },
  mainSuccess: {
    minWidth: '100%',
    backgroundColor: '#7eed80',
    color: theme.palette.primary.contrastText,
    padding: theme.spacing(1),
    marginBottom: theme.spacing(2),
  },
}));
