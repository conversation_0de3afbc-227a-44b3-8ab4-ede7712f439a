import { createStyles, makeStyles } from '@material-ui/core/styles';
import { Theme } from '@material-ui/core';

export const useSettingsStyles = makeStyles((theme: Theme) =>
  createStyles({
    root: {
      flexGrow: 1,
    },
    paper: {
      padding: theme.spacing(3, 4),
      borderRadius: 16,
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
      border: '1px solid rgba(0, 0, 0, 0.05)',
      backgroundColor: '#FFFFFF',
      transition: 'all 0.3s ease',
      '&:hover': {
        boxShadow: '0 8px 30px rgba(0, 0, 0, 0.08)',
        transform: 'translateY(-4px)',
      },
    },
    container: {
      flexWrap: 'wrap',
    },
    formFooterButton: {
      marginTop: theme.spacing(3),
      marginRight: theme.spacing(2),
      padding: theme.spacing(1, 3),
      borderRadius: 8,
      fontWeight: 600,
    },
    formControl: {
      minWidth: 120,
      marginBottom: theme.spacing(3),
      '& .MuiOutlinedInput-root': {
        borderRadius: 8,
      },
    },
    percentageInput: {
      maxWidth: 60,
      textAlign: 'center',
      borderRadius: 8,
    },
    accountSettingsTopGutter: {
      marginTop: 50,
    },
    divider: {
      marginTop: theme.spacing(4),
      marginBottom: theme.spacing(4),
    },
    warning: {},
    setupAlert: {
      minWidth: '100%',
      backgroundColor: theme.palette.error.main,
      color: theme.palette.error.contrastText,
      padding: theme.spacing(3),
      marginBottom: theme.spacing(3),
      borderRadius: 16,
      boxShadow: '0 4px 12px rgba(216, 44, 13, 0.2)',
      '& li': {
        marginBottom: '8px',
      },
      '& ul': {
        marginTop: '8px',
        paddingLeft: theme.spacing(2),
      },
      '& a': {
        color: 'white',
        fontWeight: 'bold',
        textDecoration: 'underline',
      },
    },
    gradientPreview: {
      borderRadius: '30px',
      height: '60px',
      width: '60px',
      marginLeft: '4px',
      background: 'red',
      boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
    },
    uploadWrapper: {
      position: 'relative',
      borderRadius: 12,
      overflow: 'hidden',
    },
    uploadOverlay: {
      background: 'rgba(240, 240, 240, 0.8)',
      position: 'absolute',
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      backdropFilter: 'blur(4px)',
    },
    uploadSpinner: {
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
    },
    error: {
      marginTop: theme.spacing(2),
      padding: theme.spacing(2),
      color: '#FFFFFF',
      background: theme.palette.error.main,
      fontWeight: 500,
      borderRadius: 8,
      boxShadow: '0 4px 12px rgba(216, 44, 13, 0.2)',
    },
    sectionTitles: {
      display: 'flex',
      alignItems: 'center',
      marginBottom: theme.spacing(1),
    },
    sectionTitle: {
      fontSize: '1.25rem',
      fontWeight: 600,
      color: theme.palette.text.primary,
    },
    infoLinks: {
      display: 'flex',
      marginLeft: theme.spacing(1),
    },
    infoSpacing: {
      marginRight: theme.spacing(1),
    },
    stepper: {
      backgroundColor: 'transparent',
      marginBottom: theme.spacing(4),
      paddingLeft: '0',
      paddingRight: '0',
      '& .MuiStepLabel-label': {
        fontWeight: 500,
      },
      '& .MuiStepLabel-active': {
        fontWeight: 600,
        color: theme.palette.primary.main,
      },
      '& .MuiStepIcon-root.MuiStepIcon-active': {
        color: theme.palette.primary.main,
      },
      '& .MuiStepIcon-root.MuiStepIcon-completed': {
        color: theme.palette.success.main,
      },
    },
    statusInactive: {
      color: theme.palette.error.main,
      fontWeight: 'bold',
      marginLeft: theme.spacing(2),
    },
    statusActive: {
      color: theme.palette.success.main,
      fontWeight: 'bold',
      marginLeft: theme.spacing(2),
    },
    settingsPreviewContainer: {
      height: '100vh',
      maxHeight: '700px',
      backgroundColor: '#fff',
      padding: theme.spacing(3),
      borderRadius: 16,
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
    },
    settingsPreviewIframe: {
      width: '100%',
      height: '100%',
      border: 'none',
      borderRadius: 8,
    },
    settingsPreviewTitle: {
      marginRight: 'auto',
      fontWeight: 600,
      fontSize: '1.25rem',
    },
    settingsPreviewBody: {
      boxSizing: 'border-box',
      padding: theme.spacing(2),
      height: '100%',
    },
    settingsPreviewDarkMode: {
      backgroundColor: theme.palette.grey['900']
    },
    settingsHeader: {
      marginBottom: theme.spacing(4),
    },
    settingsTitle: {
      fontWeight: 700,
      fontSize: '1.75rem',
      marginBottom: theme.spacing(1),
    },
    settingsSubtitle: {
      color: theme.palette.text.secondary,
      maxWidth: 800,
    },
    settingsSectionTitle: {
      fontWeight: 600,
      fontSize: '1.25rem',
      marginBottom: theme.spacing(1),
      color: theme.palette.primary.main,
    },
    settingsSectionDescription: {
      color: theme.palette.text.secondary,
      marginBottom: theme.spacing(2),
    },
    settingsCard: {
      marginBottom: theme.spacing(4),
    },
    switchLabel: {
      fontWeight: 500,
    },
    switchDescription: {
      marginTop: theme.spacing(1),
      color: theme.palette.text.secondary,
    },
    helpLink: {
      color: theme.palette.primary.main,
      textDecoration: 'none',
      fontWeight: 500,
      '&:hover': {
        textDecoration: 'underline',
      },
    },
    infoIcon: {
      fontSize: '1.25rem',
      marginLeft: theme.spacing(1),
      color: theme.palette.primary.main,
    },
  }),
);
