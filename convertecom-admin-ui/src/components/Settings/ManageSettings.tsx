import React, { useCallback, useContext, useEffect, useState } from 'react';
import {
  Box,
  Button,
  FormControl as CoreFormControl,
  Grid,
  InputAdornment,
  InputLabel,
  Paper,
  Select,
  Step,
  StepLabel,
  Stepper,
  Typography,
} from '@material-ui/core';
import { get, merge, isEqual } from 'lodash';
import { useSettingsStyles } from './settings-styles';
import { FormControl } from '../Form';
import { getTenantByToken, updateTenantByToken, updateTenantFallbackCoupon } from '../../services/TenantService';
import LoadingProgress from '../LoadingProgress';
import AccountSettings from './AccountSettings';
import { setupShopifyBilling } from '../../services/BillingService';
import { downloadOptinsCsv } from '../../services/OptinService';
import { getEmailLists } from '../../services/EmailService';
import SystemError from '../Errors/SystemError';
import InfoIcon from '@material-ui/icons/Info';
// import WarningMessage from './components/WarningMessage'
import StatusSection from './components/Status';
import BillingSection from './components/Billing';
import MountMessage from './components/MountMessage';
import defaultValues, { defaultSettingsValues } from './defaultTemplate';
import { initialOnboardingStatus, OnboardingStatus } from '../../interfaces/onboarding-status.interface';
import { SettingsPreview, SettingsPreviewComponentType } from './SettingsPreview';
import { GlobalContext } from '../../contexts/global.context';
import { LoadingContext } from '../../contexts/loading.context';
import { isErrorResponse } from '../../services/Service';

interface BillingCredentials {
  platform: string;
  externalId: string;
}

interface EmailPlatformCredentials {
  platform: string;
  apiKey: string;
  listId: string;
  startDate?: string;
}

interface Values {
  couponPercentage: string;
  minimumGrossPercentage: string;
  reservationMinutes: number;
  fallbackPercentage: string;
  fallbackCouponMaxPercentage: string;
  emailPlatformCredentials: EmailPlatformCredentials;
  billingCredentials: BillingCredentials;
}


function onboardingStepsCompleted(status: OnboardingStatus) {
  const {
    isBillingSetup,
    arePopupsCustomized,
    isCodeSnippetAdded,
    isConvertEcomActivated,
  } = status;

  if (
    isBillingSetup &&
    arePopupsCustomized &&
    isCodeSnippetAdded &&
    isConvertEcomActivated
  ) {
    return 4;
  } else if (isBillingSetup && arePopupsCustomized && isCodeSnippetAdded) {
    return 3;
  } else if (isBillingSetup && arePopupsCustomized) {
    return 2;
  } else if (isBillingSetup) {
    return 1;
  }
  return 0;
}

function defaultEmailPlatformCredentials(): EmailPlatformCredentials {
  return {
    platform: '',
    apiKey: '',
    listId: '',
    startDate: '',
  };
}

export default (props: any) => {
  const classes = useSettingsStyles();

  const [loadError, setLoadError] = useState('');
  const [loadErrorCode, setLoadErrorCode] = useState(500);
  const [errors, setErrors] = useState([]);
  const [values, setValues] = useState(defaultValues);
  const [settingsValues, setSettingsValues] = useState(defaultSettingsValues);
  const [emailPlatformCredentials, setEmailPlatformCredentials] = useState<EmailPlatformCredentials>(defaultEmailPlatformCredentials());
  const [active, setActive] = useState(false);
  const [requireEmailProperties, setRequireEmailProperties] = useState(false);
  const [showDownloadError, setShowDownloadError] = useState(false);
  const [emailLists, setEmailLists] = useState([{ id: '', name: 'None' }]);
  const [shopifyUrl, setShopifyUrl] = useState('');
  const [setupComplete, setSetupComplete] = useState(false);
  const [stepsCompleted, setStepsCompleted] = useState(0);
  const {
    isLoading,
    setIsLoading } = useContext(LoadingContext);
  const {
    setTenantSetupComplete,
    updateStatus,
  } = useContext(GlobalContext);
  const [onboardingStatus, setOnboardingStatus] = useState<OnboardingStatus>(
    initialOnboardingStatus,
  );
  const [showPreview, setShowPreview] = useState(false);
  const [templatePreview, setTemplatePreview] = useState(values.template);
  const [previewComponent, setPreviewComponent] = useState<
    SettingsPreviewComponentType
  >('mainPopUp');

  const loadEmailLists = useCallback(
    async () => {
      if (
        !emailPlatformCredentials ||
        !emailPlatformCredentials.platform ||
        !emailPlatformCredentials.apiKey ||
        isLoading
      ) {
        return;
      }

      const result = await getEmailLists(
        emailPlatformCredentials.platform,
        emailPlatformCredentials.apiKey,
      );
      if (result.length === 0) {
        setEmailLists([{ id: '', name: 'None' }]);
      } else {
        setEmailLists(result);
        setEmailPlatformCredentials(v => ({
          ...v,
          listId: v.listId ? v.listId : result[0].id,
        }));
      }
    }, [emailPlatformCredentials, isLoading]
  );

  /**
   * Zing is moving to use rgba(x, x, x, x) for the opt in popup background.
   */
  function convertHexToRgbaDefault(hex: string | undefined, defaultValue: string) {
    if (!hex) {
      return defaultValue;
    }
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
        a: 0.6
      }
      : defaultValue;
  }


  useEffect(() => {
    setTemplatePreview(values.template);
  }, [values.template]);

  // Load existing tenant configuration
  useEffect(() => {
    setIsLoading(true);
    shopifyAdminUrl();

    getTenantByToken()
      .then(response => {
        if (isErrorResponse(response)) {
          throw new Error(response.status.toString());
        }
        setRequireEmailProperties(
          !!get(response, 'data.emailPlatformCredentials.platform'),
        );
        setOnboardingStatus(response.data.onboardingStatus);
        const completedSteps = onboardingStepsCompleted(
          response.data.onboardingStatus,
        );
        setStepsCompleted(completedSteps);

        if (completedSteps === 4) {
          updateStatus(true);
          setSetupComplete(true);
        } else {
          setTenantSetupComplete(false);
        }

        const templateSetup = response.data.template !== null;

        // Don't override default template if null
        if (!templateSetup) {
          response.data.template = values.template;
        }

        const emailPlatformCredentials = get(
          response,
          'data.emailPlatformCredentials',
          {},
        );

        const optinBackground = get(response, 'data.template.optInBackground', {} as {
          text: string;
          color: string;
          colorRgba: {
            r: number;
            g: number;
            b: number;
            a: number;
          };
        });
        if (!optinBackground.colorRgba) {
          const colorRgba = convertHexToRgbaDefault(optinBackground.color, '#000000');
          if (typeof colorRgba !== 'string') {
            optinBackground.colorRgba = colorRgba;
          }
        }

        const updated = {
          ...response.data,
          ...{
            template: {
              ...response.data.template,
              optInBackground: {
                ...optinBackground
              }
            }
          },
          couponPercentage: (parseFloat(response.data.couponPercentage) * 100).toString(),
          minimumGrossPercentage: (parseFloat(response.data.minimumGrossPercentage) * 100).toString(),
          fallbackPercentage: response.data.fallbackCoupon
            ? (response.data.fallbackCoupon.percentage * 100).toString()
            : '0',
          fallbackCouponMaxPercentage: response.data.fallbackCouponMaxPercentage
            ? (response.data.fallbackCouponMaxPercentage * 100).toString()
            : '0',
        };
        const shouldUpdateValues = !isEqual(updated, values);
        if (shouldUpdateValues) {
          setValues({
            ...response.data,
            reservationMinutes: Number(response.data.reservationMinutes ?? 0), // ensure it's a number
            template: {
              ...response.data.template,
              optInBackground: {
                ...optinBackground
              }
            },
            couponPercentage: (parseFloat(response.data.couponPercentage) * 100).toString(),
            minimumGrossPercentage: (parseFloat(response.data.minimumGrossPercentage) * 100).toString(),
            fallbackPercentage: response.data.fallbackCoupon
              ? (response.data.fallbackCoupon.percentage * 100).toString()
              : '0',
            fallbackCouponMaxPercentage: response.data.fallbackCouponMaxPercentage
              ? (response.data.fallbackCouponMaxPercentage * 100).toString()
              : '0',
          });
          setSettingsValues({
            couponPercentage: (parseFloat(response.data.couponPercentage) * 100).toString(),
            fallbackPercentage: response.data.fallbackCoupon
              ? (response.data.fallbackCoupon.percentage * 100).toString()
              : '0',
            fallbackCouponMaxPercentage: response.data.fallbackCouponMaxPercentage
              ? (response.data.fallbackCouponMaxPercentage * 100).toString()
              : '0',
            reservationMinutes: response.data.reservationMinutes,
            minimumGrossPercentage: (parseFloat(response.data.minimumGrossPercentage) * 100).toString(),
          })
          setEmailPlatformCredentials({
            ...defaultEmailPlatformCredentials(),
            ...emailPlatformCredentials
          })
        }
        setActive(response.data.active);
        setIsLoading(false);
      })
      .catch(err => {
        setIsLoading(false);
        setLoadError(`A network error occurred. Please try again later.`);
        setLoadErrorCode(get(err, 'response.status', 500));
      });
  }, [setIsLoading, setTenantSetupComplete, updateStatus, values, values.template]);

  useEffect(() => {
    if (!isLoading) {
      loadEmailLists();
    }
  }, [isLoading, loadEmailLists]);

  useEffect(() => {
    const newStepsCompleted = onboardingStepsCompleted(onboardingStatus);
    setStepsCompleted(newStepsCompleted);
    if (newStepsCompleted === 4) {
      setSetupComplete(true);
    }
  }, [onboardingStatus]);

  async function handleSubmit(e: any) {
    e.preventDefault();

    const couponResponse = await updateTenantFallbackCoupon({
      percentage: parseFloat(values.fallbackPercentage) / 100,
    });

    const tenantData: any = {
      couponPercentage: parseFloat(settingsValues.couponPercentage) / 100,
      fallbackCouponMaxPercentage: parseFloat(settingsValues.fallbackCouponMaxPercentage) / 100,
      minimumGrossPercentage: parseFloat(settingsValues.minimumGrossPercentage) / 100,
      reservationMinutes: Number(settingsValues.reservationMinutes),
      emailPlatformCredentials: { ...emailPlatformCredentials },
    };

    const tenantResponse = await updateTenantByToken(tenantData);

    if (tenantResponse.errors) {
      const errors = merge(couponResponse.errors, tenantResponse.errors);
      setErrors(errors);
    } else {
      window.location.href = '/settings';
    }
  }

  const handleChangeSettings = (field: string) => (
    event: React.ChangeEvent<
      HTMLInputElement | { name?: string | undefined; value: unknown }
    >,
  ) => {
    setSettingsValues({ ...settingsValues, [field]: event.target.value });
  };

  const handleEmailPlatformCredentials = (prop: string) => (event: React.ChangeEvent<
    HTMLInputElement | { name?: string | undefined; value: unknown }
  >,) => {
    // If email platform changed, make other properties required
    if (prop === 'platform') {
      setRequireEmailProperties(event.target.value !== '');
      setEmailPlatformCredentials({
        ...emailPlatformCredentials,
        platform: event.target.value as string,
        apiKey: '',
        listId: '',
      });
      setEmailLists([{ id: '', name: 'None' }]);
    } else {
      setEmailPlatformCredentials({ ...emailPlatformCredentials, [prop]: event.target.value });
    }
  }

  async function handleActiveToggle(e: any) {
    const status = e.target.checked;
    setIsLoading(true);
    try {
      await updateTenantByToken({ active: e.target.checked });
      if (!onboardingStatus.isConvertEcomActivated && status === true) {
        await updateOnboardingStatus({ isConvertEcomActivated: true });
      }
      setActive(status);
    } catch (err) {
      setErrors(err.errors);
    }
    setIsLoading(false);
  }

  async function handleBillingSetupButton() {
    setIsLoading(true);

    try {
      const response = await setupShopifyBilling();
      if (response.redirectUrl) {
        window.location.href = response.redirectUrl;
      }
    } catch (err) {
      console.log(err);
    }
    setIsLoading(false);
  }

  async function handleOptInDownload() {
    try {
      const file = await downloadOptinsCsv();
      if (!file) {
        throw new Error('No opt-ins to download');
      }
      const csvContent = 'data:text/csv;charset=utf-8,' + file;
      window.open(encodeURI(csvContent), '_blank');
    } catch (e) {
      setShowDownloadError(true);
      setTimeout(() => setShowDownloadError(false), 5000);
      setErrors(e);
    }
  }

  async function onApiKeyBlur() {
    await loadEmailLists();
  }

  async function updateOnboardingStatus(changes: Partial<OnboardingStatus>) {
    try {
      const newStatus = { ...onboardingStatus, ...changes };
      await updateTenantByToken({ onboardingStatus: newStatus });
      setOnboardingStatus(newStatus);
    } catch (e) {
      throw new Error();
    }
  }

  async function popupsCustomized() {
    try {
      await updateOnboardingStatus({ arePopupsCustomized: true });
    } catch (e) {
      console.error(e);
    }
  }

  async function codeSnippetAdded() {
    try {
      await updateOnboardingStatus({ isCodeSnippetAdded: true });
      setSetupComplete(true);
    } catch (e) {
      console.error(e);
    }
  }

  function shopifyAdminUrl() {
    let url = window.sessionStorage.getItem('shopifyAdminUrl');
    url = url ? url : '';
    setShopifyUrl(url);
  }

  function handlePreview(
    component: SettingsPreviewComponentType,
    template: typeof defaultValues.template,
  ) {
    setShowPreview(true);
    setTemplatePreview(template);
    setPreviewComponent(component);
  }

  const hideCancel = stepsCompleted !== 1;

  const steps = [
    'Setup Billing',
    'Customize Popups',
    'Insert Code Snippet',
    'Activate Zing',
  ];

  if (loadError) {
    return <SystemError text={loadError} />;
  }

  if (isLoading) {
    return <LoadingProgress />;
  }

  return (
    <>
      <div className={classes.settingsHeader}>
        <Typography variant="h4" className={classes.settingsTitle}>
          Settings
        </Typography>
        <Typography variant="subtitle1" className={classes.settingsSubtitle}>
          Configure your ConvertEcom integration to maximize your store's performance
        </Typography>
      </div>

      {stepsCompleted < 4 && (
        <Paper className={classes.paper} style={{ marginBottom: '32px' }}>
          <Box p={2}>
            <Typography variant="h6" gutterBottom>
              Setup Progress
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              Complete these steps to fully set up your ConvertEcom integration
            </Typography>
            <Stepper
              activeStep={stepsCompleted}
              alternativeLabel
              classes={{ root: classes.stepper }}
            >
              {steps.map(label => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>
        </Paper>
      )}

      {(setupComplete || stepsCompleted >= 3) && (
        <StatusSection
          active={active}
          handleActiveToggle={handleActiveToggle}
        />
      )}

      {(stepsCompleted === 0 ||
        !values.billingCredentials ||
        !values.billingCredentials.externalId) && (
          <BillingSection handleBillingSetupButton={handleBillingSetupButton} />
        )}

      {(setupComplete || stepsCompleted === 1) && (
        <AccountSettings
          tenant={values.template}
          hideCancel={hideCancel}
          onUpdate={popupsCustomized}
          onPreview={handlePreview}
        />
      )}

      {stepsCompleted === 2 && (
        <MountMessage
          shopifyUrl={shopifyUrl}
          shopifyAdminUrl={shopifyAdminUrl}
          onSubmit={codeSnippetAdded}
        />
      )}

      {setupComplete && (
        <Grid container spacing={4} className={classes.settingsCard}>
          <Grid item xs={12}>
            <Typography variant="h5" component="h2" className={classes.settingsSectionTitle}>
              Configuration Settings
            </Typography>
            <Typography variant="body1" component="p" className={classes.settingsSectionDescription}>
              Configure ConvertEcom settings for your store
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <Paper className={classes.paper}>
              <form
                className={classes.container}
                noValidate
                autoComplete="off"
                onSubmit={handleSubmit}
              >
                <Grid container justifyContent="center" spacing={4}>
                  <Grid item xs={12} md={3}>
                    <div style={{ display: 'flex' }}>
                      <Typography
                        variant="h6"
                        component="h6"
                        color="primary"
                        gutterBottom
                      >
                        Zing Settings
                      </Typography>
                      <a
                        target="_blank"
                        rel="noopener noreferrer"
                        href="https://rocketzing.zendesk.com/hc/en-us/articles/360037984072"
                      >
                        <InfoIcon color="primary" style={{ fontSize: '28px' }} />
                      </a>
                    </div>
                    <Typography variant="body2" component="p">
                      Configure Zing settings for your store.
                    </Typography>
                    <br />
                    <Typography variant="body2" component="p">
                      We’ve applied some suggested default settings. These have proven
                      to be the most effective starting settings for most of our
                      customers. We recommend starting with these settings and seeing
                      the results before modifying.
                    </Typography>
                    <br />
                    <Typography variant="body2" component="p">
                      Please review the tutorial videos for best practices.
                    </Typography>
                  </Grid>

                  <Grid item xs={12} md={7}>
                    <Paper className={classes.paper}>
                      <FormControl
                        value={settingsValues.couponPercentage}
                        name="couponPercentage"
                        label="Zing Discount Pool"
                        inputClass={classes.percentageInput}
                        endAdornment={
                          <InputAdornment position="end">%</InputAdornment>
                        }
                        onChange={handleChangeSettings('couponPercentage')}
                        hint="This is the amount set aside from Opted-In sales for your customers to pay forward to each other."
                        errors={errors}
                        variant="outlined"
                      />

                      {/* <small>
                <a href="#">Show Me</a>
              </small> */}

                      <FormControl
                        value={settingsValues.fallbackPercentage}
                        name="fallbackPercentage"
                        label="Minimum Zing Discount"
                        inputClass={classes.percentageInput}
                        endAdornment={
                          <InputAdornment position="end">%</InputAdornment>
                        }
                        onChange={handleChangeSettings('fallbackPercentage')}
                        hint="This is the lowest discount any Zing Opted-In customer will receive."
                        errors={errors}
                      />

                      <FormControl
                        value={settingsValues.fallbackCouponMaxPercentage}
                        name="fallbackCouponMaxPercentage"
                        label="Maximum Zing Discount"
                        inputClass={classes.percentageInput}
                        endAdornment={
                          <InputAdornment position="end">%</InputAdornment>
                        }
                        onChange={handleChangeSettings('fallbackCouponMaxPercentage')}
                        hint="This is the highest discount any Zing Opted-In customer will receive."
                        errors={errors}
                      />

                      {/* <FormControl
                  valu{settingsValues.minimumGrossPercentage}"
                name="minimumGrossPercentage"
                label="Minimum Gross Percentage"
                inputClass={classes.percentageInput}
                endAdornment={<InputAdornment position="end">%</InputAdornment>}
                value={values.minimumGrossPercentage}
                onChange={handleChangeSettings('minimumGrossPercentage')}
                hint={
                  "The portion of a customer's subtotal that cannot be discounted by a Zing coupon (ie: if 80%, a cart subtotal must be at least $10 to qualify for a $2 coupon)"
                }
                errors={errors}
              /> */}

                      <FormControl
                        value={settingsValues.reservationMinutes}
                        name="reservationMinutes"
                        label="Zing Timeout Duration"
                        inputClass={classes.percentageInput}
                        endAdornment={
                          <InputAdornment position="end">min</InputAdornment>
                        }
                        onChange={handleChangeSettings('reservationMinutes')}
                        hint="The length of time a Zing is reserved for an order. After this time expires, your customer will have the opportunity to Try Again."
                        errors={errors}
                      />

                      <Grid item xs={12}>
                        <Button
                          variant="contained"
                          href="/settings"
                          className={classes.formFooterButton}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="contained"
                          color="primary"
                          type="submit"
                          className={classes.formFooterButton}
                        >
                          Save
                        </Button>
                      </Grid>
                    </Paper>
                  </Grid>
                </Grid>

                <Grid container justifyContent="center" spacing={4}>
                  <Grid item xs={12} md={3}>
                    <div style={{ display: 'flex' }}>
                      <Typography
                        variant="h6"
                        component="h6"
                        color="primary"
                        gutterBottom
                      >
                        Email Platform Integration
                      </Typography>
                      <a
                        target="_blank"
                        rel="noopener noreferrer"
                        href="https://rocketzing.zendesk.com/hc/en-us/articles/360038328391"
                      >
                        <InfoIcon color="primary" style={{ fontSize: '28px' }} />
                      </a>
                    </div>
                    <Typography variant="body2" component="p" gutterBottom>
                      Add an integration with a third-party email platform to
                      automatically consume new opt-in email addresses.
                    </Typography>
                    <Button
                      variant="contained"
                      color="primary"
                      type="button"
                      onClick={() => handleOptInDownload()}
                    >
                      Download Opt-In CSV
                    </Button>
                    <Typography
                      variant="body2"
                      component="p"
                      style={{
                        opacity: showDownloadError ? 1 : 0,
                        color: 'red',
                        transition: 'opacity 0.5s ease',
                      }}
                    >
                      Could not download opt-in csv. Either there was an error, or
                      there are no current opt-ins to download.
                    </Typography>
                  </Grid>

                  <Grid item xs={12} md={7}>
                    <Paper className={classes.paper}>
                      <CoreFormControl className={classes.formControl} fullWidth>
                        <InputLabel htmlFor="platform" shrink={true}>
                          Platform
                        </InputLabel>
                        <Select
                          native
                          onChange={handleEmailPlatformCredentials(
                            'platform'
                          )}
                          value={emailPlatformCredentials.platform}
                          inputProps={{
                            name: 'platform',
                            id: 'platform',
                          }}
                        >
                          <option value="">None</option>
                          <option value="klaviyo">Klaviyo</option>
                          <option value="mailchimp">Mailchimp</option>
                        </Select>
                      </CoreFormControl>

                      <FormControl
                        type="password"
                        name="apiKey"
                        label="Private API Key"
                        onChange={handleEmailPlatformCredentials('apiKey')}
                        onBlur={onApiKeyBlur}
                        value={emailPlatformCredentials.apiKey}
                        errors={errors}
                        required={requireEmailProperties}
                      />

                      <small>
                        <a href="#" onClick={() => loadEmailLists()}>
                          Refresh Lists
                        </a>
                      </small>

                      <CoreFormControl className={classes.formControl} fullWidth>
                        <InputLabel htmlFor="listId" shrink={true}>
                          Email List
                        </InputLabel>
                        <Select
                          native
                          onChange={handleEmailPlatformCredentials(
                            'listId'
                          )}
                          value={emailPlatformCredentials.listId}
                          inputProps={{
                            name: 'listId',
                            id: 'listId',
                          }}
                        >
                          {emailLists.map(list => (
                            <option value={list.id} key={list.id}>
                              {list.name}
                            </option>
                          ))}
                        </Select>
                      </CoreFormControl>

                      <Grid item xs={12}>
                        <Button
                          variant="contained"
                          href="/settings"
                          className={classes.formFooterButton}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="contained"
                          color="primary"
                          type="submit"
                          className={classes.formFooterButton}
                        >
                          Save
                        </Button>
                      </Grid>
                    </Paper>
                  </Grid>
                </Grid>
              </form>
            </Paper>
          </Grid>
        </Grid>
      )}
      <SettingsPreview
        isShown={showPreview}
        template={templatePreview}
        title="Preview"
        onClose={() => setShowPreview(!showPreview)}
        component={previewComponent}
      />
    </>
  );
};
