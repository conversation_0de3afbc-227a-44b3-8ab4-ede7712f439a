export default {
  couponPercentage: '0',
  fallbackCouponMaxPercentage: '0',
  minimumGrossPercentage: '0',
  reservationMinutes: 0,
  fallbackPercentage: '0',
  emailPlatformCredentials: {
    platform: '',
    apiKey: '',
    listId: '',
    startDate: '',
  },
  billingCredentials: {
    platform: null,
    externalId: null,
  },
  template: {
    popupDelay: 3000,
    optInBackground: {
      text: '',
      color: '#000000',
      colorRgba: {r: 0, g: 0, b: 0, a: 0.6},
    },
    cartBackground: {
      text: '',
      color: '#565656'
    },
    mainHeader: {
      text: 'SOMEONE JUST PAID IT FORWARD TO YOU!',
      color: '#ffffff',
    },
    mainSubHeader: {
      text: 'That means there is money waiting for you to spend!',
      color: '#ffffff',
    },
    optIn: {
      text: 'ENTER YOUR EMAIL ADDRESS TO CHOOSE A MONEY BAG',
      color: '#ffffff',
    },
    optInButton: {
      text: 'SUBMIT',
      color: '#ffffff',
      backgroundColor: '#000000',
    },
    optOutLink: {
      text: 'NO THANKS',
      color: '#ffffff',
    },
    optOutExtra: {
      text: ', pay it forward to someone else',
      color: '#ffffff',
    },
    optInResultHeader: {
      text: 'So exciting!',
      color: '#ffffff',
    },
    optInResult: {
      text: `You'll find out what you got in your cart!`,
      color: '#ffffff',
    },
    optInResultLink: {
      text: 'SHOP NOW',
      color: '#ffffff',
    },
    cart: {
      text: 'YOUR PAY IT FORWARD GIFT IS WAITING FOR YOU!',
      color: '#ffffff',
    },
    cartButton: {
      text: 'SEE WHAT YOU GOT',
      color: '#ffffff',
      backgroundColor: '#000000',
    },
    cartAmount: {
      text: 'YOUR PAY IT FORWARD GIFT OF $%AMOUNT% IS WAITING FOR YOU!',
      color: '#ffffff',
    },
    cartTime: {
      text: 'You have %TIME% to complete checkout before your gift is given to someone else.',
      color: '#ffffff',
    },
    cartExpired: {
      text: 'YOUR PAY IT FORWARD GIFT HAS EXPIRED!',
      color: '#ffffff',
    },
    cartExpiredButton: {
      text: 'TRY AGAIN',
      color: '#ffffff',
      backgroundColor: '#000000',
    },
    cartInvalid: {
      text: 'YOUR PAY IT FORWARD GIFT WAS RESET BECAUSE YOUR CART CHANGED!',
      color: '#ffffff',
    },
    cartInvalidButton: {
      text: 'TRY AGAIN',
      color: '#ffffff',
      backgroundColor: '#000000',
    },
    cartPopupPickOne: {
      text: 'PICK ONE!',
      color: '#ffffff',
    },
    cartPopupHeader: {
      text: 'Congratulations!',
      color: '#ffffff',
    },
    cartPopupAmount: {
      text: 'A discount of $%AMOUNT% has been added to your cart!',
      color: '#ffffff',
    },
    cartPopupTime: {
      text: 'You have %TIME% to complete checkout before your discount is given to someone else.',
      color: '#ffffff',
    },
    cartPopupLink: {
      text: 'CONTINUE',
      color: '#ffffff',
    },
    cartPopupIconGradient: {
      text: '',
      color: '#ffffff',
      backgroundColor: '#888888',
    },
    cartPopupCustomImage: {
      text: '',
    }
  }
}

export const defaultSettingsValues = {
  couponPercentage: '0',
  fallbackPercentage: '0',
  fallbackCouponMaxPercentage: '0',
  reservationMinutes: 0,
  minimumGrossPercentage: '0',
}

export const emailPlatformCredentials = {
  platform: '',
  apiKey: '',
  listId: '',
  startDate: '',
}
