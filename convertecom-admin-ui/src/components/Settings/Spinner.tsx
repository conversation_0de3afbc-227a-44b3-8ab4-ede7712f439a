import * as React from 'react';

const Spinner = ({ className, size = 100 }: { className: string, size?: number }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 50 50"
    width={`${size}px`}
    height={`${size}px`}
  >
    <circle cx="25" cy="25" r="9.64557" fill="none" stroke="#3F52B5" strokeWidth="2" >
      <animate
        attributeName="r"
        calcMode="spline"
        values="0;20"
        keyTimes="0;1"
        dur="1.5"
        keySplines="0 0.2 0.8 1"
        begin="-0.75s"
        repeatCount="indefinite"
      />
      <animate
        attributeName="opacity"
        calcMode="spline"
        values="1;0"
        keyTimes="0;1"
        dur="1.5"
        keySplines="0.2 0 0.8 1"
        begin="-0.75s"
        repeatCount="indefinite"
      />
    </circle>
    <circle cx="25" cy="25" r="18.5184" fill="none" stroke="#ED227E" strokeWidth="2">
      <animate
        attributeName="r"
        calcMode="spline"
        values="0;20"
        keyTimes="0;1"
        dur="1.5"
        keySplines="0 0.2 0.8 1"
        begin="0s"
        repeatCount="indefinite"
      />
      <animate
        attributeName="opacity"
        calcMode="spline"
        values="1;0"
        keyTimes="0;1"
        dur="1.5"
        keySplines="0.2 0 0.8 1"
        begin="0s"
        repeatCount="indefinite"
      />
    </circle>
  </svg>
);

export default Spinner;
