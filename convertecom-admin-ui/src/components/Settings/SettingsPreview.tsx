import React, { FunctionComponent, useEffect, useRef, useState } from 'react';
import * as ReactDOM from 'react-dom';
import defaultTemplate from './defaultTemplate';
import { useSettingsStyles } from './settings-styles';
import { Typography } from '@material-ui/core';
import Box from '@material-ui/core/Box';
import Button from '@material-ui/core/Button';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Switch from '@material-ui/core/Switch';
import Dialog from '@material-ui/core/Dialog';
import DialogTitle from '@material-ui/core/DialogTitle';
import DialogContent from '@material-ui/core/DialogContent';

export type SettingsPreviewComponentType =
  | 'mainPopUp'
  | 'optInSuccess'
  | 'cartSeeWhatYouGot'
  | 'cartGiftWaiting'
  | 'cartExpired'
  | 'couponInvalid'
  | 'couponPicker'
  | 'icon';

export interface SettingsPreviewProps {
  isShown: boolean;
  template: typeof defaultTemplate.template;
  title: string;
  component: SettingsPreviewComponentType;
  onClose: () => void;
}

export const SettingsPreview = (props: SettingsPreviewProps) => {
  const styles = useSettingsStyles();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [iframeLoaded, setIframeLoaded] = useState(false);
  const previewIframe = useRef<HTMLIFrameElement>(null);
  const boxClasses = isDarkMode
    ? `${styles.settingsPreviewContainer} ${styles.settingsPreviewDarkMode}`
    : styles.settingsPreviewContainer;
  const previewOrigin: string = typeof process.env.REACT_APP_PREVIEW_ORIGINS !== 'undefined'
    ? process.env.REACT_APP_PREVIEW_ORIGINS
    : 'http://localhost:8080';

  useEffect(() => {
    if (
      iframeLoaded &&
      previewIframe.current &&
      previewIframe.current.contentWindow
    ) {
      // Due to the build setup, we have to post to each valid origin
        // Assert not null because we checked above
        previewIframe.current!.contentWindow!.postMessage(
          {
            type: 'updatePreview',
            component: props.component,
            payload: props.template,
          },
          previewOrigin,
        );
    }
  }, [props.template, iframeLoaded, props.component, previewOrigin]);
  useEffect(() => {
    if (!props.isShown) {
      setIframeLoaded(false);
    }
  }, [props.isShown]);
  return (
    <>
      <Dialog
        fullWidth={true}
        maxWidth="lg"
        open={props.isShown}
        onClose={props.onClose}
      >
        <DialogTitle id="zing-preview-dialog-title">
          <Box display="flex" justifyContent="space-between">
            <Typography
              variant="h6"
              component="h2"
              className={styles.settingsPreviewTitle}
            >
              {props.title}
            </Typography>
            <div>
              <FormControlLabel
                control={
                  <Switch
                    onChange={() => setIsDarkMode(!isDarkMode)}
                    checked={isDarkMode}
                  />
                }
                label="Dark Mode"
              />
              <Button color="inherit" onClick={props.onClose}>
                Close
              </Button>
            </div>
          </Box>
        </DialogTitle>
        <DialogContent className={boxClasses} dividers>
          <div className={styles.settingsPreviewBody}>
            <iframe
              src={`${previewOrigin}/preview.html`}
              className={styles.settingsPreviewIframe}
              ref={previewIframe}
              onLoad={() => setIframeLoaded(true)}
              title="Preview"
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
