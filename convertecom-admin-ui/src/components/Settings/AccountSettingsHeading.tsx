import * as React from 'react';
import InfoLink, { InfoLinkProps } from './InfoLink';
import { FunctionComponent } from 'react';
import Box from '@material-ui/core/Box';
import Button from '@material-ui/core/Button';
import VisibilityIcon from '@material-ui/icons/Visibility';

export interface AccountSettingsHeadingProps extends InfoLinkProps {
  heading: string;
  onShowPreview?: () => void;
}

export const AccountSettingsHeading: FunctionComponent<
  AccountSettingsHeadingProps
> = props => {
  const onShowPreview = props.onShowPreview;
  return (
    <Box
      display="flex"
      flexDirection="row"
      justifyContent="space-between"
      alignItems="flex-end"
    >
      <InfoLink link={props.link} topGutter={props.topGutter}>
        {props.heading}
      </InfoLink>
      {typeof onShowPreview !== 'undefined' ? (
        <Button
          onClick={onShowPreview}
          endIcon={<VisibilityIcon />}
          color="primary"
        >
          Preview
        </Button>
      ) : null}
    </Box>
  );
};

export default AccountSettingsHeading;
