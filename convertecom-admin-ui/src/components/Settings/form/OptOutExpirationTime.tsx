import * as React from 'react';
import AccountSettingsHeading from '../AccountSettingsHeading';
import {
  createStyles,
  Grid,
  InputAdornment,
  Switch,
  Theme,
  Typography,
} from '@material-ui/core';
import { FormControl } from '../../Form';
import { makeStyles } from '@material-ui/core/styles';
import { APIError } from '../../../services/Service';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    input: {
      width: '8rem'
    },
  }),
);

export const SessionExpiry = () => (
  <Typography variant="body2" component="p">
    Show the popup after the browsing session is finished (user exits browser).
  </Typography>
);

export interface OptOutExpirationTimeProps {
  onChange: (optOutTtl: number) => void;
  optOutTtl?: number;
}

export const OptOutExpirationTime = (props: OptOutExpirationTimeProps) => {
  const { onChange, optOutTtl } = props;
  const hasCustomExpiry = !!optOutTtl;
  const inputRef = React.useRef<HTMLInputElement>();

  const classes = useStyles();
  // Hold the form state
  const [optOutHours, setOptOutHours] = React.useState(0);
  const [errors, setErrors] = React.useState<APIError[]>([]);

  function handleInputBlur() {
    if (!inputRef.current) {
      return;
    }
    const { value } = inputRef.current;
    const hours = Number.isNaN(Number(value)) ? 0 : Number(value);
    if (hours < 1 || hours > 10000) {
      setErrors([{field: 'optOutTtl', messages: ['Delay can be between 1 and 10,000 hours.']}])
      inputRef.current.value = optOutHours.toString();
    } else {
      setErrors([]);
      const hoursAsSeconds = hours * 60 * 60;
      setOptOutHours(hours);
      onChange(hoursAsSeconds);
    }
  }

  function handleTimeoutTypeChange() {
    // If there is a custom expiry, unset it
    if (hasCustomExpiry) {
      onChange(0);
    } else {
      // set it to the current opt out hours, default to one day
      onChange((optOutHours || 24) * 60 * 60);
    }
  }

  React.useEffect(() => {
    if (typeof optOutTtl !== 'undefined' && optOutTtl > 0 && inputRef.current) {
      inputRef.current.value = Math.floor(optOutTtl / 60 / 60).toString();
      // console.log('optoutttl', optOutTtl);
      // // Convert seconds to hours
      // setOptOutHours(Math.floor(optOutTtl / 60 / 60));
    }
  }, [optOutTtl]);

  return (
    <>
      <AccountSettingsHeading heading="Opt-out Expiration Time" topGutter />

      <Grid container spacing={4} style={{ margin: '0 0 15px' }}>
        <Switch
          checked={hasCustomExpiry}
          onChange={handleTimeoutTypeChange}
          value="true"
          color="primary"
        />
        <Typography
          variant="body2"
          component="p"
          gutterBottom
          style={{ marginTop: 10 }}
        >
          Use a custom expiration length
        </Typography>
        {!hasCustomExpiry ? (
          <SessionExpiry />
        ) : (
          <FormControl
            name="optOutTtl"
            label="Delay in hours"
            inputClass={classes.input}
            endAdornment={<InputAdornment position="end">hours</InputAdornment>}
            onBlur={handleInputBlur}
            variant="outlined"
            hint="The length of time after a user opts out to show the popup again, between 1 and 10,000 hours."
            inputProps={{ min: 1, max: 10000, step: 1, pattern: '\d+' }}
            inputRef={inputRef}
            errors={errors}
          />
        )}
      </Grid>
    </>
  );
};
