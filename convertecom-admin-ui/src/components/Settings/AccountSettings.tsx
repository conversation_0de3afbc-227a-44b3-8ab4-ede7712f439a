import React, { useRef, useState } from 'react';
import { Button, Grid, Paper, Switch, Typography } from '@material-ui/core';
import { useSettingsStyles } from './settings-styles';
import FormControlWithColor from '../Form/FormControlWithColor';
import { updateTenantByToken, uploadTenantImage } from '../../services/TenantService';
import { get } from 'lodash';
import Spinner from './Spinner';
import ImageUploader from 'react-images-upload';
import AccountSettingsHeading from './AccountSettingsHeading';
import { SettingsPreviewComponentType } from './SettingsPreview';
import defaultTemplate from './defaultTemplate';
import { FormControl } from '../Form';
import { OptOutExpirationTime } from './form/OptOutExpirationTime';
import { Template, TemplateValue } from '../../interfaces/template-value.interface';

const s3Url = 'https://zingpopupimages.s3.us-east-2.amazonaws.com/';

export interface AccountSettingsProps {
  onUpdate: () => Promise<void>;
  onPreview: (
    component: SettingsPreviewComponentType,
    template: typeof defaultTemplate.template,
  ) => void;
}


export default (props: any & AccountSettingsProps) => {
  const classes = useSettingsStyles();
  const [errors, setErrors] = useState([]);
  const [values, setValues] = useState(props.tenant as Template);
  const defaultCartBackground: TemplateValue = { text: '', color: '#565656' };
  const bannerBackground = get(values, 'cartBackground', defaultCartBackground);
  const defaultIconGradient = {
    text: '',
    color: '##00DD00',
    backgroundColor: '#009900',
  };
  const iconGradient = values.cartPopupIconGradient || defaultIconGradient;
  const hasCustomImage = !!get(values, 'cartPopupCustomImage.text', null);
  const [showIconUploader, setShowIconUploader] = useState(hasCustomImage);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [imgUploadError, setImgUploadError] = useState('');
  const imgUploaderRef = useRef(null);
  const { onUpdate = () => Promise.resolve() } = props;

  function handleShowPreview(component: SettingsPreviewComponentType) {
    props.onPreview(component, {
      ...values,
      ...(showIconUploader ? {} : { cartPopupCustomImage: { text: '' } }),
    });
  }

  async function handleSubmit(e: any) {
    e.preventDefault();

    const { popupDelay } = values;
    if (popupDelay === 0) {
      alert('Please enter a popup delay value.');
      return;
    }

    const template = {
      ...values,
      // if the user chose not to use a custom image, remove the file name reference
      ...(showIconUploader ? {} : { cartPopupCustomImage: { text: '' } }),
    };

    try {
      const result = await updateTenantByToken({ template });      

      if (result.errors) {
        // TODO
      } else {
        await onUpdate();
        window.sessionStorage.setItem(
          'mainSuccess',
          'Settings updated successfully.',
        );
        window.location.href = '/settings';
      }
    } catch (e) {
      console.error(e);
    }
  }

  async function handleIconFileChange(files: File[]) {
    const file = files[0];

    setImgUploadError('');
    setIsUploadingImage(true);
    try {
      const response = await uploadTenantImage(file);
      if (response.status > 201) {
        throw new Error(response.statusText || 'Upload failed');
      }
      setValues({
        ...values,
        cartPopupCustomImage: {
          text: response.filename,
        },
      });
    } catch (error) {
      setImgUploadError(error.message);
    }
    setIsUploadingImage(false);

    // clear the input field
    // @ts-ignore ref will not be null when this function fires
    imgUploaderRef.current.clearPictures();
  }

  function handlePopupCustomImageError() {
    // image upload failed, so remove the file name reference
    setValues({ ...values, cartPopupCustomImage: { text: '' } });
  }

  function setPopupDelay(e: React.ChangeEvent<HTMLInputElement>) {
    const { value } = e.target;
    const ms = parseInt(value, 10);

    setValues({ ...values, popupDelay: (Number.isNaN(ms) ? 0 : ms) * 1000 });
  }

  return (
    <Grid container justify="center" spacing={4}>
      <Grid item xs={12} md={3}>
        <Typography variant="h6" component="h6" color="primary" gutterBottom>
          Account Settings
        </Typography>
        <Typography variant="body2" component="p">
          Branding/Customization
        </Typography>
        <br />
        <Typography variant="body2" component="p">
          We’ve entered suggested content that our most successful customers are
          using. Feel free to modify these default settings to match the colors
          and text to your brand and style.
        </Typography>
        <br />
        <Typography variant="body2" component="p">
          To better understand where each section fits in the user flow, please
          see our{' '}
          <a
            target="_blank"
            rel="noopener noreferrer"
            href="https://rocketzing.zendesk.com/hc/en-us/articles/************"
          >
            setup guide
          </a>
          .
        </Typography>
      </Grid>

      <Grid item xs={12} md={7}>
        <Paper className={classes.paper}>
          <form
            className={classes.container}
            noValidate
            autoComplete="off"
            onSubmit={handleSubmit}
          >
            <AccountSettingsHeading
              link="https://rocketzing.zendesk.com/hc/en-us/articles/************"
              heading="Main Pop Up"
              onShowPreview={() => handleShowPreview('mainPopUp')}
            />
            <FormControlWithColor
              hideInput
              name="optInBackground"
              label="Opt-in Popup Background Color"
              value={values.optInBackground}
              setter={(o: TemplateValue) =>
                setValues({ ...values, optInBackground: o })
              }
              errors={errors}
              hasColorAlpha={true}
            />

            <FormControlWithColor
              name="mainHeader"
              label="Header"
              value={values.mainHeader}
              setter={(o: TemplateValue) =>
                setValues({ ...values, mainHeader: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="mainSubHeader"
              label="Sub-header"
              value={values.mainSubHeader}
              setter={(o: TemplateValue) =>
                setValues({ ...values, mainSubHeader: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="optIn"
              label="Opt-in"
              value={values.optIn}
              setter={(o: TemplateValue) => setValues({ ...values, optIn: o })}
              errors={errors}
            />

            <FormControlWithColor
              name="optInButton"
              label="Opt-in Button"
              value={values.optInButton}
              setter={(o: TemplateValue) =>
                setValues({ ...values, optInButton: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="optOutLink"
              label="Opt-out Link"
              value={values.optOutLink}
              setter={(o: TemplateValue) =>
                setValues({ ...values, optOutLink: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="optOutExtra"
              label="Opt-out Extra"
              value={values.optOutExtra}
              setter={(o: TemplateValue) =>
                setValues({ ...values, optOutExtra: o })
              }
              errors={errors}
            />

            <AccountSettingsHeading
              link="https://rocketzing.zendesk.com/hc/en-us/articles/************"
              heading="Opt-in Success Pop-up"
              onShowPreview={() => handleShowPreview('optInSuccess')}
              topGutter
            />
            <FormControlWithColor
              name="optInResultHeader"
              label="Header"
              value={values.optInResultHeader}
              setter={(o: TemplateValue) =>
                setValues({ ...values, optInResultHeader: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="optInResult"
              label="Body"
              value={values.optInResult}
              setter={(o: TemplateValue) =>
                setValues({ ...values, optInResult: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="optInResultLink"
              label="Link"
              value={values.optInResultLink}
              setter={(o: TemplateValue) =>
                setValues({ ...values, optInResultLink: o })
              }
              errors={errors}
            />

            <AccountSettingsHeading
              heading="Cart Display - See what you got"
              onShowPreview={() => handleShowPreview('cartSeeWhatYouGot')}
              topGutter
            />

            <FormControlWithColor
              hideInput
              name="cartBackground"
              label="Cart Banner Background Color"
              value={bannerBackground}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartBackground: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="cart"
              label="Cart"
              value={values.cart}
              setter={(o: TemplateValue) => setValues({ ...values, cart: o })}
              errors={errors}
            />

            <FormControlWithColor
              name="cartButton"
              label="Cart Button"
              value={values.cartButton}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartButton: o })
              }
              errors={errors}
            />

            <AccountSettingsHeading
              heading="Cart Display - Gift waiting"
              onShowPreview={() => handleShowPreview('cartGiftWaiting')}
              topGutter
            />

            <FormControlWithColor
              name="cartAmount"
              label="Cart amount won (use %AMOUNT% placeholder)"
              value={values.cartAmount}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartAmount: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="cartTime"
              label="Cart Time Left (use %TIME% placeholder)"
              value={values.cartTime}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartTime: o })
              }
              errors={errors}
            />

            <AccountSettingsHeading
              link="https://rocketzing.zendesk.com/hc/en-us/articles/************"
              heading="Cart Display - Coupon Expired"
              onShowPreview={() => handleShowPreview('cartExpired')}
              topGutter
            />

            <FormControlWithColor
              name="cartExpired"
              label="Cart Expired"
              value={values.cartExpired}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartExpired: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="cartExpiredButton"
              label="Cart Expired Retry Button"
              value={values.cartExpiredButton}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartExpiredButton: o })
              }
              errors={errors}
            />

            <AccountSettingsHeading
              link="https://rocketzing.zendesk.com/hc/en-us/articles/************"
              heading="Cart Display - Coupon Invalid"
              onShowPreview={() => handleShowPreview('couponInvalid')}
              topGutter
            />

            <FormControlWithColor
              name="cartInvalid"
              label="Cart Invalid"
              value={values.cartInvalid}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartInvalid: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="cartInvalidButton"
              label="Cart Invalid Retry Button"
              value={values.cartInvalidButton}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartInvalidButton: o })
              }
              errors={errors}
            />

            <AccountSettingsHeading
              link="https://rocketzing.zendesk.com/hc/en-us/articles/************"
              heading="Coupon Picker Popup"
              onShowPreview={() => handleShowPreview('couponPicker')}
              topGutter
            />

            <FormControlWithColor
              name="cartPopupPickOne"
              label="Cart Popup 'Pick one'"
              value={values.cartPopupPickOne}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartPopupPickOne: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="cartPopupHeader"
              label="Cart Popup Result Header"
              value={values.cartPopupHeader}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartPopupHeader: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="cartPopupAmount"
              label="Cart Popup Result Amount (use %AMOUNT% placeholder)"
              value={values.cartPopupAmount}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartPopupAmount: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="cartPopupTime"
              label="Cart Popup Result Time Left (use %TIME% placeholder)"
              value={values.cartPopupTime}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartPopupTime: o })
              }
              errors={errors}
            />

            <FormControlWithColor
              name="cartPopupLink"
              label="Cart Popup Result Link"
              value={values.cartPopupLink}
              setter={(o: TemplateValue) =>
                setValues({ ...values, cartPopupLink: o })
              }
              errors={errors}
            />

            <AccountSettingsHeading
              link="https://rocketzing.zendesk.com/hc/en-us/articles/************"
              heading="Popup Icon"
              onShowPreview={() => handleShowPreview('icon')}
              topGutter
            />

            <Grid container spacing={4} style={{ margin: '0 0 15px' }}>
              <Switch
                checked={showIconUploader}
                onChange={() => setShowIconUploader(!showIconUploader)}
                value="true"
                color="primary"
              />
              <Typography
                variant="body2"
                component="p"
                gutterBottom
                style={{ marginTop: 10 }}
              >
                Use a custom image
              </Typography>
            </Grid>

            {showIconUploader ? (
              <>
                {hasCustomImage && (
                  <img
                    src={`${s3Url}${values.cartPopupCustomImage.text}`}
                    onError={handlePopupCustomImageError}
                    alt=""
                    style={{ maxWidth: '100%', maxHeight: '300px' }}
                  />
                )}
                <div className={classes.uploadWrapper}>
                  <ImageUploader
                    ref={imgUploaderRef}
                    buttonText="Select an Image"
                    imgExtension={['.svg', '.png', '.jpg', '.gif']}
                    label="5mb Max (SVG, PNG, JPG, GIF)"
                    maxFileSize={5242880}
                    onChange={handleIconFileChange}
                    fileContainerStyle={{
                      boxShadow: 'none',
                    }}
                    buttonStyles={{
                      background: '#EEE',
                      borderRadius: '4px',
                      color: '#333',
                      fontWeight: '500',
                      textTransform: 'uppercase',
                    }}
                    singleImage
                    withPreview
                    withIcon
                  />
                  {imgUploadError && (
                    <Typography
                      variant="body2"
                      component="p"
                      className={classes.error}
                      gutterBottom
                    >
                      {imgUploadError}
                    </Typography>
                  )}
                  {isUploadingImage && (
                    <>
                      <div className={classes.uploadOverlay} />
                      <Spinner className={classes.uploadSpinner} />
                    </>
                  )}
                </div>
              </>
            ) : (
              <FormControlWithColor
                name="cartPopupIconGradient"
                label="Default Icon Color"
                hideInput
                value={iconGradient}
                setter={(o: TemplateValue) =>
                  setValues({ ...values, cartPopupIconGradient: o })
                }
                errors={errors}
              >
                <div
                  className={classes.gradientPreview}
                  style={{
                    background: `linear-gradient(${iconGradient.color}, ${iconGradient.backgroundColor})`,
                  }}
                />
              </FormControlWithColor>
            )}

            <AccountSettingsHeading heading="Popup Delay" topGutter />

            <Grid container spacing={4} style={{ margin: '0 0 15px' }}>
              <FormControl
                name="popupDelay"
                label="Delay in seconds"
                value={values.popupDelay === 0 ? '' : values.popupDelay / 1000}
                onChange={setPopupDelay}
                inputClass={classes.percentageInput}
                hint="The time to wait until the popup is shown, between 1 and 60 seconds"
                errors={errors}
                variant="outlined"
                inputProps={{ min: 1, max: 60, step: 1 }}
                type="number"
              />
            </Grid>

            <OptOutExpirationTime
              onChange={optOutTtl => {
                setValues({ ...values, optOutTtl });
              }}
              optOutTtl={values.optOutTtl}
            />

            <Grid item xs={12}>
              {props.hideCancel && (
                <Button
                  variant="contained"
                  href="/settings"
                  className={classes.formFooterButton}
                  disabled={isUploadingImage}
                >
                  Cancel
                </Button>
              )}
              <Button
                variant="contained"
                color="primary"
                type="submit"
                className={classes.formFooterButton}
                disabled={isUploadingImage}
              >
                Save
              </Button>
            </Grid>
          </form>
        </Paper>
      </Grid>
    </Grid>
  );
};
