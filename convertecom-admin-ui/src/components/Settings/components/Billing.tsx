import React from 'react'
import { useSettingsStyles } from '../settings-styles';
import {
	Paper,
	Typography,
	Button,
	Grid,
	Box,
	Divider,
	List,
	ListItem,
	ListItemIcon,
	ListItemText
} from '@material-ui/core';
import PaymentIcon from '@material-ui/icons/Payment';
import CheckCircleOutlineIcon from '@material-ui/icons/CheckCircleOutline';

export default (props: any) => {
	const classes = useSettingsStyles()
	const { handleBillingSetupButton } = props
	return (
		<Grid container spacing={4} className={classes.settingsCard}>
			<Grid item xs={12}>
				<Typography variant="h5" component="h2" className={classes.settingsSectionTitle}>
					Billing Setup
				</Typography>
				<Typography variant="body1" component="p" className={classes.settingsSectionDescription}>
					Connect ConvertEcom to your Shopify store for billing
				</Typography>
			</Grid>

			<Grid item xs={12}>
				<Paper className={classes.paper}>
					<Box display="flex" alignItems="center" mb={3}>
						<PaymentIcon color="primary" style={{ fontSize: 28, marginRight: 16 }} />
						<Typography variant="h6">
							Shopify Billing Integration
						</Typography>
					</Box>

					<Divider />

					<Box my={3}>
						<Typography variant="body1" paragraph>
							ConvertEcom charges a usage fee of 1.5% of cart subtotals that redeemed a ConvertEcom discount. This helps us provide you with the best service while ensuring our interests are aligned with your success.
						</Typography>

						<Typography variant="body1" paragraph>
							Benefits of using ConvertEcom:
						</Typography>

						<List>
							<ListItem dense>
								<ListItemIcon style={{ minWidth: 36 }}>
									<CheckCircleOutlineIcon color="primary" fontSize="small" />
								</ListItemIcon>
								<ListItemText primary="Increase your conversion rates" />
							</ListItem>
							<ListItem dense>
								<ListItemIcon style={{ minWidth: 36 }}>
									<CheckCircleOutlineIcon color="primary" fontSize="small" />
								</ListItemIcon>
								<ListItemText primary="Recover potentially lost revenue" />
							</ListItem>
							<ListItem dense>
								<ListItemIcon style={{ minWidth: 36 }}>
									<CheckCircleOutlineIcon color="primary" fontSize="small" />
								</ListItemIcon>
								<ListItemText primary="Build your email marketing list" />
							</ListItem>
							<ListItem dense>
								<ListItemIcon style={{ minWidth: 36 }}>
									<CheckCircleOutlineIcon color="primary" fontSize="small" />
								</ListItemIcon>
								<ListItemText primary="Pay only for successful conversions" />
							</ListItem>
						</List>
					</Box>

					<Box mt={4} display="flex" flexDirection="column" alignItems="flex-start">
						<Typography
							variant="body1"
							style={{ fontWeight: 'bold' }}
						>
							Follow the steps below to grant ConvertEcom permission to bill you monthly for your usage:
						</Typography>

						<Button
							variant="contained"
							color="primary"
							size="large"
							onClick={handleBillingSetupButton}
							startIcon={<PaymentIcon />}
						>
							Set up Shopify billing
						</Button>
					</Box>
				</Paper>
			</Grid>
		</Grid>
	)
}
