import React from 'react'
import { useSettingsStyles } from '../settings-styles';
import InfoIcon from '@material-ui/icons/Info';


export default (props: any) => {
  const classes = useSettingsStyles()
  const { showBillingSetupError, showTemplateSetupError, shopifyUrl, shopifyAdminUrl } = props
  return (
    <div className={classes.setupAlert}>
      <div className={classes.sectionTitles}>
        <strong className={classes.infoSpacing}>IMPORTANT:</strong>Before you start using Zing, please address the following:
        <a target="_blank" rel="noopener noreferrer" href="https://rocketzing.zendesk.com/hc/en-us/articles/************" className={classes.infoLinks}>
          <InfoIcon style={{ fontSize: '28px', color: 'white' }} />
        </a>
      </div>
      <ol>
        {showBillingSetupError &&
          <li>Set up your billing account (start by reviewing the "Billing" section below and click "Set Up Billing" button)</li>
        }
        {showTemplateSetupError &&
          <li>Save your Account Settings (confirm by clicking "Save" in the "Account Settings" section)</li>
        }
        <li>
          Ensure that you have updated your Cart template with the Zing banner (detailed instructions <a target="_blank" rel="noopener noreferrer" href="https://rocketzing.zendesk.com/hc/en-us/articles/************--How-do-I-complete-the-Zing-app-configuration-">here</a>)
          <ul>
            <li>From your {!!shopifyAdminUrl ? <a target="_blank" rel="noopener noreferrer" href={shopifyUrl}>Shopify admin portal</a> : <>Shopify admin portal</>}, select "Online Store" from the "SALES CHANNEL" sidebar</li>
            <li>Select "Themes" from the dropdown</li>
            <li>Look for the "Current theme" section and select "Actions" dropdown</li>
            <li>Select "Edit Code" from the dropdown</li>
            <li>Under the "Sections" folder, select the Cart file (usually named "cart-template.liquid")</li>
            <li>Insert  <strong>{`<div id="convertecom-reservation-container"></div>`}</strong>  before the last tag (usually a {'</div>'} or {'</section>'})</li>
            <li>Click "Save" on the top right portion of the screen</li>
          </ul>
        </li>
      </ol>
    </div>
  )
}
