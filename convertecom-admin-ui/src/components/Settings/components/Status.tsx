import React from 'react'
import { useSettingsStyles } from '../settings-styles';
import {
  Paper,
  Typography,
  Grid,
  Switch,
  Box,
  Divider,
  Link,
  Chip
} from '@material-ui/core';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
import PowerSettingsNewIcon from '@material-ui/icons/PowerSettingsNew';

export default (props: any) => {
  const { active, handleActiveToggle } = props
  const classes = useSettingsStyles()
  return (
    <Grid container spacing={4} className={classes.settingsCard}>
      <Grid item xs={12}>
        <Typography variant="h5" component="h2" className={classes.settingsSectionTitle}>
          Integration Status
        </Typography>
        <Typography variant="body1" component="p" className={classes.settingsSectionDescription}>
          Control whether the ConvertEcom integration is active on your store
        </Typography>
      </Grid>

      <Grid item xs={12}>
        <Paper className={classes.paper}>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
            <Box display="flex" alignItems="center">
              <PowerSettingsNewIcon color={active ? "primary" : "error"} />
              <Box ml={2}>
                <Typography variant="h6">
                  ConvertEcom Integration
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  {active ? 'Currently active on your store' : 'Currently inactive on your store'}
                </Typography>
              </Box>
            </Box>

            <Box display="flex" alignItems="center">
              <Chip
                label={active ? 'Active' : 'Inactive'}
                color={active ? "primary" : "default"}
                style={{
                  backgroundColor: active ? 'rgba(0, 178, 169, 0.1)' : 'rgba(0, 0, 0, 0.08)',
                  color: active ? '#00837E' : '#5A5A5A',
                  fontWeight: 600
                }}
              />
              <Box ml={2}>
                <FormControlLabel
                  classes={{ label: active ? classes.statusActive : classes.statusInactive }}
                  label=""
                  control={<Switch
                    checked={active}
                    onChange={handleActiveToggle}
                    value="true"
                    color="primary"
                    inputProps={{ 'aria-label': 'activation toggle' }}
                  />}
                />
              </Box>
            </Box>
          </Box>

          <Divider />

          <Box mt={3}>
            {active ? (
              <Typography variant="body1">
                Your ConvertEcom integration is currently <strong>active</strong>. Flipping the switch above will hide the ConvertEcom pop-up and CTAs from your store, but new orders will still generate coupons behind the scenes.
              </Typography>
            ) : (
              <>
                <Typography variant="body1" paragraph>
                  Please ensure you have followed and understand our <Link href="https://convertecom.zendesk.com/hc/en-us/articles/************--How-do-I-complete-the-app-configuration-" rel="noopener noreferrer" target="_blank" className={classes.helpLink}>setup guide</Link> before activating ConvertEcom.
                </Typography>
                <Typography variant="body1">
                  Your ConvertEcom integration is currently <strong>inactive</strong>. Coupons will still be generated behind the scenes, but the ConvertEcom pop-ups and CTAs will remain hidden. Flip the switch above to activate the integration.
                </Typography>
              </>
            )}
          </Box>

          <Box mt={3} p={2} bgcolor="rgba(0, 0, 0, 0.02)" borderRadius={1} display="flex">
            <InfoOutlinedIcon style={{ color: '#6D7175', marginRight: '16px', flexShrink: 0, marginTop: '4px' }} />
            <Typography variant="body2" color="textSecondary">
              All ConvertEcom coupons will be automatically injected into your store's discount codes. ConvertEcom codes all start with "CONVERTECOM_". It's very important that you <strong>do not delete or change any "CONVERTECOM_" discount codes</strong> in your Shopify admin panel or ConvertEcom may award broken discount codes to your customers.
            </Typography>
          </Box>
        </Paper>
      </Grid>
    </Grid>
  )
}
