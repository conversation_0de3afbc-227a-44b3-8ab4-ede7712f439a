import React from 'react'
import { useSettingsStyles } from '../settings-styles';
import {
  Button,
  Paper,
  Typography,
  Grid,
} from '@material-ui/core';

export default (props: any) => {
  const { shopifyAdminUrl, shopifyUrl, setActiveStep, setSetupComplete, onSubmit } = props
  const classes = useSettingsStyles()

  // const handleSubmit = () => {
  //   window.localStorage.setItem('ZingAcknowledgment', 'true')
  //   setActiveStep(3)
  //   setSetupComplete(true)
  // }
  return (
    <Grid container justify="center" spacing={4}>
      <Grid item xs={12} md={3}>
        <Typography variant="h6" component="h6" color="primary" gutterBottom>
          Zing Banner
      </Typography>
        <Typography variant="body2" component="p">
          Update current Shopify theme to include Zing banner on checkout
      </Typography>
      </Grid>

      <Grid item xs={12} md={7}>
        <Paper className={classes.paper}>
          <div style={{ paddingTop: '10px' }} >
            <strong className={classes.infoSpacing}>IMPORTANT:</strong>Ensure that you have updated your Cart template with the Zing banner (detailed instructions <a target="_blank" rel="noopener noreferrer" href="https://rocketzing.zendesk.com/hc/en-us/articles/360034920752--How-do-I-complete-the-Zing-app-configuration-">here</a>)
          </div>
          <ul style={{ marginBottom: '0' }}>
            <li>From your {!!shopifyAdminUrl ? <a target="_blank" rel="noopener noreferrer" href={shopifyUrl}>Shopify admin portal</a> : <>Shopify admin portal</>}, select "Online Store" from the "SALES CHANNEL" sidebar</li>
            <li>Select "Themes" from the dropdown</li>
            <li>Look for the "Current theme" section and select "Actions" dropdown</li>
            <li>Select "Edit Code" from the dropdown</li>
            <li>Under the "Sections" folder, select the Cart file (usually named "cart-template.liquid")</li>
            <li>Insert  <strong>{`<div id="convertecom-reservation-container"></div>`}</strong>  before the last tag (usually a {'</div>'} or {'</section>'})</li>
            <li>Click "Save" on the top right portion of the screen</li>
          </ul>
          <Grid item xs={12}>
            <Button
              variant="contained"
              color="primary"
              type="button"
              onClick={onSubmit}
              className={classes.formFooterButton}
            >
              Done
            </Button>
          </Grid>
        </Paper>
      </Grid>
    </Grid >
  )
}
