import * as React from 'react';
import InfoIcon from '@material-ui/icons/Info';
import { useSettingsStyles } from './settings-styles';
import { Typography } from '@material-ui/core';
import { FunctionComponent } from 'react';

export interface InfoLinkProps {
  link?: string;
  topGutter?: boolean;
}

const InfoLink: FunctionComponent<InfoLinkProps> = (props) => {
  const classes = useSettingsStyles();
  return (
    <div
      className={`${classes.sectionTitles} ${
        props.topGutter ? classes.accountSettingsTopGutter : ''
      }`}
    >
      <Typography variant="h5" component="h5" display="inline">
        {props.children}
      </Typography>
      {props.link && <a
        target="_blank"
        rel="noopener noreferrer"
        href={props.link}
        className={classes.infoLinks}
      >
        <InfoIcon color="primary" style={{ fontSize: '28px' }} />
      </a>}
    </div>
  );
};

export default InfoLink;
