import React, { useState, useEffect, useContext } from 'react';
import { Button, Paper, Grid, Typography } from '@material-ui/core';

import { getUser, updateUser } from '../../services/UserService';
import { useStyles } from './styles';
import { FormControl } from '../Form';
import LoadingProgress from '../LoadingProgress';
import { Link } from 'react-router-dom';
import { LoadingContext } from '../../contexts/loading.context';

interface Values {
  email: string;
  password: string;
  passwordConfirmation: string;
}

export default (props: any) => {
  const classes = useStyles();

  const { id } = props;
  const { isLoading, setIsLoading } = useContext(LoadingContext);
  const [errors, setErrors] = useState([]);
  const [values, setValues] = useState({
    email: '',
    password: '',
    passwordConfirmation: '',
  });

  useEffect(() => {
    setIsLoading(true);

    getUser(id).then(response => {
      setValues({
        email: response.data.email,
        password: '',
        passwordConfirmation: '',
      });

      setIsLoading(false);
    });
  }, [id, setIsLoading]);

  async function handleSubmit(e: any) {
    e.preventDefault();
    const response = await updateUser(id, values);
    if (response.errors) {
      setErrors(response.errors);
    } else {
      window.sessionStorage.setItem('mainSuccess', 'User updated successfully');
      window.location.href = '/users';
    }
  }

  const handleChange = (prop: keyof Values) => (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setValues({ ...values, [prop]: event.target.value });
  };

  return (
    <Grid container>
      <Grid item xs={12} md={3}>
        <Typography component="h6" variant="h6" color="primary">
          Edit User
        </Typography>
        <Typography component="p" variant="body2">
          Edit user email or password.
        </Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <Paper className={classes.paper}>
          {isLoading && <LoadingProgress />}
          <form
            className={classes.container}
            noValidate
            autoComplete="off"
            onSubmit={handleSubmit}
          >
            <FormControl
              name="email"
              label="Email"
              type="email"
              onChange={handleChange('email')}
              value={values.email}
              errors={errors}
            />

            <FormControl
              name="password"
              label="New Password"
              type="password"
              onChange={handleChange('password')}
              value={values.password}
              errors={errors}
              hint="Leave blank to keep the existing password"
            />

            <FormControl
              name="passwordConfirmation"
              label="New Password Confirmation"
              type="password"
              onChange={handleChange('passwordConfirmation')}
              value={values.passwordConfirmation}
              errors={errors}
            />

            <div style={{ marginBottom: '20px' }}>
              <Button
                variant="contained"
                to="/users"
                className={classes.formFooterButton}
                component={Link as any}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                color="primary"
                type="submit"
                className={classes.formFooterButton}
              >
                Save changes
              </Button>
            </div>
          </form>
        </Paper>
      </Grid>
    </Grid>
  );
};
