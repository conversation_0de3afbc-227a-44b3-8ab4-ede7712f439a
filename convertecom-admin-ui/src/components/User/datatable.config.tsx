import React from 'react';
import { MUIDataTableColumn } from 'mui-datatables';
import { Button } from '@material-ui/core';
import { Link } from 'react-router-dom';

export const UserTableColumns: MUIDataTableColumn[] = [
  {
    name: 'id',
    label: 'ID',
    options: {
      filter: false,
      sort: true,
      display: 'false',
    },
  },
  {
    name: 'email',
    label: 'Email',
    options: {
      filter: false,
      sort: true,
    },
  },
  {
    name: 'id',
    label: 'Actions',
    options: {
      filter: false,
      sort: false,
      searchable: false,
      customBodyRender: id => {
        if (id) {
          return (
            <>
              <Button component={Link} to={`/users/edit/${id}`}>
                Edit
              </Button>
              <Button component={Link} to={`/users/detail/${id}`}>
                Details
              </Button>
            </>
          );
        }
      },
    },
  },
];
