import React, { useContext, useState } from 'react';
import MUIDataTable, { MUIDataTableState } from 'mui-datatables';
import { Button, Grid, Typography } from '@material-ui/core';
import { Add as AddIcon } from '@material-ui/icons';
import { getUsers } from '../../services/UserService';
import { IFindAllParams } from '../../services/Service';
import { UserTableColumns } from './datatable.config';
import MenuHeader from '../MenuHeader';
import LoadingProgress from '../LoadingProgress';
import { Link } from 'react-router-dom';
import { LoadingContext } from '../../contexts/loading.context';

async function getData(tableState: any) {
  const params: IFindAllParams = {
    skip: tableState.page * tableState.rowsPerPage,
    take: tableState.rowsPerPage,
  };

  if (tableState.activeColumn !== null) {
    const activeColumn = tableState.columns[tableState.activeColumn];
    params.order = activeColumn.name;
    params.dir = activeColumn.sortDirection;
  }

  return await getUsers(params);
}

export default (props: any) => {
  const { isLoading, setIsLoading } = useContext(LoadingContext);
  const [data, setData] = useState([]);
  const [count, setCount] = useState(0);

  const options = {
    serverSide: true,
    onTableChange: async function (
      action: string,
      tableState: MUIDataTableState,
    ) {
      switch (action) {
        case 'changePage':
        case 'changeRowsPerPage':
        case 'sort':
          setIsLoading(true);
          const { data, total } = await getData(tableState);
          setIsLoading(false);
          setCount(total);
          setData(data);
          break;
        default:
          if (action !== 'propsUpdate')
            console.log(`ignoring table action: ${action}`);
      }
    },
    onTableInit: async function (action: string, tableState: MUIDataTableState) {
      setIsLoading(true);
      const { data, total } = await getData(tableState);
      setIsLoading(false);
      setCount(total);
      setData(data);
    },
    count,
    page: 0,
    rowsPerPageOptions: [1, 10, 25, 50],
    selectableRows: 'none' as const,
    print: false,
    download: false,
    search: false,
    filter: false,
  };

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <MenuHeader>
          <Button
            variant="contained"
            size="large"
            color="primary"
            component={Link as any}
            to="/users/add"
          >
            <AddIcon />
            Add User
          </Button>
        </MenuHeader>
      </Grid>

      <Grid item xs={12} md={3}>
        <Typography component="h6" variant="h6" color="primary" gutterBottom>
          Admin Management
        </Typography>
        <Typography component="p" variant="body2">
          Add, edit, and view super-admin accounts that can be used to access
          this admin portal.
        </Typography>
      </Grid>

      <Grid item xs={12} md={9}>
        {isLoading && <LoadingProgress />}
        <MUIDataTable
          title={''}
          data={data}
          columns={UserTableColumns}
          options={options}
        />
      </Grid>
    </Grid>
  );
};
