import React, { useState, useEffect, useContext } from 'react';
import { getUser } from '../../services/UserService';
import {
  CircularProgress,
  Paper,
  Theme,
  Grid,
  Typography,
} from '@material-ui/core';
import { makeStyles, createStyles } from '@material-ui/styles';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableRow from '@material-ui/core/TableRow';
import LoadingProgress from '../LoadingProgress';
import { LoadingContext } from '../../contexts/loading.context';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    root: {
      flexGrow: 1,
    },
    paper: {
      padding: theme.spacing(1, 2),
    },
    table: {},
    rightIcon: {
      marginLeft: theme.spacing(1),
    },
  }),
);

function detailRow(header: string, text: string) {
  return (
    <TableRow>
      <TableCell>
        <strong>{header}</strong>
      </TableCell>
      <TableCell align="right">{text}</TableCell>
    </TableRow>
  );
}

export default (props: any) => {
  const classes = useStyles();

  const { id } = props;
  const { isLoading, setIsLoading } = useContext(LoadingContext);
  const [data, setData] = useState({
    id: '',
    email: '',
    signInCount: '',
    currentSignInAt: '',
    currentSignInIp: '',
    lastSignInAt: '',
    lastSignInIp: '',
    createdAt: '',
    updatedAt: '',
  });

  useEffect(() => {
    setIsLoading(true);

    getUser(id)
      .then(response => {
        setData(response.data);
        setIsLoading(false);
      })
      .catch(err => {
        window.sessionStorage.setItem('mainError', 'User not found');
        window.location.href = '/users';
      });
  }, [id, setIsLoading]);

  if (isLoading) {
    return (
      <div>
        <CircularProgress />
      </div>
    );
  } else {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12} md={3}>
          <Typography component="h6" variant="h6" color="primary">
            User Details
          </Typography>
          <Typography component="p" variant="body2">
            Associated user details.
          </Typography>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper className={classes.paper}>
            {isLoading && <LoadingProgress />}
            <Table className={classes.table}>
              <TableBody>
                {detailRow('ID', data.id)}
                {detailRow('Email', data.email)}
                {detailRow('Total Sign-ins', data.signInCount)}
                {detailRow('Current Sign-in', data.currentSignInAt)}
                {detailRow('Current IP', data.currentSignInIp)}
                {detailRow('Last Sign-in', data.lastSignInAt)}
                {detailRow('Last IP', data.lastSignInIp)}
                {detailRow('Created', data.createdAt)}
                {detailRow('Updated', data.updatedAt)}
              </TableBody>
            </Table>
          </Paper>
        </Grid>
      </Grid>
    );
  }
};
