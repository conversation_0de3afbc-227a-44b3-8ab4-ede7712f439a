import React, { useState } from 'react';
import { Paper, Typography, Grid, Button } from '@material-ui/core';

import { useStyles } from './styles';
import { FormControl } from '../Form';
import { createUser } from '../../services/UserService';
import { Link as RouterLink } from 'react-router-dom';

interface Values {
  email: string;
  password: string;
  passwordConfirmation: string;
}

export default (props: any) => {
  const classes = useStyles();

  const [errors, setErrors] = useState([]);
  const [values, setValues] = useState({
    email: '',
    password: '',
    passwordConfirmation: '',
  });

  async function handleSubmit(e: any) {
    e.preventDefault();
    const response = await createUser(values);
    console.log(response);
    if (response.errors) {
      setErrors(response.errors);
    } else {
      window.location.href = '/users';
    }
  }

  const handleChange = (prop: keyof Values) => (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setValues({ ...values, [prop]: event.target.value });
  };

  return (
    <Grid container>
      <Grid item xs={12} md={3}>
        <Typography component="h6" variant="h6" color="primary">
          Add User
        </Typography>
        <Typography component="p" variant="body2">
          Add a new Admin user.
        </Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <Paper elevation={1} className={classes.paper}>
          <form
            className={classes.container}
            noValidate
            autoComplete="off"
            onSubmit={handleSubmit}
          >
            <FormControl
              name="email"
              label="Email"
              type="email"
              onChange={handleChange('email')}
              value={values.email}
              errors={errors}
            />
            <FormControl
              name="password"
              label="Password"
              type="password"
              hint="Must be at least 8 characters long."
              onChange={handleChange('password')}
              value={values.password}
              errors={errors}
            />
            <FormControl
              name="passwordConfirmation"
              label="Password Confirmation"
              type="password"
              onChange={handleChange('passwordConfirmation')}
              value={values.passwordConfirmation}
              errors={errors}
            />

            <div style={{ marginBottom: '20px' }}>
              <Button
                variant="contained"
                to="/users"
                className={classes.formFooterButton}
                component={RouterLink as any}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                color="primary"
                type="submit"
                className={classes.formFooterButton}
              >
                Save
              </Button>
            </div>
          </form>
        </Paper>
      </Grid>
    </Grid>
  );
};
