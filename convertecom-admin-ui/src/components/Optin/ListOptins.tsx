import React, { useState } from 'react';
import { <PERSON>rid, <PERSON>po<PERSON>, Button } from '@material-ui/core';
import downloadjs from 'downloadjs';
import { IFindAllParams } from '../../services/Service';
import { getOptins, downloadOptinsCsv } from '../../services/OptinService';
import MUIDataTable, { MUIDataTableState } from 'mui-datatables';
import { OptinListColumns } from './datatable.config';
import LoadingProgress from '../LoadingProgress';

async function getData(tableState: any) {
  const params: IFindAllParams = {
    skip: tableState.page * tableState.rowsPerPage,
    take: tableState.rowsPerPage,
  };

  if (tableState.activeColumn !== null) {
    const activeColumn = tableState.columns[tableState.activeColumn];
    params.order = activeColumn.name;
    params.dir = activeColumn.sortDirection;
  }

  return await getOptins(params);
}

async function handleCsvDownload() {
  const rows = await downloadOptinsCsv();
  downloadjs(rows, 'optin-emails.csv', 'text/csv');
}

export default (props: any) => {
  const { isLoading, setIsLoading } = props;
  const [data, setData] = useState([]);
  const [count, setCount] = useState(0);

  const options = {
    serverSide: true,
    onTableChange: async function(
      action: string,
      tableState: MUIDataTableState,
    ) {
      switch (action) {
        case 'changePage':
        case 'changeRowsPerPage':
        case 'sort':
          setIsLoading(true);
          const { data, total } = await getData(tableState);
          setIsLoading(false);
          setCount(total);
          setData(data);
          break;
        default:
          if (action !== 'propsUpdate')
            console.log(`ignoring table action: ${action}`);
      }
    },
    onTableInit: async function(action: string, tableState: MUIDataTableState) {
      setIsLoading(true);
      const { data, total } = await getData(tableState);
      setIsLoading(false);
      setCount(total);
      setData(data);
    },
    count,
    page: 0,
    rowsPerPageOptions: [1, 10, 25, 50],
    selectableRows: 'none' as const,
    print: false,
    download: false,
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={3}>
        <Typography component="h6" variant="h6" color="primary" gutterBottom>
          Optins
        </Typography>
        <Typography component="p" variant="body2">
          Email addresses of users who opted in
        </Typography>
        <Button variant="contained" color="primary" onClick={handleCsvDownload}>
          Export to CSV
        </Button>
      </Grid>
      <Grid item xs={12} md={9}>
        {isLoading && <LoadingProgress />}
        <MUIDataTable
          title={''}
          data={data}
          columns={OptinListColumns}
          options={options}
        />
      </Grid>
    </Grid>
  );
};
