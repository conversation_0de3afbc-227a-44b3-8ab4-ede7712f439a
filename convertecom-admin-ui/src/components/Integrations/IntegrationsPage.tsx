import * as React from 'react';
import { useCallback, useContext, useEffect, useState } from 'react';
import { PageContainer } from '../core/PageContainer';
import { LoadingContext } from '../../contexts/loading.context';
import { RecartIntegration } from './RecartIntegration';
import {
  getIntegrationPlatforms,
  getIntegrations,
  IntegrationDto,
  IntegrationPlatformName,
} from '../../services/IntegrationService';
import { isErrorResponse } from '../../services/Service';
import { Snackbar, Typography } from '@material-ui/core';

export interface RecartIntegrationData {
  id: string;
  isEnabled: boolean;
  source: string;
  smartDisplayEnabled: boolean;
  forceSendToMessenger: boolean;
  skin: 'light' | 'dark';
}

interface Integrations {
  [IntegrationPlatformName.RECART]?: RecartIntegrationData;
}

interface Platforms {
  [IntegrationPlatformName.RECART]?: string;
}

function extractRecart(item: IntegrationDto) {
  if (item.configuration.__platform !== IntegrationPlatformName.RECART) {
    throw new Error('Invalid integration provided for Recart.');
  }
  return {
    id: item.id,
    isEnabled: item.isEnabled,
    source: item.configuration!.source,
    smartDisplayEnabled: item.configuration!.smartDisplayEnabled,
    forceSendToMessenger: item.configuration!.forceSendToMessenger,
    skin: item.configuration!.skin,
  };
}

export const IntegrationsPage = () => {
  const { setIsLoading } = useContext(LoadingContext);
  const [integrations, setIntegrations] = useState<Integrations>({});
  const [platforms, setPlatforms] = useState<Platforms>({});
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string }>({
    open: false,
    message: '',
  });

  function updateIntegrations(integrations: IntegrationDto[]) {
    setIntegrations(
      integrations.reduce<Integrations>((result, item) => {
        if (item.configuration.__platform === IntegrationPlatformName.RECART) {
          result[IntegrationPlatformName.RECART] = extractRecart(item);
        }
        return result;
      }, {}),
    );
  }

  async function fetchIntegrations() {
    const [integrationsRes, platformsRes] = await Promise.all([
      getIntegrations(),
      getIntegrationPlatforms(),
    ]);
    if (isErrorResponse(integrationsRes) || isErrorResponse(platformsRes)) {
      alert('Unable to load integrations.');
      return;
    }

    updateIntegrations(integrationsRes.data);

    setPlatforms(
      platformsRes.data.reduce<Platforms>((acc, { id, name }) => {
        acc[name] = id;
        return acc;
      }, {}),
    );
  }

  function refreshIntegrations(
    updatedIntegrationName?: IntegrationPlatformName,
  ) {
    return async () => {
      const integrationsRes = await getIntegrations();
      if (isErrorResponse(integrationsRes)) {
        alert('Failed to update integrations.');
        return;
      }
      updateIntegrations(integrationsRes.data);

      setSnackbar({
        open: true,
        message: updatedIntegrationName
          ? `Updated ${updatedIntegrationName} settings.`
          : 'Updated settings.',
      });
    };
  }

  function closeSnackbar() {
    setSnackbar({ open: false, message: '' });
  }

  const refreshRecart = useCallback(
    refreshIntegrations(IntegrationPlatformName.RECART),
    [],
  );

  useEffect(() => {
    setIsLoading(true);
    fetchIntegrations().then(() => {
      setIsLoading(false);
    });
  }, [fetchIntegrations, setIsLoading]);
  return (
    <PageContainer>
      {platforms[IntegrationPlatformName.RECART] ? (
        <RecartIntegration
          platformId={platforms[IntegrationPlatformName.RECART]!}
          integrationData={integrations.recart}
          onSave={refreshRecart}
        />
      ) : null}
      {Object.keys(platforms).length === 0 ? (
        <Typography variant="body2" align="center" color="textSecondary">
          No integrations available.
        </Typography>
      ) : null}
      <Snackbar
        open={snackbar.open}
        message={snackbar.message}
        autoHideDuration={6000}
        onClose={closeSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />
    </PageContainer>
  );
};
