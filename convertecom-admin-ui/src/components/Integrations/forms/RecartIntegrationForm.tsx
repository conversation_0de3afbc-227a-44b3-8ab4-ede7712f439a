import * as React from 'react';
import { useCallback, useEffect } from 'react';
import {
  Button,
  createStyles,
  FormControl as MuiFormControl,
  FormControlLabel,
  FormHelperText,
  makeStyles,
  Paper,
  Switch,
} from '@material-ui/core';
import { Controller, useForm, SubmitHandler } from 'react-hook-form';
import { FormControl } from '../../Form';
import RadioGroup from '@material-ui/core/RadioGroup';
import Radio from '@material-ui/core/Radio';
import InputLabel from '@material-ui/core/InputLabel';

const useStyles = makeStyles(theme =>
  createStyles({
    paper: {
      padding: theme.spacing(2, 4),
    },
    form: {
      maxWidth: '400px',
    },
    actionButton: {
      marginTop: theme.spacing(2),
      marginRight: theme.spacing(1),
    },
  }),
);

export interface FormValues {
  source: string;
  smartDisplayEnabled: boolean;
  forceSendToMessenger: boolean;
  skin: 'light' | 'dark';
}

export interface RecartIntegrationFormProps {
  values: FormValues;
  isEnabled: boolean;
  onSubmit: SubmitHandler<FormValues>;
  formDisabled: boolean;
}

export const RecartIntegrationForm = (props: RecartIntegrationFormProps) => {
  const { values, onSubmit, formDisabled } = props;
  const classes = useStyles();
  const { register, handleSubmit, control, formState, reset } = useForm<
    FormValues
  >({ defaultValues: values });

  const resetForm = useCallback(() => {
    reset(values);
  }, [reset, values]);

  useEffect(() => {
    resetForm();
  }, [resetForm, values]);

  return (
    <Paper className={classes.paper}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className={classes.form}
        aria-disabled={formDisabled}
      >
        <MuiFormControl margin="normal">
          {/* The radio labels are reversed because it seems easier for the layperson to understand text color vs
          color scheme. The dark skin has white text and the light skin has dark text.*/}
          <label>Messenger Widget Text Color</label>
          <Controller
            name="skin"
            control={control}
            defaultValue={values.skin}
            render={({ field }) => (
              <RadioGroup {...field} row={true}>
                <FormControlLabel
                  value="light"
                  control={<Radio />}
                  label="Dark"
                />
                <FormControlLabel
                  value="dark"
                  control={<Radio />}
                  label="Light"
                />
              </RadioGroup>
            )}
          />
          <FormHelperText id="skinHelperText">
            Use a light text color if the popup background is dark or a dark
            text color if the popup background is light.
          </FormHelperText>
        </MuiFormControl>
        <MuiFormControl margin="normal">
          <FormControlLabel
            disabled={true /*formDisabled*/}
            control={
              <Controller
                name="forceSendToMessenger"
                control={control}
                defaultValue={values.forceSendToMessenger}
                render={({ field }) => (
                  <Switch
                    {...field}
                    id="forceSendToMessenger"
                    aria-describedby="forceSendToMessengerHelperText"
                  />
                )}
              />
            }
            label={
              <label htmlFor="forceSendToMessenger">
                Force "Send to Messenger" functionality
              </label>
            }
          />
          <FormHelperText id="forceSendToMessengerHelperText">
            If a user is logged into Facebook, they need to click the "Send to
            Messenger" checkbox.
          </FormHelperText>
        </MuiFormControl>
        <MuiFormControl margin="normal">
          <FormControlLabel
            disabled={true /*formDisabled*/}
            control={
              <Controller
                name="smartDisplayEnabled"
                control={control}
                defaultValue={values.smartDisplayEnabled}
                render={({ field }) => (
                  <Switch
                    {...field}
                    id="smartDisplayEnabled"
                    aria-describedby="smartDisplayEnabledHelperText"
                  />
                )}
              />
            }
            label={
              <label htmlFor="smartDisplayEnabled">Smart Display On</label>
            }
          />
          <FormHelperText id="smartDisplayEnabledHelperText">
            Detect whether a customer is logged into Facebook and show your
            Messenger tools.{' '}
            <strong>It is recommended to leave this on.</strong>
            <br />
            <a
              href="https://help.recart.com/en/articles/2926064-smart-display-getting-started"
              rel="noopener nofollow noreferrer"
              target="_blank"
            >
              Learn more
            </a>
          </FormHelperText>
        </MuiFormControl>
        <FormControl
          label="Source"
          name="source"
          {...register("source")}
          defaultValue={values.source}
          disabled
          hint="The event source. Read-only."
        />
        <Button
          type="button"
          variant="contained"
          className={classes.actionButton}
          onClick={resetForm}
          disabled={!formState.isDirty || formDisabled}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          color="primary"
          className={classes.actionButton}
          disabled={!formState.isDirty || formDisabled}
        >
          Save
        </Button>
      </form>
    </Paper>
  );
};
