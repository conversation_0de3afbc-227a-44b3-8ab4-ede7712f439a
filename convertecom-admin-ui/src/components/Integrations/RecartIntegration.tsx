import * as React from 'react';
import { useEffect, useState } from 'react';
import { FormControlLabel, Grid, Switch, Typography } from '@material-ui/core';
import {
  FormValues,
  RecartIntegrationForm,
} from './forms/RecartIntegrationForm';
import { PageSection } from '../core/PageSection';
import { SubmitHandler } from 'react-hook-form';
import { RecartIntegrationData } from './IntegrationsPage';
import {
  createIntegration,
  IntegrationPlatformName,
  updateIntegration,
} from '../../services/IntegrationService';
import { isErrorResponse } from '../../services/Service';
import { RequestState } from '../../interfaces/request-state.enum';
import { useRequestState } from '../../util/hooks';
import _get from 'lodash/get';

const defaultValues = (): FormValues => ({
  source: 'zing',
  smartDisplayEnabled: true,
  forceSendToMessenger: true,
  skin: 'dark',
});

export interface RecartIntegrationProps {
  integrationData?: RecartIntegrationData;
  platformId: string;
  onSave: () => Promise<void>;
}

export const RecartIntegration = (props: RecartIntegrationProps) => {
  const { integrationData, onSave, platformId } = props;
  const [values, setValues] = useState(defaultValues);
  const [requestState, setRequestState] = useRequestState();

  const saveForm: SubmitHandler<FormValues> = async formData => {
    setRequestState.loading();
    if (!integrationData || typeof integrationData.id === 'undefined') {
      const res = await createIntegration({
        platformId,
        configuration: {
          ...formData,
          __platform: IntegrationPlatformName.RECART,
        },
      });
      if (isErrorResponse(res)) {
        alert('Unable to save settings!');
        setRequestState.error();
        return;
      }
    }
    if (integrationData && typeof integrationData.id !== 'undefined') {
      const res = await updateIntegration(integrationData.id, {
        configuration: {
          ...formData,
          __platform: IntegrationPlatformName.RECART,
        },
      });
      if (isErrorResponse(res)) {
        alert('Unable to save settings!');
        setRequestState.error();
        return;
      }
    }
    setRequestState.none();
    await onSave();
  };

  const toggleIntegration = async () => {
    setRequestState.loading();
    // Create the integration if it hasn't been enabled
    if (!integrationData || typeof integrationData.id === 'undefined') {
      const res = await createIntegration({
        platformId,
        isEnabled: true,
        configuration: {
          ...values,
          __platform: IntegrationPlatformName.RECART,
        },
      });
      if (isErrorResponse(res)) {
        alert('Error setting up integration.');
        setRequestState.error();
        return;
      }
    } else if (typeof integrationData !== 'undefined') {
      // Integration configuration exists but it is either disabled or enabled
      const res = await updateIntegration(integrationData.id, {
        isEnabled: !integrationData.isEnabled,
      });
      if (isErrorResponse(res)) {
        alert('Error setting enabled state.');
        setRequestState.error();
        return;
      }
    }
    setRequestState.none();
    await onSave();
  };

  useEffect(() => {
    if (integrationData) {
      setValues({
        source: integrationData.source,
        smartDisplayEnabled: integrationData.smartDisplayEnabled,
        forceSendToMessenger: integrationData.forceSendToMessenger,
        skin: integrationData.skin,
      });
    }
  }, [integrationData]);

  const isEnabled = _get(integrationData, 'isEnabled', false);

  return (
    <PageSection
      title="Recart"
      description="Integrate your existing Recart Shopify plugin with Zing."
      sidebarContent={
        <Grid>
          <FormControlLabel
            control={
              <Switch
                name="enabled"
                checked={isEnabled}
                onChange={toggleIntegration}
                disabled={requestState === RequestState.LOADING}
              />
            }
            label={
              isEnabled ? (
                <Typography color="secondary">
                  <strong>Enabled</strong>
                </Typography>
              ) : (
                <Typography color="textSecondary">Disabled</Typography>
              )
            }
          />
          <Typography gutterBottom={true}>
            <a
              href="https://recart.com/"
              target="_blank"
              rel="noreferrer noopener nofollow"
            >
              Get Recart
            </a>
          </Typography>
        </Grid>
      }
    >
      <RecartIntegrationForm
        values={values}
        isEnabled={isEnabled}
        onSubmit={saveForm}
        formDisabled={requestState === RequestState.LOADING}
      />
    </PageSection>
  );
};
