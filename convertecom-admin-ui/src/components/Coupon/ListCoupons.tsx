import React, { useState } from 'react';
import MUIDataTable, { MUIDataTableState } from 'mui-datatables';
import {
  Table,
  TableBody,
  TableRow,
  TableCell,
  Grid,
  Typography,
} from '@material-ui/core';
import { zipObject } from 'lodash';

import { useStyles } from './styles';
import { CouponListColumns } from './datatable.config';
import { IFindAllParams } from '../../services/Service';
import { getCoupons } from '../../services/CouponService';
import LoadingProgress from '../LoadingProgress';

async function getData(tableState: any) {
  const params: IFindAllParams = {
    skip: tableState.page * tableState.rowsPerPage,
    take: tableState.rowsPerPage,
  };

  if (tableState.activeColumn !== null) {
    const activeColumn = tableState.columns[tableState.activeColumn];
    params.order = activeColumn.name;
    params.dir = activeColumn.sortDirection;
  }

  return await getCoupons(params);
}

export default (props: any) => {
  const classes = useStyles();

  const { isLoading, setIsLoading } = props;
  const [data, setData] = useState([]);
  const [count, setCount] = useState(0);

  const options = {
    serverSide: true,
    onTableChange: async function(
      action: string,
      tableState: MUIDataTableState,
    ) {
      switch (action) {
        case 'changePage':
        case 'changeRowsPerPage':
        case 'sort':
          setIsLoading(true);
          const { data, total } = await getData(tableState);
          setIsLoading(false);
          setCount(total);
          setData(data);
          break;
        default:
          if (action !== 'propsUpdate')
            console.log(`ignoring table action: ${action}`);
      }
    },
    onTableInit: async function(action: string, tableState: MUIDataTableState) {
      setIsLoading(true);
      const { data, total } = await getData(tableState);
      setIsLoading(false);
      setCount(total);
      setData(data);
    },
    count,
    page: 0,
    rowsPerPageOptions: [1, 10, 25, 50],
    selectableRows: 'none' as const,
    expandableRows: true,
    renderExpandableRow: (rowData: any, rowMeta: any) => {
      const keys = CouponListColumns.map(o => o.name);
      const row = zipObject(keys, rowData);

      const originatorOrder: any = row.originatorOrder;

      let originatorOrderRows;
      if (originatorOrder) {
        originatorOrderRows = (
          <>
            <TableRow>
              <TableCell component="th">Originator Order ID</TableCell>
              <TableCell>{originatorOrder.externalOrderId}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Originator Order Total Price</TableCell>
              <TableCell>${originatorOrder.totalPrice}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">
                Originator Order Subtotal Price
              </TableCell>
              <TableCell>${originatorOrder.subtotalPrice}</TableCell>
            </TableRow>
          </>
        );
      }

      const redemptionOrders: any = row.redemptionOrders;
      const redemptionOrder: any = redemptionOrders[0];

      let redemptionOrderRows;
      if (redemptionOrder) {
        redemptionOrderRows = (
          <>
            <TableRow>
              <TableCell component="th">Redemption Order ID</TableCell>
              <TableCell>{redemptionOrder.externalOrderId}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Redemption Order Total Price</TableCell>
              <TableCell>${redemptionOrder.totalPrice}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">
                Redemption Order Subtotal Price
              </TableCell>
              <TableCell>${redemptionOrder.subtotalPrice}</TableCell>
            </TableRow>
          </>
        );
      }

      return (
        <TableRow className={classes.expandableRow}>
          <TableCell className={classes.expandableRowContainerCell} colSpan={7}>
            <Table size="small">
              <TableBody>
                {originatorOrderRows}
                <TableRow>
                  <TableCell component="th">Coupon Rate</TableCell>
                  <TableCell>
                    {parseFloat(row.percentage.toString()) * 100}%
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell component="th">Minimum Cart Subtotal</TableCell>
                  <TableCell>
                    {row.minimumCartSubtotal
                      ? `$${row.minimumCartSubtotal}`
                      : 'N/A - legacy'}
                  </TableCell>
                </TableRow>
                {row.reservedId && (
                  <TableRow>
                    <TableCell component="th">Reserved User ID</TableCell>
                    <TableCell>{row.reservedId}</TableCell>
                  </TableRow>
                )}
                {redemptionOrderRows}
              </TableBody>
            </Table>
          </TableCell>
        </TableRow>
      );
    },
    print: false,
    download: false,
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={3}>
        <Typography component="h6" variant="h6" color="primary" gutterBottom>
          Coupons
        </Typography>
        <Typography component="p" variant="body2">
          Coupons available or generated by the system.
        </Typography>
      </Grid>
      <Grid item xs={12} md={9}>
        {isLoading && <LoadingProgress />}
        <MUIDataTable
          title={''}
          data={data}
          columns={CouponListColumns}
          options={options}
        />
      </Grid>
    </Grid>
  );
};
