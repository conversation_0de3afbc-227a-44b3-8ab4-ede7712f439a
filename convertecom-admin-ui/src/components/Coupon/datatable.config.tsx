import { MUIDataTableColumn } from 'mui-datatables';

export const CouponListColumns: MUIDataTableColumn[] = [
  {
    name: 'id',
    label: 'ID',
    options: {
      filter: true,
      sort: true,
      display: 'false',
    },
  },
  {
    name: 'code',
    label: 'Code',
    options: {
      filter: true,
      sort: true,
    },
  },
  {
    name: 'orderAmount',
    label: 'Originator Order Amount',
    options: {
      filter: false,
      sort: true,
      display: 'excluded',
    },
  },
  {
    name: 'percentage',
    label: 'Discount Percentage',
    options: {
      filter: false,
      sort: true,
      display: 'excluded',
    },
  },
  {
    name: 'minimumCartSubtotal',
    label: 'Minimum Cart Subtotal',
    options: {
      filter: false,
      sort: true,
      display: 'excluded',
    },
  },
  {
    name: 'couponAmount',
    label: 'Amount',
    options: {
      filter: true,
      sort: true,
    },
  },
  {
    name: 'createdAt',
    label: 'Created At',
    options: {
      filter: false,
      sort: true,
    },
  },
  {
    name: 'reservedId',
    label: 'Reserved ID',
    options: {
      filter: false,
      sort: true,
      display: 'excluded',
    },
  },
  {
    name: 'reservedUntil',
    label: 'Reserved Until',
    options: {
      filter: false,
      sort: true,
    },
  },
  {
    name: 'originatorOrder',
    label: 'Originator Order',
    options: {
      filter: false,
      sort: false,
      display: 'excluded',
    },
  },
  {
    name: 'redemptionOrders',
    label: 'Redemption Orders',
    options: {
      filter: false,
      sort: false,
      display: 'excluded',
    },
  },
];
