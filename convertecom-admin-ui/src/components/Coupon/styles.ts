import { createStyles, makeStyles, fade } from '@material-ui/core/styles';
import { Theme } from '@material-ui/core';

export const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    expandableRow: {
      backgroundColor: '#fafafa',
    },
    expandableRowContainerCell: {
      // padding: theme.spacing(10)
    },
    progressOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      zIndex: 110,
      display: 'flex',
      width: '100%',
      height: '100%',
      backgroundColor: fade(theme.palette.background.paper, 0.7),
    },
    progressContainer: {
      margin: 'auto',
    },
  }),
);
