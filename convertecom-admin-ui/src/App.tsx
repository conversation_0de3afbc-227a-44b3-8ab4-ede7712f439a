import { Container, CssBaseline } from '@material-ui/core';
import { decode } from 'jsonwebtoken';
import { has } from 'lodash';
import React, { useEffect, useState } from 'react';
import 'url-search-params-polyfill';
import MainAlerts from './components/MainAlerts';
import Navbar from './components/Navbar';
import { isAuthenticated, isSuperAdmin } from './services/AuthService';
import {
  getTenantByToken,
  isTenantSetupComplete,
} from './services/TenantService';
import { useStyles } from './styles';
import { GlobalContextValue } from './interfaces/global-props.interface';
import { TrialNotification } from './components/TrialNotification';
import {
  LoadingContext,
  LoadingContextValue,
} from './contexts/loading.context';
import { AppRoutes } from './routes';
import { GlobalContext } from './contexts/global.context';
import { isErrorResponse } from './services/Service';
import { TenantContextProvider } from './contexts/tenant.context';
// import { useAppBridge } from '@shopify/app-bridge-react';


export default () => {
  const classes = useStyles();
  // const shopify = useAppBridge();
  const [tenantSetupComplete, setTenantSetupComplete] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthed, setIsAuthed] = useState(isAuthenticated());
  const [expired, setExpired] = useState(false);
  const [setupStatus, updateStatus] = useState(false);
  const [tenant, setTenant] = useState<any>(undefined);
  const globalContext: GlobalContextValue = {
    tenantSetupComplete,
    isAuthed,
    setIsAuthed,
    expired,
    setExpired,
    setupStatus,
    updateStatus,
    setTenantSetupComplete,
  };

  const loadingContext: LoadingContextValue = {
    isLoading,
    setIsLoading,
  };

  // Using AppRoutes component instead of useRoutes

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.has('access_token')) {
      const token = urlParams.get('access_token') || '';
      try {
        const jwtPayload = decode(token);
        if (!has(jwtPayload, 'tenantId')) {
          throw new Error('Invalid token');
        }
        window.sessionStorage.setItem('accessToken', token);
        window.sessionStorage.setItem(
          'accessTokenExpiresAt',
          urlParams.get('access_token_expires_at') || '0',
        );
        setIsAuthed(isAuthenticated());
        window.location.href = '/';
      } catch (e) {
        setIsLoading(false);
        alert('Error occurred while validating token');
        console.error(e);
      }
      return;
    }

    if (!isAuthed) {
      return setExpired(true);
    }

    if (!isSuperAdmin()) {
      getTenantByToken()
        .then(res => {
          if (isErrorResponse(res)) {
            throw new Error('Could not get tenant');
          }
          const tenant = res.data;
          setTenant(tenant);
          window.sessionStorage.setItem(
            'shopifyAdminUrl',
            `https://${tenant.externalId}/admin/apps`,
          );
          return isTenantSetupComplete(tenant);
        })
        .then(isComplete => setTenantSetupComplete(isComplete))
        .catch(err => console.error(err));
    }
  }, [isAuthed]);

  return (
    <div className={classes.root}>
      <CssBaseline />

      {isAuthenticated() && (
        <Navbar tenantSetupComplete={tenantSetupComplete} />
      )}

      <main className={classes.content}>
        <div className={classes.appBarSpacer} />
        {!!tenant ? (
          <TrialNotification trialDaysRemaining={tenant.trialDaysRemaining} />
        ) : null}

        <Container maxWidth="lg" className={classes.container}>
          <MainAlerts />
          <LoadingContext.Provider value={loadingContext}>
            <GlobalContext.Provider value={globalContext}>
              <TenantContextProvider><AppRoutes /></TenantContextProvider>
            </GlobalContext.Provider>
          </LoadingContext.Provider>
        </Container>
      </main>
    </div>
  );
};
