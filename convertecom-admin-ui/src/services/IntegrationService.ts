import { apiGet, apiPatch, apiPost } from './Service';

export enum IntegrationPlatformName {
  RECART = 'recart',
}

export interface IntegrationDto {
  id: string;
  isEnabled: boolean;
  configuration: Record<string, any> & { __platform: IntegrationPlatformName };
}

export interface IntegrationPlatformDto {
  id: string;
  name: IntegrationPlatformName;
}

export interface CreateIntegrationDto {
  platformId: string;
  isEnabled?: boolean;
  configuration: { [key: string]: any } & {
    __platform: IntegrationPlatformName;
  };
}

export interface UpdateIntegrationDto {
  isEnabled?: boolean;
  configuration?: { [key: string]: any } & {
    __platform: IntegrationPlatformName;
  };
}

export function getIntegrations() {
  return apiGet<{ data: IntegrationDto[] }>('/integration');
}

export function getIntegrationPlatforms() {
  return apiGet<{ data: IntegrationPlatformDto[] }>('/integration-platform');
}

export function createIntegration(body: CreateIntegrationDto) {
  return apiPost('/integration', body);
}

export function updateIntegration(
  integrationId: string,
  body: UpdateIntegrationDto,
) {
  return apiPatch(`/integration/${integrationId}`, body);
}
