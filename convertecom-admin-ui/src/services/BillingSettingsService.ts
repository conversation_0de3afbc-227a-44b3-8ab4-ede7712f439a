import { apiGet, apiPatch, isErrorResponse } from './Service';
import { BillingSettings } from '../components/BillingSettings/interfaces/billing-settings.interface';
import { BillingSettingType } from '../components/BillingSettings/enums/billing-setting-type.enum';
import { BillingSettingScope } from '../components/BillingSettings/enums/billing-setting-scope.enum';
import { Result } from '../util/requests';

export interface BillingSettingsSubscription {
  trialDays: number;
}

export async function fetchBillingSettings() {
  return apiGet<BillingSettings[]>('/billing-settings');
}

export async function fetchGlobalSubscriptionSettings(): Promise<
  Result<BillingSettings>
> {
  const requestError = [
    { field: '_request', messages: ['Something went wrong.'] },
  ];
  try {
    const res = await fetchBillingSettings();
    if (isErrorResponse(res)) {
      return { errors: res.errors || requestError };
    }
    const subscription = res.find(
      r =>
        r.type === BillingSettingType.SUBSCRIPTION &&
        r.scope === BillingSettingScope.GLOBAL,
    );
    if (subscription) {
      return { result: subscription };
    }
  } catch (e) {
    return { errors: requestError };
  }
  return { errors: requestError };
}

export async function updateBillingSettings(
  id: string,
  payload: { value: any },
): Promise<Result<boolean>> {
  try {
    const res = await apiPatch(`/billing-settings/${id}`, payload);
    if (isErrorResponse<boolean>(res)) {
      return {
        errors: res.errors || [
          { field: '_response', messages: ['Something went wrong'] },
        ],
      };
    }
    return { result: true };
  } catch (e) {
    return {
      errors: [{ field: '_response', messages: ['Something went wrong'] }],
    };
  }
}
