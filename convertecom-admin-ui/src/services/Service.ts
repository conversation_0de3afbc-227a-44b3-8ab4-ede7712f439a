import Axios, { AxiosError } from 'axios';
import { get } from 'lodash';

export interface IFindAllParams {
  skip: number;
  take: number;
  order?: string;
  dir?: string;
  tenantId?: string;
  date?: string;
  active?: string;
}

export interface APIError {
  field: string;
  messages: string[];
}

export interface APIErrorResponse {
  status: number;
  statusText: string;
  errors?: APIError[];
}

function getAuthenticatedClient() {
  return Axios.create({
    baseURL: '/api',
    headers: {
      Authorization: `Bearer ${window.sessionStorage.getItem('accessToken')}`,
    },
  });
}

export async function apiGet<T = any>(
  endpoint: string,
  params?: any,
): Promise<T | APIErrorResponse> {
  try {
    const authenticatedClient = getAuthenticatedClient();
    const response = await authenticatedClient({ url: endpoint, params });
    return response.data;
  } catch (e) {
    return handleErrorResponse(e);
  }
}

export async function apiPost(endpoint: string, data: any) {
  try {
    const authenticatedClient = getAuthenticatedClient();
    const response = await authenticatedClient({
      method: 'POST',
      url: endpoint,
      data,
    });
    return response.data;
  } catch (e: any) {
    return handleErrorResponse(e);
  }
}

export async function apiPatch(endpoint: string, data: any) {
  try {
    const authenticatedClient = getAuthenticatedClient();
    const response = await authenticatedClient({
      method: 'PATCH',
      url: endpoint,
      data,
    });
    return response.data;
  } catch (e: any) {
    return handleErrorResponse(e);
  }
}

// Pluck out validation errors into something the client can easily digest
function handleErrorResponse(e: AxiosError): APIErrorResponse {
  if (!e.response) {
    return { status: 500, statusText: 'Unknown error' };
  }

  if (get(e, 'response.status') === 401) {
    // Expired token. Force a redirect to root so we can display the session expired error
    window.location.href = '/';
  }

  if (!e.response.data || !e.response.data.message) {
    return {
      status: e.response.status,
      statusText: get(e.response, 'error', 'Unknown error'),
    };
  }

  const response = {
    status: e.response.status,
    statusText: e.response.statusText,
    errors: [],
  };

  if (Array.isArray(e.response.data.message)) {
    response.errors = e.response.data.message.map((m: any) => {
      return { field: m.property, messages: Object.values(m.constraints) };
    });
  } else {
    alert('Unknown error occurred');
  }

  return response;
}

export function isErrorResponse<T = any>(
  res: T | APIErrorResponse,
): res is APIErrorResponse {
  const maybeError = res as APIErrorResponse;
  return (
    typeof maybeError.errors !== 'undefined' ||
    typeof maybeError.status !== 'undefined' ||
    typeof maybeError.statusText !== 'undefined'
  );
}
