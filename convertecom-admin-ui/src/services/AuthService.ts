import { decode } from 'jsonwebtoken';
import { get } from 'lodash';
import { apiPost } from './Service';

export interface LoginDto {
  email: string;
  password: string;
}

export function isAuthenticated(): boolean {
  const accessToken = window.sessionStorage.getItem('accessToken');
  const accessTokenExpiresAt =
    window.sessionStorage.getItem('accessTokenExpiresAt') || '0';
  const expirationMs = parseInt(accessTokenExpiresAt) - Date.now();

  return !!accessToken && expirationMs > 300000;
}

export function getDecodedAccessToken() {
  const accessToken = window.sessionStorage.getItem('accessToken') || '';
  return decode(accessToken);
}

export function getUserId(): string {
  const jwtPayload = getDecodedAccessToken();
  return get(jwtPayload, 'id');
}

export function getUserRole(): string {
  const jwtPayload = getDecodedAccessToken();
  return get(jwtPayload, 'roles[0]', 'user');
}

// TODO this needs to call the tenant service and confirm that billing has been set up
export function isTenantSetupComplete(): Promise<boolean> {
  const jwtPayload = getDecodedAccessToken();
  return get(jwtPayload, 'isActive', false);
}

export function isUser(): boolean {
  return getUserRole() === 'user';
}

export function isSuperAdmin(): boolean {
  return getUserRole() === 'superadmin';
}

// ie: auth is via something like a Shopify token, not actual user login credentials
export function isTenantUser(): boolean {
  return getUserRole() === 'tenant';
}

export function logout(): void {
  const redirectHref = !isTenantUser() ? '/login' : '/';
  window.sessionStorage.clear();
  window.location.href = redirectHref;
}

export async function login(params: LoginDto): Promise<void> {
  const response = await apiPost('/auth/token', params);

  if (response.status === 401 || response.status === 400) {
    throw new Error('Invalid login credentials');
  } else if (response.status === 500) {
    throw new Error('Login error');
  }

  const expiresAt = Date.now() + response.expiresIn * 1000;

  window.sessionStorage.setItem('accessToken', response.accessToken);
  window.sessionStorage.setItem('accessTokenExpiresAt', expiresAt.toString());

  return response;
}
