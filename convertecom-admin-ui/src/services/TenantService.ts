import 'formdata-polyfill';
import { apiGet, apiPatch, apiPost, isErrorResponse } from './Service';
import { FeatureFlagsDto, TenantDto } from '../dto/tenant.dto';

export async function getTenants(params: any) {
  return await apiGet('/tenant', params);
}

export async function getTenant(id: string) {
  return apiGet(`/tenant/${id}`);
}

export async function getTenantByToken() {
  return await apiGet<{ data: TenantDto}>('/tenant/me'); // Gets the tenant associated with auth token
}

export async function updateTenantByToken(data: any) {
  return await apiPatch('/tenant/me', data);
}

export async function updateTenantFallbackCoupon(data: any) {
  return await apiPatch('tenant/fallback-coupon', data);
}

export async function uploadTenantImage(file:any) {
  const formData = new FormData()
  formData.append('file', file);
  return await apiPost('tenant/s3-upload', formData);
}

export async function isTenantSetupComplete(data?: TenantDto): Promise<boolean> {
  if (!data) {
    const res = await getTenantByToken();
    if (isErrorResponse(res)) {
      return false;
    }
    data = res.data;
  }
  return (
    !!data &&
    !!data.billingCredentials &&
    !!data.billingCredentials.externalId &&
    !!data.template
  );
}

export function updateTenantFeatureFlags(tenantId: string, featureFlags: FeatureFlagsDto) {
  return apiPatch(`/tenant/${tenantId}/feature-flags`, featureFlags);
}

export function hasFeatureFlag(
  flag: keyof FeatureFlagsDto,
  featureFlags: FeatureFlagsDto,
): boolean {
  return typeof featureFlags[flag] !== 'undefined' && !!featureFlags[flag];
}

