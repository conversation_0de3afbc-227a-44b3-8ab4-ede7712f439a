import { IFindAllParams, apiGet, apiPatch, apiPost } from './Service';

export async function getUsers(params: IFindAllParams) {
  return await apiGet(`/user`, params);
}

export async function getUser(id: string) {
  return await apiGet(`/user/${id}`);
}

export async function updateUser(id: string, data: any) {
  // TODO common interfaces between client and server
  return await apiPatch(`/user/${id}`, data);
}

export async function createUser(data: any) {
  // TODO interface
  return await apiPost(`/user`, data);
}
