import { apiGet, apiPost } from './Service';

export async function getCreditLog(tenantId: string) {
  return apiGet<{ data: any[] }>(`/tenant/${tenantId}/credit-log`);
}

export interface AddApplicationCreditDto {
  tenantId: string;
  amount: number;
  description: string;
  test: boolean | null;
}
export async function addApplicationCredit(dto: AddApplicationCreditDto) {
  return apiPost(`/credit`, dto);
}
