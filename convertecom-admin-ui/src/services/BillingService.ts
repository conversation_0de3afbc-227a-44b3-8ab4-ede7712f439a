import { apiGet, apiPost } from './Service';
import { BillingSummaryDto } from '../dto/billing.dto';

export async function getBilling(params: any) {
  return await apiGet('/billing', params);
}

export async function getBillingSummaryForPeriod(period?: number) {
  return await apiGet<BillingSummaryDto>(
    `/billing/summary${period ? `?billingPeriod=${period}` : ''}`,
  );
}

export async function setupShopifyBilling() {
  return await apiPost('/billing/setup/shopify', {});
}

export async function downloadBillingCsv() {
  return await apiGet(`/billing/export`);
}

export async function getTenantBillingHistory(
  tenantId: string,
  startDate?: Date,
  endDate?: Date,
) {
  const queryParms =
    startDate && endDate
      ? encodeURI(
          `startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`,
        )
      : '';
  return await apiGet(`/tenant/${tenantId}/billing-history?${queryParms}`);
}
