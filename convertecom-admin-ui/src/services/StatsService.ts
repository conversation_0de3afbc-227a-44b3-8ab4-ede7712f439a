import { apiGet } from './Service';
import { DashboardStatisticsDto } from '../dto/statistic.dto';
import { DateInterval } from '../util/parse-date-interval';
import { AxiosResponse } from 'axios';

export async function getDashboardStats(params: DateInterval) {
  return await apiGet<AxiosResponse<DashboardStatisticsDto>>(`/statistic/dashboard`, params);
}

export async function getReportStats(params: DateInterval) {
  return await apiGet(`/statistic/reports`, params);
}
