import * as React from 'react';
import { isAuthenticated, isSuperAdmin } from './services/AuthService';
import Dashboard from './components/Dashboard';
import { ReportsDisplay } from './components/Reports';
import ListBillingDetails from './components/Billing/ListBillingDetails';
import ListBillingSummary from './components/Billing/ListBillingSummary';
import EditSettingsForm from './components/BillingSettings/EditSettingsForm';
import {
  AddUserForm,
  EditUserForm,
  ListUsers,
  UserDetails,
} from './components/User';
import { ListTenants } from './components/Tenant';
import { ViewTenant } from './components/Tenant/ViewTenant';
import ManageIntegration from './components/Settings/ManageSettings';
import LoginForm from './components/LoginForm';
import NotFoundError from './components/Errors/NotFoundError';
import {
  shouldActivate,
  whenNotSuperAdmin,
  whenSuperAdmin,
} from './components/hoc/guards';
import { IntegrationsPage } from './components/Integrations';
import { Route, Routes } from 'react-router-dom';

export const AppRoutes = () => {
  return (
    <Routes>
      <Route
        path="/"
        element={shouldActivate(
          ({ tenantSetupComplete }) => tenantSetupComplete || isSuperAdmin(),
          Dashboard,
          '/settings',
        )({})}
      />
      <Route
        path="/reports"
        element={shouldActivate(
          ({ isAuthed, tenantSetupComplete }) =>
            isAuthed && (tenantSetupComplete || isSuperAdmin()),
          ReportsDisplay,
          '/',
        )({})}
      />
      <Route
        path="/billing"
        element={shouldActivate(
          ({ isAuthed, tenantSetupComplete }) => isAuthed && tenantSetupComplete,
          (props) => isSuperAdmin() ? <ListBillingDetails {...props} /> : <ListBillingSummary {...props} />,
          '/',
        )({})}
      />
      <Route
        path="/billing/settings"
        element={shouldActivate(
          () => isAuthenticated() && isSuperAdmin(),
          EditSettingsForm,
          '/',
        )({})}
      />
      <Route path="/users" element={whenSuperAdmin(ListUsers)()} />
      <Route path="/users/add" element={whenSuperAdmin(AddUserForm)()} />
      <Route path="/users/detail/:id" element={whenSuperAdmin(UserDetails)()} />
      <Route path="/users/edit/:id" element={whenSuperAdmin(EditUserForm)()} />
      <Route path="/tenants" element={whenSuperAdmin(ListTenants)()} />
      <Route path="/tenants/:id" element={whenSuperAdmin(ViewTenant)()} />
      <Route path="/settings" element={whenNotSuperAdmin(ManageIntegration)()} />
      <Route path="/settings/integrations" element={whenNotSuperAdmin(IntegrationsPage)()} />
      <Route
        path="/login"
        element={shouldActivate(({ isAuthed }) => !isAuthed, LoginForm, '/')({}) /* Pass empty props object */}
      />
      <Route
        path="*"
        element={shouldActivate(({ isAuthed }) => isAuthed, NotFoundError, '/')({}) /* Pass empty props object */}
      />
    </Routes>
  );
};
