export interface TemplateValue {
  text?: string;
  color?: string;
  colorRgba?: {
    a?: number;
    b: number;
    g: number;
    r: number;
  }
  backgroundColor?: string;
  opacity?: string;
}

export interface Template {
  mainHeader: TemplateValue;
  mainSubHeader: TemplateValue;
  optIn: TemplateValue;
  optInBackground: TemplateValue;
  optInButton: TemplateValue;
  optOutLink: TemplateValue;
  optOutExtra: TemplateValue;
  optInResultHeader: TemplateValue;
  optInResult: TemplateValue;
  optInResultLink: TemplateValue;
  cart: TemplateValue;
  cartBackground: TemplateValue;
  cartButton: TemplateValue;
  cartAmount: TemplateValue;
  cartTime: TemplateValue;
  cartExpired: TemplateValue;
  cartExpiredButton: TemplateValue;
  cartInvalid: TemplateValue;
  cartInvalidButton: TemplateValue;
  cartPopupPickOne: TemplateValue;
  cartPopupHeader: TemplateValue;
  cartPopupAmount: TemplateValue;
  cartPopupTime: TemplateValue;
  cartPopupLink: TemplateValue;
  cartPopupIconGradient: TemplateValue;
  cartPopupCustomImage: TemplateValue;
  popupDelay: number;
  optOutTtl: number;
}
