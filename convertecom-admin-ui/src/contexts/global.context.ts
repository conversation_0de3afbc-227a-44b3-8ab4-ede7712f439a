import * as React from 'react';
import { GlobalContextValue } from '../interfaces/global-props.interface';

export function booleanNoop(val: boolean) {
}

export const GlobalContext = React.createContext<GlobalContextValue>({
  tenantSetupComplete: true,
  isAuthed: false,
  setIsAuthed: booleanNoop,
  expired: false,
  setExpired: booleanNoop,
  setupStatus: false,
  updateStatus: booleanNoop,
  setTenantSetupComplete: booleanNoop,
});
