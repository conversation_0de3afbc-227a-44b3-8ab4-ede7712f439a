import * as React from 'react';
import { TenantDto } from '../dto/tenant.dto';
import { getTenantByToken } from '../services/TenantService';
import { isErrorResponse } from '../services/Service';
import { isAuthenticated, isSuperAdmin } from '../services/AuthService';
import { useContext } from 'react';
import { GlobalContext } from './global.context';

export interface TenantContextValue {
  tenant?: TenantDto;
  fetchTenant: () => Promise<void>;
}

export const TenantContext = React.createContext<TenantContextValue>({
  tenant: undefined,
  fetchTenant: () => Promise.resolve(),
});

export const TenantContextProvider: React.FC = props => {
  const globalContext = useContext(GlobalContext);
  const [tenant, setTenant] = React.useState<TenantDto>();

  const fetchTenant = React.useCallback(async () => {
    const response = await getTenantByToken();
    if (isErrorResponse(response)) {
      console.error('unable to load tenant')
      return;
    }
    setTenant(response.data);
    }, []);

  React.useEffect(() => {
    if (globalContext.isAuthed && !isSuperAdmin()) {
      fetchTenant();
    }
  }, [fetchTenant, globalContext.isAuthed])

  React.useEffect(() => {
    if (tenant) {
      window.sessionStorage.setItem(
        'shopifyAdminUrl',
        `https://${tenant.externalId}/admin/apps`,
      );
    }
  }, [tenant]);

  return (
    <TenantContext.Provider
      value={{ tenant, fetchTenant }}
      children={props.children}
    />
  );
};
