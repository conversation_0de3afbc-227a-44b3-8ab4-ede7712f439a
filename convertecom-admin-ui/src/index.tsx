import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';
import * as serviceWorker from './serviceWorker';
import { ThemeProvider } from '@material-ui/styles';
import theme from './theme';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from '@shopify/app-bridge-react';

// AppBridge configuration
const config = {
  apiKey: process.env.REACT_APP_SHOPIFY_API_KEY!,
  host: new URLSearchParams(window.location.search).get('host')!,
  forceRedirect: true,
};

ReactDOM.render(
  <ThemeProvider theme={theme}>
    {/* <Provider config={config}> */}
      <BrowserRouter>
        <App />
      </BrowserRouter>
    {/* </Provider> */}
  </ThemeProvider>,
  document.getElementById('root'),
);

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.unregister();
