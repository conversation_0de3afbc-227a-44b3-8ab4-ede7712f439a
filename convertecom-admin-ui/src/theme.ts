import { createMuiTheme } from '@material-ui/core/styles';

// ConvertEcom theme - completely redesigned for a modern Shopify app look
const theme = createMuiTheme({
  palette: {
    primary: {
      main: '#00B2A9', // Teal from ConvertEcom logo
      light: '#4CE3DB',
      dark: '#00837E',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#FFD100', // Yellow from ConvertEcom logo
      light: '#FFDE4C',
      dark: '#D6B000',
      contrastText: '#000000',
    },
    background: {
      default: '#F5F5F7', // Light gray background similar to modern apps
      paper: '#FFFFFF',
    },
    text: {
      primary: '#202223', // Shopify-like dark text
      secondary: '#6D7175', // Shopify-like secondary text
    },
    error: {
      main: '#D82C0D', // Shopify-like error color
    },
    success: {
      main: '#008060', // Shopify-like success color
    },
    warning: {
      main: '#FFC453',
    },
    info: {
      main: '#00BFFF',
    },
    divider: 'rgba(0, 0, 0, 0.08)',
  },
  typography: {
    fontFamily:
      'Inter, -apple-system, BlinkMacSystemFont, San Francisco, Roboto, Segoe UI, Helvetica Neue, sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '2rem',
      letterSpacing: '-0.025em',
    },
    h2: {
      fontWeight: 600,
      fontSize: '1.75rem',
      letterSpacing: '-0.025em',
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.5rem',
      letterSpacing: '-0.025em',
    },
    h4: {
      fontWeight: 600,
      fontSize: '1.25rem',
      letterSpacing: '-0.025em',
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.125rem',
      letterSpacing: '-0.025em',
    },
    h6: {
      fontWeight: 600,
      fontSize: '1rem',
      letterSpacing: '-0.025em',
    },
    subtitle1: {
      fontSize: '1rem',
      fontWeight: 500,
      letterSpacing: '-0.01em',
    },
    subtitle2: {
      fontSize: '0.875rem',
      fontWeight: 500,
      letterSpacing: '-0.01em',
    },
    body1: {
      fontSize: '0.9375rem',
      letterSpacing: '-0.01em',
    },
    body2: {
      fontSize: '0.875rem',
      letterSpacing: '-0.01em',
    },
    button: {
      fontWeight: 600,
      textTransform: 'none',
      letterSpacing: '-0.01em',
    },
    caption: {
      fontSize: '0.75rem',
      letterSpacing: '-0.01em',
    },
  },
  overrides: {
    MuiCssBaseline: {
      '@global': {
        '*': {
          boxSizing: 'border-box',
        },
        body: {
          backgroundColor: '#F5F5F7',
        },
      },
    },
    MuiPaper: {
      root: {
        boxShadow: 'none',
        border: '1px solid rgba(0, 0, 0, 0.08)',
      },
      elevation1: {
        boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.04), 0px 1px 2px rgba(0, 0, 0, 0.06)',
      },
      elevation2: {
        boxShadow: '0px 3px 6px rgba(0, 0, 0, 0.05), 0px 1px 4px rgba(0, 0, 0, 0.08)',
      },
      elevation3: {
        boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.1)',
      },
      rounded: {
        borderRadius: 12,
      },
    },
    MuiListItemIcon: {
      root: {
        minWidth: 36,
        color: 'inherit',
      },
    },
    MuiListItem: {
      root: {
        borderRadius: 8,
        '&$selected': {
          backgroundColor: 'rgba(0, 178, 169, 0.08)',
          color: '#00B2A9',
          '& .MuiListItemIcon-root': {
            color: '#00B2A9',
          },
        },
        '&:hover': {
          backgroundColor: 'rgba(0, 0, 0, 0.04)',
        },
      },
      button: {
        '&:hover': {
          backgroundColor: 'rgba(0, 0, 0, 0.04)',
        },
      },
    },
    MuiListItemText: {
      primary: {
        fontSize: '0.9375rem',
        fontWeight: 500,
      },
      secondary: {
        fontSize: '0.8125rem',
      },
    },
    MuiTableBody: {
      root: {
        boxSizing: 'content-box',
      },
    },
    MuiTableCell: {
      root: {
        borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
        padding: '16px',
      },
      head: {
        fontWeight: 600,
        color: '#202223',
      },
    },
    MuiCard: {
      root: {
        borderRadius: 12,
        overflow: 'hidden',
        transition: 'all 0.2s ease-in-out',
        border: '1px solid rgba(0, 0, 0, 0.08)',
        boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.04), 0px 1px 2px rgba(0, 0, 0, 0.06)',
        '&:hover': {
          boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.1)',
          transform: 'translateY(-2px)',
        },
      },
    },
    MuiCardContent: {
      root: {
        padding: '24px',
        '&:last-child': {
          paddingBottom: '24px',
        },
      },
    },
    MuiButton: {
      root: {
        borderRadius: 8,
        padding: '10px 20px',
        fontWeight: 600,
        fontSize: '0.9375rem',
      },
      contained: {
        boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.1), 0px 1px 2px rgba(0, 0, 0, 0.06)',
        '&:hover': {
          boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.1), 0px 2px 4px rgba(0, 0, 0, 0.06)',
        },
      },
      containedPrimary: {
        background: 'linear-gradient(135deg, #00B2A9 0%, #008B84 100%)',
        '&:hover': {
          background: 'linear-gradient(135deg, #00C9BF 0%, #00A59D 100%)',
        },
      },
      containedSecondary: {
        background: 'linear-gradient(135deg, #FFD100 0%, #FFBE00 100%)',
        '&:hover': {
          background: 'linear-gradient(135deg, #FFE03A 0%, #FFD100 100%)',
        },
      },
      outlined: {
        borderWidth: '1.5px',
        '&:hover': {
          borderWidth: '1.5px',
        },
      },
      text: {
        padding: '10px 16px',
      },
    },
    MuiIconButton: {
      root: {
        padding: 10,
        color: '#6D7175',
        '&:hover': {
          backgroundColor: 'rgba(0, 0, 0, 0.04)',
        },
      },
    },
    MuiSelect: {
      root: {
        borderRadius: 8,
      },
      select: {
        padding: '12px 14px',
        '&:focus': {
          backgroundColor: 'transparent',
        },
      },
    },
    MuiOutlinedInput: {
      root: {
        borderRadius: 8,
        '&:hover $notchedOutline': {
          borderColor: 'rgba(0, 0, 0, 0.23)',
        },
        '&$focused $notchedOutline': {
          borderColor: '#00B2A9',
          borderWidth: 2,
        },
      },
      input: {
        padding: '12px 14px',
      },
      notchedOutline: {
        borderColor: 'rgba(0, 0, 0, 0.15)',
      },
    },
    MuiInputLabel: {
      outlined: {
        transform: 'translate(14px, 14px) scale(1)',
      },
    },
    MuiAppBar: {
      root: {
        boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.04), 0px 1px 2px rgba(0, 0, 0, 0.06)',
      },
      colorDefault: {
        backgroundColor: '#FFFFFF',
        color: '#202223',
      },
    },
    MuiDrawer: {
      paper: {
        border: 'none',
        boxShadow: '1px 0px 3px rgba(0, 0, 0, 0.04), 1px 0px 2px rgba(0, 0, 0, 0.06)',
      },
    },
    MuiDivider: {
      root: {
        backgroundColor: 'rgba(0, 0, 0, 0.08)',
      },
    },
    MuiTooltip: {
      tooltip: {
        backgroundColor: '#202223',
        fontSize: '0.75rem',
        padding: '8px 12px',
        borderRadius: 4,
      },
      arrow: {
        color: '#202223',
      },
    },
    MuiChip: {
      root: {
        borderRadius: 16,
        height: 32,
        fontSize: '0.8125rem',
      },
      colorPrimary: {
        backgroundColor: 'rgba(0, 178, 169, 0.12)',
        color: '#00837E',
      },
      colorSecondary: {
        backgroundColor: 'rgba(255, 209, 0, 0.12)',
        color: '#D6B000',
      },
    },
  },
  shape: {
    borderRadius: 8,
  },
  props: {
    MuiButtonBase: {
      disableRipple: true,
    },
    MuiButton: {
      disableElevation: true,
    },
  },
});

export default theme;
