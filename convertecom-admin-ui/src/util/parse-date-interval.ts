import moment from 'moment-timezone';

export interface DateInterval {
  startDate?: string;
  endDate?: string;
  interval?: string;
}

export function parseDateInterval(value: string): DateInterval {
  let startDate, endDate, interval;
  const format = 'Y-MM-DD HH:mm:ss';

  switch (value) {
    case 'today':
      interval = 'hour';
      startDate = moment()
        .startOf('day')
        .utc()
        .format(format);
      break;
    case 'yesterday':
      interval = 'hour';
      startDate = moment()
        .subtract(1, 'day')
        .startOf('day')
        .utc()
        .format(format);
      endDate = moment()
        .subtract(1, 'day')
        .endOf('day')
        .utc()
        .format(format);
      break;
    case 'last7Days':
      interval = 'day';
      startDate = moment()
        .subtract(7, 'day')
        .startOf('day')
        .utc()
        .format(format);
      break;
    case 'last30Days':
      interval = 'day';
      startDate = moment()
        .subtract(30, 'day')
        .startOf('day')
        .utc()
        .format(format);
      break;
    case 'thisMonth':
      interval = 'day';
      startDate = moment()
        .startOf('month')
        .utc()
        .format(format);
      break;
    case 'lastMonth':
      interval = 'day';
      startDate = moment()
        .subtract(1, 'month')
        .startOf('month')
        .utc()
        .format(format);
      endDate = moment()
        .subtract(1, 'month')
        .endOf('month')
        .utc()
        .format(format);
      break;
    case 'thisYear':
      interval = 'month';
      startDate = moment()
        .startOf('year')
        .utc()
        .format(format);
      break;
    case 'lifetime':
      interval = 'month';
  }

  return { startDate, endDate, interval };
}
