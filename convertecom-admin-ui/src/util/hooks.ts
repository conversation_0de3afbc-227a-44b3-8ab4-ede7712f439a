import { RequestState } from '../interfaces/request-state.enum';
import { useState } from 'react';

export function useRequestState(
  initialState: RequestState = RequestState.NONE,
): [
  RequestState,
  { loading: () => void; error: () => void; none: () => void },
] {
  const [state, setState] = useState(initialState);

  const loading = () => setState(RequestState.LOADING);
  const error = () => setState(RequestState.ERROR);
  const none = () => setState(RequestState.NONE);

  return [state, { loading, error, none }];
}
