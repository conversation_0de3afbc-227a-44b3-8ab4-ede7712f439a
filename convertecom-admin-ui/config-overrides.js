const webpack = require('webpack');

module.exports = function override(config, env) {
    // Add polyfills for Node.js core modules
    config.resolve.fallback = {
        ...config.resolve.fallback,
        "stream": require.resolve("stream-browserify"),
        "buffer": require.resolve("buffer/"),
        "crypto": require.resolve("crypto-browserify"),
        "util": require.resolve("util/")
    };
    // Add support for .mjs files
    config.module.rules.push({
        test: /\.mjs$/,
        include: /node_modules/,
        type: 'javascript/auto'
    });

    // Process specific node_modules that use modern syntax
    const newBabelRule = {
        test: /\.(js|mjs)$/,
        include: [
            /node_modules\/apexcharts/,
            /node_modules\/react-hook-form/
        ],
        use: {
            loader: require.resolve('babel-loader'),
            options: {
                presets: [
                    require.resolve('@babel/preset-env'),
                    require.resolve('@babel/preset-react')
                ],
                plugins: [
                    require.resolve('@babel/plugin-proposal-nullish-coalescing-operator'),
                    require.resolve('@babel/plugin-proposal-optional-chaining'),
                    [require.resolve('@babel/plugin-proposal-class-properties'), { loose: true }],
                    [require.resolve('@babel/plugin-proposal-private-methods'), { loose: true }]
                ],
                cacheDirectory: true,
                cacheCompression: false,
                compact: false,
            }
        }
    };

    // Add the new rule
    config.module.rules.unshift(newBabelRule);

    // Add webpack plugins for polyfills
    config.plugins.push(
        new webpack.ProvidePlugin({
            Buffer: ['buffer', 'Buffer'],
            process: 'process/browser',
        })
    );

    return config;
};