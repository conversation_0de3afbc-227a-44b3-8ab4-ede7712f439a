module.exports = {
    presets: [
        [
            "@babel/preset-env",
            {
                targets: {
                    node: "current",
                    browsers: [
                        ">0.2%",
                        "not dead",
                        "not op_mini all"
                    ]
                }
            }
        ],
        ["@babel/preset-react", { runtime: "automatic" }],
        "@babel/preset-typescript"
    ],
    plugins: [
        "@babel/plugin-proposal-nullish-coalescing-operator",
        "@babel/plugin-proposal-optional-chaining",
        ["@babel/plugin-proposal-class-properties", { "loose": true }],
        ["@babel/plugin-proposal-private-methods", { "loose": true }]
    ]
};