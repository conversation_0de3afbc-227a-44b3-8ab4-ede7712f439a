output "codepipeline_names" {
  description = "Names of the created CodePipelines"
  value       = { for k, v in aws_codepipeline.services : k => v.name }
}

output "codepipeline_arns" {
  description = "ARNs of the created CodePipelines"
  value       = { for k, v in aws_codepipeline.services : k => v.arn }
}

output "codebuild_project_names" {
  description = "Names of the created CodeBuild projects"
  value       = { for k, v in aws_codebuild_project.services : k => v.name }
}

output "codebuild_project_arns" {
  description = "ARNs of the created CodeBuild projects"
  value       = { for k, v in aws_codebuild_project.services : k => v.arn }
}

output "pipeline_trigger_commands" {
  description = "Commands to manually trigger each pipeline"
  value = { for k, v in aws_codepipeline.services : k => "aws codepipeline start-pipeline-execution --name ${v.name}" }
}

output "s3_artifacts_bucket" {
  description = "S3 bucket for CodePipeline artifacts"
  value       = aws_s3_bucket.codepipeline_artifacts.bucket
}

output "iam_codepipeline_role_arn" {
  description = "ARN of the CodePipeline IAM role"
  value       = aws_iam_role.codepipeline.arn
}

output "iam_codebuild_role_arn" {
  description = "ARN of the CodeBuild IAM role"
  value       = aws_iam_role.codebuild.arn
}
