# ConvertEcom CodePipeline Infrastructure

This Terraform module creates AWS CodePipeline infrastructure for all 5 ConvertEcom services. Each service gets its own individual pipeline with GitHub integration and automatic deployment to ECS Fargate.

## Prerequisites

1. **Existing AWS Infrastructure**: This module assumes you have already deployed the main infrastructure (ECS cluster, ECR repositories, ALB, etc.) using the main Terraform configuration.

2. **GitHub Personal Access Token**: You need a GitHub personal access token with the following permissions:
   - `repo` (Full control of private repositories)
   - `admin:repo_hook` (Full control of repository hooks)

3. **Missing Dockerfiles**: Ensure all services have Dockerfiles (already created in previous steps).

## Services Included

- **convertecom**: Main API service (port 3000)
- **admin-api**: Admin API service (port 3000) 
- **admin-ui**: Admin UI service (port 8080)
- **queue**: Queue processing service (port 8000)
- **billing**: Billing service (port 3000)

## Architecture

Each service gets:
- **CodeBuild Project**: Builds Docker image and pushes to ECR
- **CodePipeline**: 3-stage pipeline (Source → Build → Deploy)
- **GitHub Webhook**: Triggers pipeline on main branch commits
- **Change Detection**: Smart monorepo change detection per service

## Deployment Steps

### 1. Configure Variables

```bash
cd terraform-codepipeline
cp terraform.tfvars.example terraform.tfvars
```

Edit `terraform.tfvars` with your values:
```hcl
github_token = "your-github-personal-access-token"
github_owner = "ConvertEcom"
github_repo  = "convertecom-shopify"
```

### 2. Initialize and Deploy

```bash
# Initialize Terraform
terraform init

# Plan the deployment
terraform plan

# Apply the configuration
terraform apply
```

### 3. Verify Deployment

After deployment, check:
- CodePipeline console for all 5 pipelines
- CodeBuild console for all 5 build projects
- GitHub repository webhooks are configured
- S3 bucket for artifacts is created

## Pipeline Behavior

### Triggers
- Pipelines trigger on commits to the `main` branch
- Each service has independent change detection
- Manual pipeline execution is also supported

### Build Process
1. **Source**: Pulls code from GitHub main branch
2. **Build**: 
   - Builds Docker image using service-specific Dockerfile
   - Tags with commit hash and 'latest'
   - Pushes to ECR repository
3. **Deploy**: 
   - Updates ECS service with new image
   - Uses rolling deployment strategy

### Monorepo Change Detection

The buildspec files include smart change detection to only build services when their code changes. This is handled through:
- Service-specific paths in variables
- Dockerfile paths for each service
- Environment variables for build context

## Outputs

After deployment, you'll get:
- Pipeline names and ARNs
- CodeBuild project names and ARNs  
- GitHub webhook URLs
- S3 artifacts bucket name
- IAM role ARNs

## Cost Optimization

- Uses `BUILD_GENERAL1_SMALL` compute type (cheapest)
- 20-minute build timeout to prevent runaway costs
- S3 lifecycle policies for artifact cleanup
- No unnecessary logging or monitoring

## Troubleshooting

### Common Issues

1. **GitHub Token Permissions**: Ensure token has `repo` and `admin:repo_hook` permissions
2. **ECR Repository Not Found**: Verify ECR repositories exist with correct naming
3. **ECS Service Not Found**: Verify ECS services exist with correct naming
4. **Build Failures**: Check CodeBuild logs for Docker build issues

### Manual Pipeline Execution

```bash
# Trigger a specific pipeline manually
aws codepipeline start-pipeline-execution --name convertecom-prod-convertecom-pipeline
```

### View Pipeline Status

```bash
# List all pipelines
aws codepipeline list-pipelines

# Get pipeline execution history
aws codepipeline list-pipeline-executions --pipeline-name convertecom-prod-convertecom-pipeline
```

## Security

- IAM roles follow least privilege principle
- S3 bucket has public access blocked
- GitHub webhooks use HMAC authentication
- ECR repositories use AWS managed encryption

## Cleanup

To destroy the infrastructure:

```bash
terraform destroy
```

Note: This will delete all pipelines, build projects, and the S3 artifacts bucket.
