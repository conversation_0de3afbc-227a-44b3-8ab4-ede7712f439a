#!/bin/bash

# ConvertEcom CodePipeline Deployment Script
# This script helps deploy the CodePipeline infrastructure

set -e

echo "🚀 ConvertEcom CodePipeline Deployment"
echo "======================================"

# Check if terraform.tfvars exists
if [ ! -f "terraform.tfvars" ]; then
    echo "❌ terraform.tfvars not found!"
    echo "📝 Please copy terraform.tfvars.example to terraform.tfvars and fill in your values:"
    echo "   cp terraform.tfvars.example terraform.tfvars"
    echo "   # Edit terraform.tfvars with your GitHub token and other values"
    exit 1
fi

# Check if GitHub token is set
if ! grep -q "github_token.*=.*\".*\"" terraform.tfvars; then
    echo "❌ GitHub token not configured in terraform.tfvars!"
    echo "📝 Please set your GitHub personal access token in terraform.tfvars"
    exit 1
fi

echo "✅ Configuration file found"

# Initialize Terraform if needed
if [ ! -d ".terraform" ]; then
    echo "🔧 Initializing Terraform..."
    terraform init
else
    echo "✅ Terraform already initialized"
fi

# Validate configuration
echo "🔍 Validating Terraform configuration..."
terraform validate

# Plan deployment
echo "📋 Planning deployment..."
terraform plan -out=tfplan

# Ask for confirmation
echo ""
echo "🤔 Do you want to proceed with the deployment? (y/N)"
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo "❌ Deployment cancelled"
    rm -f tfplan
    exit 0
fi

# Apply deployment
echo "🚀 Deploying CodePipeline infrastructure..."
terraform apply tfplan

# Clean up plan file
rm -f tfplan

echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "📊 Next steps:"
echo "1. Check AWS CodePipeline console for your 5 pipelines"
echo "2. Verify GitHub webhooks are configured in your repository"
echo "3. Make a test commit to trigger the pipelines"
echo "4. Monitor the first pipeline execution in AWS console"
echo ""
echo "🔗 Useful commands:"
echo "   terraform output                    # View all outputs"
echo "   terraform output webhook_urls       # View webhook URLs"
echo "   terraform output codepipeline_names # View pipeline names"
echo ""
echo "🎉 Happy deploying!"
