# ECR Repository Outputs
output "ecr_repository_urls" {
  description = "ECR repository URLs for all services"
  value       = module.ecr.repository_urls
}

output "ecr_push_commands" {
  description = "Commands to push Docker images to ECR repositories"
  value = {
    for service, url in module.ecr.repository_urls : service => [
      "aws ecr get-login-password --region ${var.aws_region} | docker login --username AWS --password-stdin ${split("/", url)[0]}",
      "docker tag ${service}:latest ${url}:latest",
      "docker push ${url}:latest"
    ]
  }
}

# VPC and Networking Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.networking.vpc_id
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = module.networking.public_subnet_ids
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = module.networking.private_subnet_ids
}

# ECS Outputs
output "ecs_cluster_id" {
  description = "ID of the ECS cluster"
  value       = module.ecs.cluster_id
}

output "ecs_cluster_name" {
  description = "Name of the ECS cluster"
  value       = module.ecs.cluster_name
}

output "ecs_service_names" {
  description = "Names of the ECS services"
  value       = module.ecs.service_names
}

output "ecs_task_definition_arns" {
  description = "ARNs of the ECS task definitions"
  value       = module.ecs.task_definition_arns
}

# ALB Outputs
output "alb_dns_name" {
  description = "DNS name of the Application Load Balancer"
  value       = module.alb.dns_name
}

output "alb_zone_id" {
  description = "Zone ID of the Application Load Balancer"
  value       = module.alb.zone_id
}

output "alb_arn" {
  description = "ARN of the Application Load Balancer"
  value       = module.alb.arn
}

output "alb_target_group_arns" {
  description = "ARNs of the ALB target groups"
  value       = module.alb.target_group_arns
}

# RDS Outputs
output "rds_endpoint" {
  description = "RDS instance endpoint"
  value       = module.rds.endpoint
  sensitive   = true
}

output "rds_port" {
  description = "RDS instance port"
  value       = module.rds.port
}

output "database_name" {
  description = "Name of the database"
  value       = module.rds.database_name
}

output "database_url_parameter_name" {
  description = "Name of the Parameter Store parameter containing database URL"
  value       = module.rds.database_url_parameter_name
}

# IAM Outputs
output "ecs_task_execution_role_arn" {
  description = "ARN of the ECS task execution role"
  value       = module.iam.ecs_task_execution_role_arn
}

output "ecs_task_role_arn" {
  description = "ARN of the ECS task role"
  value       = module.iam.ecs_task_role_arn
}

# Security Group Outputs
output "alb_security_group_id" {
  description = "ID of the ALB security group"
  value       = module.networking.alb_security_group_id
}

output "ecs_security_group_id" {
  description = "ID of the ECS security group"
  value       = module.networking.ecs_security_group_id
}

output "rds_security_group_id" {
  description = "ID of the RDS security group"
  value       = module.networking.rds_security_group_id
}

# Application URLs
output "application_urls" {
  description = "URLs for accessing different services"
  value = {
    main_api     = "https://${module.alb.dns_name}/api"
    admin_api    = "https://${module.alb.dns_name}/admin-api"
    admin_ui     = "https://${module.alb.dns_name}/admin"
    queue_api    = "https://${module.alb.dns_name}/queue"
    billing_api  = "https://${module.alb.dns_name}/billing"
  }
}

# Parameter Store Names
output "parameter_store_names" {
  description = "Names of Parameter Store parameters for environment variables"
  value = {
    database_url         = module.rds.database_url_parameter_name
    jwt_secret          = "/convertecom/${var.environment}/jwt-secret"
    shopify_api_key     = "/convertecom/${var.environment}/shopify-api-key"
    shopify_api_secret  = "/convertecom/${var.environment}/shopify-api-secret"
    sentry_dsn          = "/convertecom/${var.environment}/sentry-dsn"
    new_relic_license   = "/convertecom/${var.environment}/new-relic-license"
  }
}

# Cost Information
output "estimated_monthly_cost" {
  description = "Estimated monthly cost breakdown"
  value = {
    ecs_fargate     = "~$25-50 (5 services, 1-2 tasks each)"
    alb             = "~$16"
    rds_micro       = "~$12"
    data_transfer   = "~$5-10"
    cloudwatch      = "~$3-5"
    parameter_store = "~$1"
    total_estimate  = "~$62-94"
  }
}
