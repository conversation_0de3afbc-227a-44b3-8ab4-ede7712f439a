# ConvertEcom AWS Infrastructure

This Terraform project provisions the complete AWS infrastructure for the ConvertEcom platform using ECS Fargate, Application Load Balancer, and RDS PostgreSQL.

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                        Internet                              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Application Load Balancer                    │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ /api/*      │ /admin-api/*│ /admin/*    │ /queue/*    │  │
│  │ /billing/*  │             │             │             │  │
└──┴─────────────┴─────────────┴─────────────┴─────────────┴──┘
   │             │             │             │
┌──▼──┐       ┌──▼──┐       ┌──▼──┐       ┌──▼──┐       ┌─────┐
│ API │       │Admin│       │Admin│       │Queue│       │Bill │
│     │       │ API │       │ UI  │       │     │       │     │
│1-2  │       │1-2  │       │1-2  │       │1-2  │       │1-2  │
│Tasks│       │Tasks│       │Tasks│       │Tasks│       │Tasks│
└─────┘       └─────┘       └─────┘       └─────┘       └─────┘
   │             │             │             │             │
   └─────────────┼─────────────┼─────────────┼─────────────┘
                 │             │             │
              ┌──▼─────────────▼─────────────▼──┐
              │         RDS PostgreSQL          │
              │         (db.t3.micro)           │
              └─────────────────────────────────┘
```

## Services

| Service | Port | ALB Path | Min Tasks | Max Tasks |
|---------|------|----------|-----------|-----------|
| convertecom | 3000 | /api/* | 1 | 2 |
| convertecom-admin-master | 3000 | /admin-api/* | 1 | 2 |
| convertecom-admin-ui | 8080 | /admin/* | 1 | 2 |
| convertecom-queue-master | 8000 | /queue/* | 1 | 2 |
| convertecom-billing-service-master | 3000 | /billing/* | 1 | 2 |

## Cost Optimizations

- ✅ Single ECS cluster for all services
- ✅ Container Insights DISABLED
- ✅ No ElastiCache
- ✅ No NAT Gateway
- ✅ Max 2 tasks per service
- ✅ db.t3.micro RDS instance
- ✅ Shared ALB across services

## Prerequisites

1. AWS CLI configured
2. Terraform >= 1.0
3. Docker images pushed to ECR (for deployment)

## Quick Start

```bash
# Initialize Terraform
terraform init

# Plan infrastructure
terraform plan -var-file="environments/dev.tfvars"

# Apply infrastructure
terraform apply -var-file="environments/dev.tfvars"
```

## Environment Variables

Sensitive variables are stored in AWS Parameter Store:
- DATABASE_URL
- JWT_SECRET
- SHOPIFY_API_KEY
- SHOPIFY_API_SECRET
- SENTRY_DSN

## Module Structure

```
terraform/
├── main.tf                    # Root configuration
├── variables.tf               # Input variables
├── outputs.tf                 # Output values
├── terraform.tfvars.example   # Example variables
├── environments/              # Environment-specific configs
│   ├── dev.tfvars
│   ├── staging.tfvars
│   └── prod.tfvars
└── modules/
    ├── networking/            # VPC, subnets, security groups
    ├── ecs/                   # ECS cluster, services, tasks
    ├── alb/                   # Application Load Balancer
    ├── rds/                   # PostgreSQL database
    └── iam/                   # IAM roles and policies
```

## Estimated Monthly Cost

- ECS Fargate (5 services, 1-2 tasks): ~$25-50
- Application Load Balancer: ~$16
- RDS db.t3.micro: ~$12
- Data transfer: ~$5-10
- CloudWatch logs: ~$3-5
- **Total: ~$61-98/month**
