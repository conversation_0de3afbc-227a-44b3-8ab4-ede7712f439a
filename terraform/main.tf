terraform {
  required_version = ">= 1.0"
  
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  # Uncomment and configure for remote state
  # backend "s3" {
  #   bucket = "your-terraform-state-bucket"
  #   key    = "convertecom/terraform.tfstate"
  #   region = "us-east-1"
  # }
}

provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Project     = "ConvertEcom"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# Data sources
data "aws_availability_zones" "available" {
  state = "available"
}

# ECR Repositories
module "ecr" {
  source = "./modules/ecr"

  tags = {
    Environment = var.environment
    Project     = "convertecom"
    ManagedBy   = "terraform"
  }
}

data "aws_caller_identity" "current" {}

# Local values
locals {
  name_prefix = "${var.project_name}-${var.environment}"
  
  # Service configurations
  services = {
    convertecom = {
      name           = "convertecom"
      port           = 3000
      cpu            = 256
      memory         = 512
      path_pattern   = "/api/*"
      health_check_path = "/health"
      priority       = 300
    }
    admin-api = {
      name           = "convertecom-admin-master"
      port           = 3000
      cpu            = 256
      memory         = 512
      path_pattern   = "/admin-api/*"
      health_check_path = "/health"
      priority       = 100
    }
    admin-ui = {
      name           = "convertecom-admin-ui"
      port           = 8080
      cpu            = 256
      memory         = 512
      path_pattern   = "/admin/*"
      health_check_path = "/health"
      priority       = 200
    }
    queue = {
      name           = "convertecom-queue-master"
      port           = 8000
      cpu            = 256
      memory         = 512
      path_pattern   = "/queue/*"
      health_check_path = "/health"
      priority       = 400
    }
    billing = {
      name           = "convertecom-billing-service-master"
      port           = 3000
      cpu            = 256
      memory         = 512
      path_pattern   = "/billing/*"
      health_check_path = "/health"
      priority       = 500
    }
  }

  common_tags = {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "Terraform"
  }
}

# Networking Module
module "networking" {
  source = "./modules/networking"

  name_prefix         = local.name_prefix
  vpc_cidr           = var.vpc_cidr
  availability_zones = slice(data.aws_availability_zones.available.names, 0, 2)
  
  tags = local.common_tags
}

# IAM Module
module "iam" {
  source = "./modules/iam"

  name_prefix = local.name_prefix
  tags        = local.common_tags
}

# RDS Module
module "rds" {
  source = "./modules/rds"

  name_prefix           = local.name_prefix
  vpc_id               = module.networking.vpc_id
  private_subnet_ids   = module.networking.private_subnet_ids
  database_name        = var.database_name
  database_username    = var.database_username
  instance_class       = var.rds_instance_class
  allocated_storage    = var.rds_allocated_storage
  backup_retention_period = var.rds_backup_retention_period
  multi_az             = var.rds_multi_az
  environment          = var.environment
  log_retention_days   = var.cloudwatch_log_retention_days

  # Security
  allowed_security_group_ids = [module.networking.ecs_security_group_id]

  tags = local.common_tags
}

# ECS Module
module "ecs" {
  source = "./modules/ecs"

  name_prefix                = local.name_prefix
  services                  = local.services
  vpc_id                    = module.networking.vpc_id
  subnet_ids                = module.networking.public_subnet_ids
  security_group_ids        = [module.networking.ecs_security_group_id]

  # IAM
  task_execution_role_arn   = module.iam.ecs_task_execution_role_arn
  task_role_arn            = module.iam.ecs_task_role_arn

  # Target Groups from ALB
  target_group_arns        = module.alb.target_group_arns

  # Auto-scaling
  min_capacity             = var.ecs_min_capacity
  max_capacity             = var.ecs_max_capacity
  target_cpu_utilization   = var.ecs_target_cpu_utilization
  target_memory_utilization = var.ecs_target_memory_utilization
  scale_up_cooldown        = var.scale_up_cooldown
  scale_down_cooldown      = var.scale_down_cooldown

  # Environment
  environment              = var.environment
  ecr_repository_urls      = module.ecr.repository_urls
  database_url_parameter   = module.rds.database_url_parameter_name
  log_retention_days       = var.cloudwatch_log_retention_days

  tags = local.common_tags
}

# ALB Module
module "alb" {
  source = "./modules/alb"

  name_prefix        = local.name_prefix
  vpc_id            = module.networking.vpc_id
  subnet_ids        = module.networking.public_subnet_ids
  security_group_ids = [module.networking.alb_security_group_id]

  # Services configuration
  services          = local.services

  # SSL
  certificate_arn   = var.ssl_certificate_arn

  tags = local.common_tags
}
