output "repository_urls" {
  description = "Map of service names to their ECR repository URLs"
  value = {
    for key, repo in aws_ecr_repository.repositories : key => repo.repository_url
  }
}

output "repository_arns" {
  description = "Map of service names to their ECR repository ARNs"
  value = {
    for key, repo in aws_ecr_repository.repositories : key => repo.arn
  }
}

output "repository_names" {
  description = "Map of service names to their ECR repository names"
  value = {
    for key, repo in aws_ecr_repository.repositories : key => repo.name
  }
}

# Individual repository URLs for easy reference
output "convertecom_repository_url" {
  description = "ECR repository URL for convertecom service"
  value       = aws_ecr_repository.repositories["convertecom"].repository_url
}

output "admin_api_repository_url" {
  description = "ECR repository URL for admin API service"
  value       = aws_ecr_repository.repositories["admin-api"].repository_url
}

output "admin_ui_repository_url" {
  description = "ECR repository URL for admin UI service"
  value       = aws_ecr_repository.repositories["admin-ui"].repository_url
}

output "queue_repository_url" {
  description = "ECR repository URL for queue service"
  value       = aws_ecr_repository.repositories["queue"].repository_url
}

output "billing_repository_url" {
  description = "ECR repository URL for billing service"
  value       = aws_ecr_repository.repositories["billing"].repository_url
}
