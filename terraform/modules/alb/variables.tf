variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "vpc_id" {
  description = "ID of the VPC"
  type        = string
}

variable "subnet_ids" {
  description = "IDs of subnets for the ALB"
  type        = list(string)
}

variable "security_group_ids" {
  description = "Security group IDs for the ALB"
  type        = list(string)
}

variable "services" {
  description = "Map of services configuration"
  type = map(object({
    name              = string
    port              = number
    cpu               = number
    memory            = number
    path_pattern      = string
    health_check_path = string
    priority          = number
  }))
}

variable "target_group_arns" {
  description = "Map of target group ARNs (not used in this module, but kept for compatibility)"
  type        = map(string)
  default     = {}
}

variable "certificate_arn" {
  description = "ARN of SSL certificate for HTTPS"
  type        = string
  default     = ""
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
