# ConvertEcom Infrastructure Deployment Guide

This guide walks you through deploying the ConvertEcom infrastructure on AWS using Terraform.

## Prerequisites

### Required Tools
- [Terraform](https://www.terraform.io/downloads.html) >= 1.0
- [AWS CLI](https://aws.amazon.com/cli/) configured with appropriate credentials
- [Docker](https://www.docker.com/) for building container images
- Make (optional, for using Makefile commands)

### AWS Requirements
- AWS Account with appropriate permissions
- ECR repositories for each service
- SSL certificate in AWS Certificate Manager (for HTTPS)
- Domain name (optional)

## Quick Start

### 1. Configure AWS Credentials
```bash
aws configure
# or use environment variables
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_DEFAULT_REGION="us-east-1"
```

### 2. <PERSON><PERSON> and Setup
```bash
cd terraform
cp terraform.tfvars.example terraform.tfvars
```

### 3. Update Configuration
Edit `terraform.tfvars` with your values:
```hcl
# Update with your AWS account ID
ecr_repository_url = "************.dkr.ecr.us-east-1.amazonaws.com"

# Update with your certificate ARN (optional)
ssl_certificate_arn = "arn:aws:acm:us-east-1:************:certificate/your-cert-id"

# Update with your domain (optional)
domain_name = "your-domain.com"

# Update sensitive values
jwt_secret             = "your-secure-jwt-secret"
shopify_api_key        = "your-shopify-api-key"
shopify_api_secret     = "your-shopify-api-secret"
sentry_dsn            = "your-sentry-dsn"
new_relic_license_key = "your-new-relic-license"
```

### 4. Deploy Infrastructure

#### Using Scripts
```bash
# Initialize Terraform
./scripts/deploy.sh dev init

# Setup Parameter Store
./scripts/setup-parameter-store.sh dev

# Plan and apply
./scripts/deploy.sh dev plan
./scripts/deploy.sh dev apply
```

#### Using Makefile
```bash
# Initialize and setup development environment
make dev-init

# Deploy to development
make dev-deploy

# Check outputs
make output ENV=dev
```

## Environment-Specific Deployment

### Development
```bash
make dev-deploy
```

### Staging
```bash
make staging-deploy
```

### Production
```bash
# Plan first (always review production changes)
make prod-plan

# Apply with confirmation
make prod-apply
```

## Container Images

Before deploying, ensure your container images are built and pushed to ECR:

### 1. Create ECR Repositories
```bash
aws ecr create-repository --repository-name convertecom
aws ecr create-repository --repository-name convertecom-admin-master
aws ecr create-repository --repository-name convertecom-admin-ui
aws ecr create-repository --repository-name convertecom-queue-master
aws ecr create-repository --repository-name convertecom-billing-service-master
```

### 2. Build and Push Images
```bash
# Get ECR login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com

# Build and push each service
cd ../convertecom
docker build -t convertecom .
docker tag convertecom:latest ************.dkr.ecr.us-east-1.amazonaws.com/convertecom:latest
docker push ************.dkr.ecr.us-east-1.amazonaws.com/convertecom:latest

# Repeat for other services...
```

## SSL Certificate Setup

### 1. Request Certificate in ACM
```bash
aws acm request-certificate \
    --domain-name your-domain.com \
    --subject-alternative-names "*.your-domain.com" \
    --validation-method DNS \
    --region us-east-1
```

### 2. Validate Certificate
Follow the DNS validation process in the AWS Console.

### 3. Update terraform.tfvars
```hcl
ssl_certificate_arn = "arn:aws:acm:us-east-1:************:certificate/your-cert-id"
```

## Parameter Store Management

### View Parameters
```bash
aws ssm get-parameters-by-path --path "/convertecom/dev" --recursive --with-decryption
```

### Update Parameter
```bash
aws ssm put-parameter \
    --name "/convertecom/dev/jwt-secret" \
    --value "new-secret-value" \
    --type "SecureString" \
    --overwrite
```

## Monitoring and Troubleshooting

### Check ECS Services
```bash
aws ecs list-services --cluster convertecom-dev-cluster
aws ecs describe-services --cluster convertecom-dev-cluster --services convertecom-dev-convertecom
```

### Check ALB Health
```bash
aws elbv2 describe-target-health --target-group-arn arn:aws:elasticloadbalancing:...
```

### View Logs
```bash
aws logs describe-log-groups --log-group-name-prefix "/ecs/convertecom-dev"
aws logs tail /ecs/convertecom-dev/convertecom --follow
```

## Cost Optimization

The infrastructure is designed for cost optimization:

- **ECS Fargate**: Min 1, Max 2 tasks per service
- **RDS**: db.t3.micro instance
- **No Container Insights**: Disabled to avoid extra costs
- **No NAT Gateway**: ECS tasks in public subnets
- **Short log retention**: 7 days for dev, 30 days for prod

### Estimated Monthly Costs
- Development: ~$62-94/month
- Production: ~$80-120/month (with Multi-AZ RDS)

## Cleanup

### Destroy Environment
```bash
# Development
make dev-destroy

# Staging
make destroy ENV=staging

# Production (be very careful!)
make destroy ENV=prod
```

### Manual Cleanup
Some resources may need manual cleanup:
- ECR repositories
- Parameter Store parameters
- CloudWatch log groups (if retention is set to never expire)

## Troubleshooting

### Common Issues

1. **ECS Tasks Not Starting**
   - Check ECR image exists and is accessible
   - Verify Parameter Store parameters exist
   - Check security group rules

2. **ALB Health Checks Failing**
   - Verify health check path is correct
   - Check container port configuration
   - Ensure application responds on health check endpoint

3. **Database Connection Issues**
   - Verify RDS security group allows ECS access
   - Check database URL parameter in Parameter Store
   - Ensure database is in correct subnets

4. **SSL Certificate Issues**
   - Verify certificate is validated and issued
   - Check certificate ARN is correct
   - Ensure certificate is in the same region

### Getting Help
- Check CloudWatch logs for application errors
- Use AWS Console to inspect resources
- Review Terraform state: `terraform show`
- Check AWS CLI: `aws sts get-caller-identity`
