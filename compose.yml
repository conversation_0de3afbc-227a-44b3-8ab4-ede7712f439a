version: '3.8'

services:
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik:/etc/traefik
      - ./traefik/acme:/acme
    networks:
      - convertecom-network
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=${ACME_EMAIL}
      - --certificatesresolvers.letsencrypt.acme.storage=/acme/acme.json
      - --entrypoints.web.http.redirections.entrypoint.to=websecure
      - --entrypoints.web.http.redirections.entrypoint.scheme=https

  convertecom:
    build:
      context: ./convertecom
      dockerfile: dockerfile
    container_name: convertecom
    networks:
      - convertecom-network
    environment:
      - NODE_ENV=development
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.convertecom.rule=Host(`app.${DOMAIN}`)"
      - "traefik.http.routers.convertecom.entrypoints=websecure"
      - "traefik.http.routers.convertecom.tls.certresolver=letsencrypt"
      - "traefik.http.services.convertecom.loadbalancer.server.port=3000"

  admin-api:
    build:
      context: ./convertecom-admin-master
      dockerfile: Dockerfile
    container_name: admin-api
    networks:
      - convertecom-network
    environment:
      - NODE_ENV=development
      - PORT=3000
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.admin-api.rule=Host(`api.${DOMAIN}`)"
      - "traefik.http.routers.admin-api.entrypoints=websecure"
      - "traefik.http.routers.admin-api.tls.certresolver=letsencrypt"
      - "traefik.http.services.admin-api.loadbalancer.server.port=3000"

  admin-ui:
    build:
      context: ./convertecom-admin-ui
      dockerfile: dockerfile
    container_name: admin-ui
    networks:
      - convertecom-network
    environment:
      - NODE_ENV=development
      - API_URL=https://api.${DOMAIN}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.admin-ui.rule=Host(`dashboard.${DOMAIN}`)"
      - "traefik.http.routers.admin-ui.entrypoints=websecure"
      - "traefik.http.routers.admin-ui.tls.certresolver=letsencrypt"
      - "traefik.http.services.admin-ui.loadbalancer.server.port=8080"

  queue:
    build:
      context: ./convertecom-queue-master
      dockerfile: Dockerfile
    container_name: queue
    networks:
      - convertecom-network
    environment:
      - NODE_ENV=development
    # No Traefik labels - internal service only

  billing:
    build:
      context: ./convertecom-billing-service-master
      dockerfile: Dockerfile
    container_name: billing
    networks:
      - convertecom-network
    environment:
      - NODE_ENV=development
    # No Traefik labels - internal service only

networks:
  convertecom-network:
    driver: bridge