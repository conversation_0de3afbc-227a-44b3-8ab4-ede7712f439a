version: '3.8'

services:
  convertecom:
    build:
      context: ./convertecom
      dockerfile: dockerfile
    container_name: convertecom
    ports:
      - "3000:3000"
    networks:
      - convertecom-network
    environment:
      - NODE_ENV=development

  admin-api:
    build:
      context: ./convertecom-admin-master
      dockerfile: Dockerfile
    container_name: admin-api
    ports:
      - "3001:3000"
    networks:
      - convertecom-network
    environment:
      - NODE_ENV=development
      - PORT=3000

  admin-ui:
    build:
      context: ./convertecom-admin-ui
      dockerfile: dockerfile
    container_name: admin-ui
    ports:
      - "8080:8080"
    networks:
      - convertecom-network
    environment:
      - NODE_ENV=development
      - API_URL=http://admin-api:3000

  queue:
    build:
      context: ./convertecom-queue-master
      dockerfile: Dockerfile
    container_name: queue
    ports:
      - "8000:8000"
    networks:
      - convertecom-network
    environment:
      - NODE_ENV=development

  billing:
    build:
      context: ./convertecom-billing-service-master
      dockerfile: Dockerfile
    container_name: billing
    ports:
      - "3002:3000"
    networks:
      - convertecom-network
    environment:
      - NODE_ENV=development

networks:
  convertecom-network:
    driver: bridge