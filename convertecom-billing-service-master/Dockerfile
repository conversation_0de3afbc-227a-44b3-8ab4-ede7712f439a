# Use the official Node.js image from AWS ECR Public Gallery to avoid Docker Hub rate limits
FROM public.ecr.aws/docker/library/node:18

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json files to the container
COPY package.json package-lock.json ./

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy the rest of the application code to the container
COPY . .

# Build the TypeScript application
RUN npm run build

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application in production mode
CMD ["npm", "run", "start"]
