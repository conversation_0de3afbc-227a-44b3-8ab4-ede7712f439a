# Use the official Node.js image from AWS ECR Public Gallery to avoid Docker Hub rate limits
FROM public.ecr.aws/docker/library/node:18

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json files to the container
COPY package.json package-lock.json ./

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy the rest of the application code to the container
COPY . .

# Build the TypeScript application2025-07-29T09:30:35Z ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [api.convertecom.ai]: error: one or more domains had a problem:\n[api.convertecom.ai] acme: error: 400 :: urn:ietf:params:acme:error:connection :: *************: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-staging-v02.api.letsencrypt.org/directory acmeCA=https://acme-staging-v02.api.letsencrypt.org/directory domains=["api.convertecom.ai"] providerName=letsencrypt.acme routerName=admin-api@docker rule=Host(`api.convertecom.ai`)
2025-07-29T09:30:35Z ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [app.convertecom.ai]: error: one or more domains had a problem:\n[app.convertecom.ai] acme: error: 400 :: urn:ietf:params:acme:error:connection :: *************: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-staging-v02.api.letsencrypt.org/directory acmeCA=https://acme-staging-v02.api.letsencrypt.org/directory domains=["app.convertecom.ai"] providerName=letsencrypt.acme routerName=convertecom@docker rule=Host(`app.convertecom.ai`)
2025-07-29T09:30:37Z ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [dashboard.convertecom.ai]: error: one or more domains had a problem:\n[dashboard.convertecom.ai] acme: error: 400 :: urn:ietf:params:acme:error:connection :: *************: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-staging-v02.api.letsencrypt.org/directory acmeCA=https://acme-staging-v02.api.letsencrypt.org/directory domains=["dashboard.convertecom.ai"] providerName=letsencrypt.acme routerName=admin-ui@docker rule=Host(`dashboard.convertecom.ai`)
2025-07-29T09:30:52Z ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [dashboard.convertecom.ai]: error: one or more domains had a problem:\n[dashboard.convertecom.ai] acme: error: 400 :: urn:ietf:params:acme:error:connection :: *************: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-staging-v02.api.letsencrypt.org/directory acmeCA=https://acme-staging-v02.api.letsencrypt.org/directory domains=["dashboard.convertecom.ai"] providerName=letsencrypt.acme routerName=admin-ui@docker rule=Host(`dashboard.convertecom.ai`)
2025-07-29T09:30:56Z ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [api.convertecom.ai]: error: one or more domains had a problem:\n[api.convertecom.ai] acme: error: 400 :: urn:ietf:params:acme:error:connection :: *************: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-staging-v02.api.letsencrypt.org/directory acmeCA=https://acme-staging-v02.api.letsencrypt.org/directory domains=["api.convertecom.ai"] providerName=letsencrypt.acme routerName=admin-api@docker rule=Host(`api.convertecom.ai`)
2025-07-29T09:30:58Z ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [app.convertecom.ai]: error: one or more domains had a problem:\n[app.convertecom.ai] acme: error: 400 :: urn:ietf:params:acme:error:connection :: *************: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-staging-v02.api.letsencrypt.org/directory acmeCA=https://acme-staging-v02.api.letsencrypt.org/directory domains=["app.convertecom.ai"] providerName=letsencrypt.acme routerName=convertecom@docker rule=Host(`app.convertecom.ai`)
2025-07-29T09:31:06Z ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [dashboard.convertecom.ai]: error: one or more domains had a problem:\n[dashboard.convertecom.ai] acme: error: 400 :: urn:ietf:params:acme:error:connection :: *************: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-staging-v02.api.letsencrypt.org/directory acmeCA=https://acme-staging-v02.api.letsencrypt.org/directory domains=["dashboard.convertecom.ai"] providerName=letsencrypt.acme routerName=admin-ui@docker rule=Host(`dashboard.convertecom.ai`)
2025-07-29T09:31:08Z ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [api.convertecom.ai]: error: one or more domains had a problem:\n[api.convertecom.ai] acme: error: 400 :: urn:ietf:params:acme:error:connection :: *************: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-staging-v02.api.letsencrypt.org/directory acmeCA=https://acme-staging-v02.api.letsencrypt.org/directory domains=["api.convertecom.ai"] providerName=letsencrypt.acme routerName=admin-api@docker rule=Host(`api.convertecom.ai`)
2025-07-29T09:31:17Z ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [app.convertecom.ai]: error: one or more domains had a problem:\n[app.convertecom.ai] acme: error: 400 :: urn:ietf:params:acme:error:connection :: *************: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-staging-v02.api.letsencrypt.org/directory acmeCA=https://acme-staging-v02.api.letsencrypt.org/directory domains=["app.convertecom.ai"] providerName=letsencrypt.acme routerName=convertecom@docker rule=Host(`app.convertecom.ai`)
2025-07-29T09:31:18Z ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [dashboard.convertecom.ai]: error: one or more domains had a problem:\n[dashboard.convertecom.ai] acme: error: 400 :: urn:ietf:params:acme:error:connection :: *************: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-staging-v02.api.letsencrypt.org/directory acmeCA=https://acme-staging-v02.api.letsencrypt.org/directory domains=["dashboard.convertecom.ai"] providerName=letsencrypt.acme routerName=admin-ui@docker rule=Host(`dashboard.convertecom.ai`)
RUN npm run build

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application in production mode
CMD ["npm", "run", "start"]
