import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  ManyToOne,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { Tenant } from '../tenant/tenant.entity';
import { BillingLog } from './billing-log.entity';

@Entity()
export class Billing {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(type => Tenant, tenant => tenant.billing)
  tenant: Tenant;

  @Column()
  date: string;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
  })
  amount: number;

  // In case the percentage changes at some point, we have a record of what it was
  @Column({
    type: 'decimal',
    precision: 3,
    scale: 2,
  })
  percentage: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
  })
  subtotalSum: number;

  @OneToOne(type => BillingLog)
  @JoinColumn()
  billingLog: BillingLog;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
