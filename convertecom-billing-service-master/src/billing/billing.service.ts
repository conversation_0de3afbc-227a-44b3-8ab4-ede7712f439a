import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Billing } from './billing.entity';
import { Repository } from 'typeorm';
import { Moment } from 'moment';
import * as moment from 'moment';
import { Order } from '../order/order.entity';
import { Tenant } from '../tenant/tenant.entity';
import { first } from 'lodash';

@Injectable()
export class BillingService {
  constructor(
    @InjectRepository(Billing)
    private readonly billingRepository: Repository<Billing>,
    @InjectRepository(Tenant)
    private readonly tenantRepository: Repository<Tenant>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
  ) {
  }

  async createPendingBill(data: Partial<Billing>): Promise<Billing> {
    try {
      return await this.billingRepository.save(data);
    } catch (e) {
      if (e.routine === '_bt_check_unique') {
        // Ignore unique constraint failure, because that means the bill already exists
        Logger.log(
          `Failed to create a bill for tenant "${data.tenant.id}" date "${data.date}" because it already exists.`,
        );
      } else {
        // Rethrow
        throw e;
      }
    }
  }

  async calculateBillingForDate(date: Moment): Promise<void> {
    const startDate = date.format('YYYY-MM-DD 00:00:00');
    const endDate = date.format('YYYY-MM-DD 23:59:59');

    // Get all Shopify tenants
    const tenants = await this.tenantRepository.find({
      where: {platform: 'shopify'},
    });

    for (const tenant of tenants) {
      try {
        const trialEndsOn = moment(tenant.trialEndsOn);
        if (tenant.trialEndsOn && trialEndsOn.isSameOrAfter(date, 'day')) {
          Logger.log(`Tenant ${tenant.id} has a trial ending on ${tenant.trialEndsOn}, skipping for date ${endDate.toString()}`);
          continue;
        }
      } catch (e) {
        Logger.error(`Error fetching trial status for tenant ${tenant.id}`);
      }
      Logger.log(
        `Calculating subtotal sum for tenant "${tenant.id}" for date "${startDate}" - "${endDate}"`,
      );
      const subtotalSum = await this.calculateDailySubtotalSumForTenant(
        tenant,
        startDate,
        endDate,
      );
      const amount = subtotalSum * tenant.billingPercentage;
      await this.createPendingBill({
        tenant,
        date: date.format('YYYY-MM-DD'),
        subtotalSum,
        amount,
        percentage: tenant.billingPercentage,
      });
    }
  }

  private async calculateDailySubtotalSumForTenant(
    tenant: Tenant,
    startDate: string,
    endDate: string,
  ): Promise<number> {
    const results = await this.orderRepository.query(
      `
      SELECT SUM(o."subtotalPrice") AS "subtotalSum"
      FROM "order" o
      JOIN "coupon_redemption_orders_order" x ON x."orderId" = o.id
      JOIN "coupon" c on c.id = x."couponId"
      WHERE
      o."tenantId" = $1
      AND o."createdAt" BETWEEN $2 AND $3
    `,
      [tenant.id, startDate, endDate],
    );

    return first(results).subtotalSum || 0;
  }
}
