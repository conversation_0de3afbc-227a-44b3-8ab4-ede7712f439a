import { Billing } from '../billing.entity';
import { getMockTenant } from '../../tenant/mock/entity';

const mockDate = new Date('2019-06-24T19:54:30.671Z');

export function getMockBilling(): Billing {
  return {
    id: 'id',
    tenant: getMockTenant(),
    date: '2019-06-24',
    amount: 1,
    percentage: 0.01,
    subtotalSum: 100,
    billingLog: null,
    createdAt: mockDate,
    updatedAt: mockDate,
  };
}
