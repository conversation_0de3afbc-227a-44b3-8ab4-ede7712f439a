import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BillingService } from './billing.service';
import { Billing } from './billing.entity';
import { Tenant } from '../tenant/tenant.entity';
import { Order } from '../order/order.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Billing, Tenant, Order])],
  providers: [BillingService],
  exports: [BillingService],
})
export class BillingModule {}
