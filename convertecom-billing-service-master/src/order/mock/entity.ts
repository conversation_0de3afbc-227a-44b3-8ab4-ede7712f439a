import { Order } from '../order.entity';
import { getMockTenant } from '../../tenant/mock/entity';

const mockDate = new Date('2019-06-24T19:54:30.671Z');

export function getMockOrder(): Order {
  return {
    id: 'id',
    tenant: getMockTenant(),
    externalOrderId: 'id',
    externalCustomerId: 'id',
    redeemedCoupons: [],
    totalPrice: 100,
    totalDiscounts: 5,
    subtotalPrice: 95,
    totalTax: 0,
    zingDiscounts: 0,
    externalCreatedAt: null,
    externalPaymentAt: null,
    createdAt: mockDate,
    updatedAt: mockDate,
  };
}
