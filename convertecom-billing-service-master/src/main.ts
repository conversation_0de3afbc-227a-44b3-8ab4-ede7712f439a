import { config } from 'dotenv';
config();

import { NestFactory } from '@nestjs/core';
import * as Sentry from '@sentry/node';
import { RavenInterceptor } from 'nest-raven';
import { AppModule } from './app.module';
import { HttpException } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  if (process.env.SENTRY_DSN) {
    Sentry.init({ dsn: process.env.SENTRY_DSN });
    app.use(Sentry.Handlers.requestHandler());
    app.useGlobalInterceptors(
      new RavenInterceptor({
        filters: [
          {
            type: HttpException,
            filter: (exception: HttpException) => 500 > exception.getStatus(),
          },
        ],
      }),
    );
  }

  await app.listen(process.env.PORT || 3000);
}
bootstrap();
