import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  getHealthCheck() {
    return { status: 'ok', service: 'convertecom-billing-service-master', timestamp: new Date().toISOString() };
  }
}
