import { Injectable, Logger } from '@nestjs/common';
import { NestSchedule, Cron } from 'nest-schedule';
import * as moment from 'moment';
import { BillingService } from '../billing/billing.service';

@Injectable()
export class BillingJobRunnerService extends NestSchedule {
  billingService: BillingService;

  constructor(billingService: BillingService) {
    super();
    this.billingService = billingService;
  }

  @Cron('11 14 * * *') // Run at 2:11 PM (2 minutes from now for testing)
  async calculateTenantBilling() {
    try {
      const yesterday = moment().subtract(1, 'day');
      Logger.log(
        `Calculating tenant billing for date "${yesterday.format(
          'YYYY-MM-DD',
        )}"`,
      );
      await this.billingService.calculateBillingForDate(yesterday);
      Logger.log('Done');
    } catch (e) {
      // TODO send high-priority error to Sentry
      Logger.error('Failed to calculate tenant billing');
      Logger.error(e);
    }
  }
}
